<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="true" label-width="80px">
      <el-form-item label="项目名称" prop="name">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.name"
          placeholder="项目名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.sectionName"
          placeholder="标段名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <my-select
          id="type"
          pvalue="projectType"
          v-model="queryParams.type"
          placeholder="项目类型"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row v-show="false" :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd('project')"
          v-hasPermi="['project:project:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="()=>handleUpdate({},'project')"
          v-hasPermi="['project:project:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:project:delete']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:project:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload(null, 'project')"></right-toolbar>
    </el-row>

    <my-table
      url="/project/project/page"
      ref="projectTable"
      row-key="deptId"
      :fixed="true"
      cell-class-name="el-table-max-cell2"
      @my-selection-change="handleSelectionChange"
      @my-sort-change ="handleSortChange"
    >
      <el-table-column
        label="项目名称"
        fixed="left"
        header-align="center"
        align="left"
        min-width="250"
        prop="name"
        sortable="custom"
        column-key="project.NAME">
        <template #default="scope">
          <el-tooltip :content="scope.row.name" placement="top" effect="light">
            <div>{{ scope.row.name }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="标段名称"
        align="left"
        header-align="center"
        min-width="160"
        prop="sectionName"
        sortable="custom"
        column-key="project.SECTION_NAME">
      </el-table-column>
      <el-table-column
        label="项目位置"
        align="left"
        header-align="center"
        min-width="160"
        prop="areaName"
        sortable="custom"
        column-key="project.AREA_NAME">
      </el-table-column>
      <el-table-column
        label="项目类型"
        align="center"
        header-align="center"
        min-width="160"
        prop="type"
        sortable="custom"
        column-key="project.TYPE">
        <template slot-scope="scope">
          <my-view pvalue="projectType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column
        label="采砂人（供砂人）"
        header-align="center"
        align="center"
        min-width="150"
        prop="leaderName"
        sortable="custom"
        column-key="project.LEADER_NAME">
      </el-table-column>
      <el-table-column
        label="联系电话"
        align="center"
        header-align="center"
        min-width="120"
        prop="contact"
        sortable="custom"
        column-key="project.CONTACT">
      </el-table-column>
      <el-table-column
        label="监管部门"
        align="left"
        header-align="center"
        min-width="150"
        prop="deptName"
        sortable="custom"
        column-key="dept.NAME">
      </el-table-column>
      <el-table-column
        label="控制总量(万吨)"
        align="right"
        header-align="center"
        min-width="150"
        prop="totalYield"
        sortable="custom"
        column-key="project.TOTAL_YIELD">
        <template #default="scope">
          {{common.toThousands(scope.row.totalYield,2,',')}}
        </template>
      </el-table-column>
      <el-table-column
        label="采砂许可证/弃砂审批文号"
        align="center"
        header-align="center"
        min-width="200"
        prop="licenseNo"
        sortable="custom"
        column-key="project.LICENSE_NO">
      </el-table-column>
      <el-table-column
        label="操作"
        min-width="200"
        header-align="center"
        column-key="caozuo"
        fixed="right"
        align="center">
        <template slot-scope="scope">
<!--          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-tickets"
            title="查看附件"
            @click="handleFileList(scope.row)"
            v-hasPermi="['project:project:viewFile']"
          ></el-button>-->
          <el-button
            size="small"
            type="success"
            class="btn-table-operate iconfont el-icon-smm-keshihuajianguan"
            title="项目监管"
            @click="handleProjectSupervision(scope.row)"
            v-hasPermi="['project:project:info']"
          ></el-button>

        </template>
      </el-table-column>
    </my-table>

  </div>
</template>

<script>
import {
  getProject,
  delProject,
  delProjectBatch,
  addProject,
  updateProject,
  delProjectFile,
  getProjectConfig,
  updateProjectConfig,
  addProjectConfig,
  getProjectEncryptedString, getProjectWxCode
} from '@/api/project/project'
import {
  addWeighingStation,
  delWeighingStation,
  getWeighingStation,
  updateWeighingStation
} from "@/api/project/weighingStation";
import MyAreaSelect from "@/components/YB/MyAreaSelect";
import MyCascader from "@/components/YB/MyCascader";
import MyFileUpload from "@/components/YB/MyFileUpload";
import MyPdfReader from "@/components/YB/MyPdfReader";
import MyTencentMap from "@/components/YB/MyTencentMap";
import QRCode from "qrcodejs2-fix";
import { getPartnerAllList } from '@/api/open/partner'

import InputCoords from '@/views/components/input-coords/index.vue'
import GeoPlotter from "@/views/components/plotter/index.vue"
import * as XLSX from 'xlsx';

import L from 'leaflet';
import common from '../../../utils/common'

export default {
  name: "Project",
  computed: {
    common() {
      return common
    }
  },
  components: {
    MyAreaSelect: MyAreaSelect,
    MyCascader: MyCascader,
    MyFileUpload: MyFileUpload,
    'el-image-viewer': () => import("element-ui/packages/image/src/image-viewer"),
    MyPdfReader: MyPdfReader,
    MyTencentMap: MyTencentMap,

    InputCoords,
    GeoPlotter,

  },
  data() {
    return {
      // 项目列表-选中数组
      deptIds: [],
      // 项目列表-非单个禁用
      single: true,
      // 项目列表-非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: '',
        areaCode: '',
        type: '',
        sectionName: '',
      },
      // 表单参数
      project: {},
      // 表单状态
      mode: '',
      // 文件类型  常用文件类型：.txt,.doc,.docx,.pdf,.xls,.xlsx,.bmp,.gif,.jpeg,.png,.jpg,.zip,.rar,.7z,.tar
      fileType: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
      fileType1: '.bmp,.gif,.jpg,.jpeg,.png',  //印章图片类型
      //印章图片
      imgList: [],
      dialogVisible: false,
      dialogImageUrl: "",
      // 上传类型
      pathFieldName: 'project-file',
      pathFieldName1: 'seal-image-img',
      // 是否显示附件列表弹出层
      openFileList: false,
      // 数据主键
      deptId: '',
      // 是否显示预览图片弹出层
      imageVisible: false,
      // 图片路径
      imageUrl: '',
      // 是否显示预览pdf弹出层
      openViewPdf: false,
      // pdf文件路径
      pdfUrl: '',
      // 是否显示磅站管理列表弹出层
      openWeighingStation: false,
      // 磅站列表-选中数组
      ids: [],
      // 磅站列表-非单个禁用
      single1: true,
      // 磅站列表-非多个禁用
      multiple1: true,
      // 是否显示新增磅站弹出层
      openAddWeighingStation: false,
      // 磅站表单参数
      weighingStation: {},
      // 新增/修改磅站弹出层标题
      weighingStationTitle: '',
      // 是否显示项目配置弹出层
      projectConfig: false,
      // 项目配置相关字段
      projectConfigForm: {
        deptId: '',
        leaderAuthDuration: null,
        supervisorAuthDuration: null,
        authorityAuthDuration: null,
        useOpenApi:null,
        partnerId:'',
      },
      // 项目配置表单状态
      configMode: '',
      // 项目二维码弹出层
      openQrCode: false,
      openWxQrCode: false,
      // 项目二维码弹出层-项目名称
      qrCodeProjectName: '',
      partnerList:'',//系统名称
      wxQrCode:'',

      // 存储 my-form-item 组件的数组
      formItems: [
        {
          formItemsId: `formItem_0`, // my-form-item唯一 ID
          labelNum: 0,
          label: `可采区K0`, // my-form-item标签
          ref: "formItem_0", // my-form-item ref
          listCoord: [
            {
              ref: "formItem_0_coord_0", id: 'formItem_0_0', visible: true
            }
          ], // 当前my-form-item中的坐标列表
          visible: true
        }
      ],
      trueID: 0, //formItem顺序ID
      showID: 0, //label 显示的ID


      choiceFromItemIndex: null, // 当前选中的 my-form-item 的索引

      mapDialogs: {
        "road": null,
        "pipe": null,
        "wire": null,
        "park": null,
      },
      readOnly : true,

      //上传坐标点参数
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        action: '',
      },
      selectedFile: null,
      coordinates: [], // 存储经纬度坐标
    };
  },
  mounted() {

  },
  watch: {

  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.projectTable.changeTableHeight();
  },

  methods: {
    validatorImg(rule, value, callback){
      if (this.project.fileList == null || this.project.fileList.length<= 0 ) {
        callback(new Error("请上传印章图片"));
      } else {
        callback();
      }
    },
    /** 查询项目列表 */
    reload(restart, params) {
      if (params === 'project') {
        this.$refs.projectTable.search(this.queryParams, restart);
      } else {
        this.$refs.weighingStationTable.search({}, restart);
      }
      this.single = true;
      this.multiple = true;
    },
    /** 取消按钮 */
    cancel(params) {
      if (params === 'project') {
        this.open = false;
      } else if (params === 'weighingStation') {
        this.openAddWeighingStation = false;
      } else {
        this.projectConfig = false;
      }
      this.reset(params);
    },
    /** 表单重置 */
    reset(params) {
      if (params === 'project') {
        this.project = {
          deptId: '',
          deptCode: '',
          name: '',
          sectionName:'',
          areaName: '',
          areaCode: '',
          type: '',
          parentId: '',
          totalYield: '' ,
          licenseNo: '',
          leaderName: '',
          contact: '',
          uploadFileList: [],
          fileList:[]
        };
        this.listCoord = [
          { ref: "coord_0", id: 0, visible: true },
        ];
      } else if(params === 'weighingStation')  {
        this.weighingStation = {
          id: '',
          projectId: '',
          name: '',
          longitude: '',
          latitude: '',
          address: '',
          searchValue: '',
        };
      } else {
        this.projectConfigForm = {
          deptId: '',
          leaderAuthDuration: null,
          supervisorAuthDuration: null,
          authorityAuthDuration: null,
          useClient: false
        }
      }
      this.resetForm(params);
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true,'project');
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 项目列表-多选框选中数据 */
    handleSelectionChange() {
      this.deptIds = this.$refs.projectTable.getSelectRowKeys();
      this.single = this.deptIds.length!==1;
      this.multiple = !this.deptIds.length;
    },

    /** 新增按钮操作 */
    handleAdd(params) {
      this.readOnly = false;
      this.reset(params);
      if (params === 'project') {
        this.open = true;
        this.mode = 'add';
        this.title = "添加项目";
        this.imgList = [];
      } else {
        this.openAddWeighingStation = true;
        this.weighingStation.projectId = this.deptId;
        this.weighingStationTitle = '添加磅站';
      }
    },

    /** 修改按钮操作 */
    handleUpdate(row, params) {
      this.readOnly = false;
      this.reset(params);
      if (params === 'project') {
        const deptId = row.deptId || this.deptIds[0];
        getProject(deptId).then(r => {
          this.project = r.project;
          this.open = true;
          this.mode = 'update';
          this.title = "修改项目";
          this.fromCoords(this.project.coords);
          this.handelFileList(r)
        });
      } else {
        const id = row.id || this.ids[0];
        getWeighingStation(id).then(r => {
          this.weighingStation = r.weighingStation;
          this.openAddWeighingStation = true;
          this.weighingStationTitle = '修改磅站';
        });
      }
    },
    /** 查看按钮操作 */
    handleProjectSupervision(row){
      const deptId = row.deptId || this.deptIds[0];
      console.log('deptId---------'+deptId)
      console.log('----'+process.env.VUE_APP_PROJECT_ID)
      let configIds=process.env.VUE_APP_PROJECT_ID;
      // if (configIds.indexOf(deptId) == -1) {
      //   this.$modal.msgError("该项目未对接北斗设备");
      //   return;
      // }
      //原窗口打开
      // this.$router.push({path: '/projectSupervision/detail/' + deptId})
      //新窗口打开
      window.open("/projectSupervision/detail/" + deptId);

    },

    /** 提交按钮 */
    submitForm(params) {
      if (params === 'project') {
        this.$refs["project"].validate((valid, errorObj) => {
          if (valid) {
            //采区范围坐标合法判断
            let coordsCheckMsg = this.checkCoords(-1);
            if (coordsCheckMsg != "") {
              this.$modal.msgError(coordsCheckMsg);
              return;
            }
            let coordsCurrent = this.toCoords(-1);
            console.log("submitForm++++++++++++++:coordsCurrent:" + coordsCurrent.coordsGeojson)
            this.project.coords = coordsCurrent.coordsString;
            this.project.geoJson = coordsCurrent.coordsGeojson;
            if (this.project.deptId) {
              updateProject(this.project).then(res => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.reload(false,'project');
              });
            } else {
              addProject(this.project).then(res => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.reload(false,'project');
              });
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      } else if(params === 'weighingStation') {
        this.$refs["weighingStation"].validate((valid, errorObj) => {
          if (valid) {
            if (this.weighingStation.id) {
              updateWeighingStation(this.weighingStation).then(res => {
                this.$modal.msgSuccess("修改成功");
                this.openAddWeighingStation = false;
                this.reload(false,'weighingStation');
              });
            } else {
              addWeighingStation(this.weighingStation).then(res => {
                this.$modal.msgSuccess("新增成功");
                this.openAddWeighingStation = false;
                this.reload(false,'weighingStation');
              });
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      } else {
        if (this.configMode==='update') {
          if (!this.projectConfigForm.useOpenApi){
            this.projectConfigForm.partnerId = ''
          }
          updateProjectConfig(this.projectConfigForm).then(res => {
            this.$modal.msgSuccess("修改成功");
            this.projectConfig = false;
          });
        } else {
          if (!this.projectConfigForm.useOpenApi) {
            this.projectConfigForm.partnerId = ''
          }
          addProjectConfig(this.projectConfigForm).then(res => {
            this.$modal.msgSuccess("新增成功");
            this.projectConfig = false;
          });
        }
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(()=> {
        if (row.deptId) {
          return delProject(row.deptId);
        } else {
          return delProjectBatch(this.deptIds);
        }
      }).then(() => {
        this.reload(false, 'project');
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    handleSortChange(opt,obj){
      this.queryParams.sidx = obj.sidx
      this.queryParams.order = obj.order
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/project/project/export',
        {...this.queryParams},
        `项目管理_${new Date().getTime()}.xlsx`, 'application/json');
    },

    /** 项目位置change事件 获取areaName */
    getAreaName(areaName) {
      this.project.areaName = areaName;
      if (this.mode==='add') {
        if (this.project.areaCode.length >= 15) {
          const deptObj = this.$refs.deptCascader.deptList.find(item => {
            return item.areaCode === this.project.areaCode;
          });
          this.project.parentId = deptObj ? deptObj.deptId : '';
        } else {
          this.project.parentId = '';
        }
      }
    },

    /** 获取fileList */
    getFileList(fileList) {
      this.project.uploadFileList = fileList;
    },
    getFileList1(fileList) {
      this.project.fileList = fileList
    },

    /** 查看附件列表 */
    handleFileList(row) {
      this.deptId = row.deptId;
      this.openFileList = true;
    },

    /** 预览附件操作 */
    handleView(row) {
      if (row.uploadContentType.indexOf('image') >= 0) {
        this.imageVisible = true;
        this.imageUrl = row.uploadFilePath;
      } else if (row.uploadContentType.indexOf("pdf") >= 0) {
        this.openViewPdf = true;
        this.pdfUrl =  row.uploadFilePath;
      }
    },

    /** 下载附件操作 */
    handleDownloadFile(row) {
      this.download("/com/uploadfile/download/" + row.id, {}, row.uploadFileName);
    },

    /** 删除附件操作 */
    handleDeleteFile(row) {
      this.$modal.confirm('是否确认删除此附件？').then(() => {
        return delProjectFile(row.id);
      }).then(() => {
        this.$refs.fileTable.search();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 判断文件格式 是否可以预览 */
    isPreview(row) {
      return row.uploadContentType.indexOf('image') >= 0 || row.uploadContentType.indexOf("pdf") >= 0;
    },

    /** 磅站管理操作 */
    handleWeighingStation(row) {
      this.deptId = row.deptId;
      this.openWeighingStation = true;
    },

    /** 正则验证经度输入 */
    validateLng(rule, value, callback) {
      var that = this;
      return new Promise((resolve, reject) => {
        if (that.weighingStation.longitude) {
          if ((Number(that.weighingStation.longitude) >= 112) && (Number(that.weighingStation.longitude) <= 121)) {
            resolve();
          } else {
            reject();
          }
        } else {
          resolve();
        }
      });
    },

    /** 正则验证纬度输入 */
    validateLat(rule, value, callback) {
      var that = this;
      return new Promise((resolve, reject) => {
        if (that.weighingStation.latitude) {
          if ((Number(that.weighingStation.latitude) >= 35) && (Number(that.weighingStation.latitude) <= 44)) {
            resolve();
          } else {
            reject();
          }
        } else {
          resolve();
        }
      });
    },

    /** 获取选中点地址信息 */
    getSelectPosition(data) {
      this.weighingStation.longitude = data.longitude;
      this.weighingStation.latitude = data.latitude;
      this.weighingStation.address = data.address;
    },

    /** 磅站列表-多选框选中数据 */
    handleSelectionChange1() {
      this.ids = this.$refs.weighingStationTable.getSelectRowKeys();
      this.single1 = this.ids.length!==1;
      this.multiple1 = !this.ids.length;
    },

    /** 删除磅站操作 */
    handleDelWeighingStation(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(()=> {
        return delWeighingStation(row.id);
      }).then(() => {
        this.reload(false, 'weighingStation');
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 项目配置按钮操作 */
    handleProjectConfig(row) {
      this.projectConfig = true;
      getProjectConfig(row.deptId).then(r => {
        if (r.projectConfig) {
          this.projectConfigForm = r.projectConfig;
          this.configMode = 'update';
        } else {
          this.projectConfigForm.deptId = row.deptId;
          this.configMode = 'add';
        }
      });
      getPartnerAllList().then(res=>{
        this.partnerList = res.list
      })
    },

    /** 项目二维码按钮操作 */
    handleProjectCode(row) {
      getProjectEncryptedString(row.deptId).then(r=> {
        if (r.value) {
          this.openQrCode = true;
          this.qrCodeProjectName = row.name;
          this.$nextTick(() => {
            new QRCode(this.$refs.qrCodeDiv, {
              text: r.value,
              width: 280,
              height: 280,
              colorDark: '#333333', //二维码颜色
              colorLight: '#ffffff', //二维码背景色
              correctLevel: QRCode.CorrectLevel.L //容错率 L/M/H
            });
          });
        }
      });
    },
    handleProjectWxCode(row){
      getProjectWxCode(row.deptId).then(r=>{
        if (row.sectionName){
          this.qrCodeProjectName = row.name +'('+row.sectionName+')'
        }else {
          this.qrCodeProjectName = row.name;
        }
        this.openWxQrCode = true;
        this.wxQrCode = r.uploadFile
      })
    },
    handelFileList(r){
      console.log(r.project.fileList)
      if (r.project.fileList && r.project.fileList.length > 0) {
        this.imgList = r.project.fileList.map(item => {
          return {
            name: item.uploadFileName,
            url: item.uploadFilePath,
            uploadFile: item,
          }
        });
      } else {
        this.imgList = [];
      }
    },

    //添加可采区组件方法
    onClickAddFormItem() {
      console.log("新增前this.trueID:------- " + this.trueID)
      this.trueID++
      this.showID++
      // 添加新的 my-form-item 组件
      this.formItems.push({
        formItemsId: `formItem_${this.trueID}`, // 生成唯一 ID
        labelNum: this.showID,
        label: `可采区K${this.showID}`, // 可以自定义标签
        ref: `formItem_${this.trueID}`,
        listCoord: [
          {
            ref: `formItem_${this.trueID}_coord_0`,
            id: `formItem_${this.trueID}_0`,
            visible: true
          }
        ], // 初始化坐标列表
        visible: true
      });
      //nextId接续
      console.log("新增后this.trueID:------- " + this.trueID)
    },

    // 添加删除可采区组件方法
    onClickDeleteFormItem(index) {
      let num = this.formItems[index].labelNum;
      // 删除指定的formItem
      this.formItems[index].visible = false;
      // 更新所有的formItem的label标签值
      for (let i = index + 1; i < this.formItems.length; i++) {
        if(this.formItems[i].visible){
          this.formItems[i].labelNum = num;
          this.formItems[i].label = "可采区K" + num;
          num++;
        }
      }
      this.showID--
      console.log("Remaining form items: ", this.formItems);
    },

    //添加采区坐标范围方法
    onClickMinusCoordInput(index, coordIndex) {
      this.formItems[index].listCoord[coordIndex].visible = false;
      // this.listCoord[k].visible = false;
    },
    onClickPlusCoordInput(formItemIndex) {
      let n = this.formItems[formItemIndex].listCoord.length;
      // let n = this.listCoord.length;
      this.formItems[formItemIndex].listCoord.push({
        ref: "formItem_" + formItemIndex + "_coord_" + n, id: "formItem_" + formItemIndex + "_" + n, visible: true,
      });
      this.$nextTick(() => {
        // let el0 = this.$refs[this.listCoord[0].ref][0];
        // let el = this.$refs[this.listCoord[n].ref][0];
        let el0 = this.$refs[this.formItems[formItemIndex].listCoord[0].ref][0];
        let el = this.$refs[this.formItems[formItemIndex].listCoord[n].ref][0];
        el.mode = el0.mode;
        el.mc.centralMeridian = el0.mc.centralMeridian;
      });
    },
    onSelectCoordInput(m, cm) {
      for (let i = 0; i < this.listCoord.length; i++) {
        let el = this.$refs[this.listCoord[i].ref][0];
        el.mode = m;
        el.mc.centralMeridian = cm;
      }
    },
    onClickPlot(formItemIndex){
      this.choiceFromItemIndex = formItemIndex;
      let caller = {};
      let coordsCheckMsg = this.checkCoords(formItemIndex);
      console.log("onClickPlot-coordsCheckMsg:" + coordsCheckMsg)
      if (coordsCheckMsg != "") {
        caller.msg = coordsCheckMsg + "；从头开始绘制";
        caller.geojson = null;
        caller.typeAllowed = 30;
        this.onClickMap(caller);
        return;
      }
      let coordsCurrent = this.toCoords(formItemIndex);
      console.log("onClickPlot-coordsCurrent:" + coordsCurrent)
      caller.typeAllowed = 30;
      caller.geojson = coordsCurrent.coordsGeojson;
      caller.msg = "检查到已有坐标信息，可修改或重新绘制";
      this.onClickMap(caller);
    },
    checkCoords(formItemIndex){
      if(formItemIndex != -1){
        //单独验证一个formItem(打开地图绘制时)
        let n = this.formItems[formItemIndex].listCoord.length;
        let vv = [];
        for (let i = 0; i < n; i++) {
          let visible = this.formItems[formItemIndex].listCoord[i].visible;
          if (visible) {
            let el = this.$refs[this.formItems[formItemIndex].listCoord[i].ref][0];
            let v = el.getValue();
            if (v.msg != "OK") {
              return ("第" +(vv.length+1) + "个拐点坐标：" +v.msg)
            } else {
              vv.push(v);
            }
          }
        }
        let k = vv.length;
        if (k < 1) {
          return (this.formItems[formItemIndex].label  + "尚未填写坐标");
        }
      }else{
        //多个formItem情况下每个listCoord都需要验证(提交结果时)
        this.formItems.forEach(item=>{
          console.log("+++++++进入到坐标验证循环++++++++++")
          if(item.visible){
            console.log("验证列表：" + item.label)
            let n = item.listCoord.length;
            let vv = [];
            for (let i = 0; i < n; i++) {
              let visible = item.listCoord[i].visible;
              if (visible) {
                console.log("checkCoords,每一个coords组件引用：" + this.$refs[item.listCoord[i].ref])
                let el = this.$refs[item.listCoord[i].ref][0];
                let v = el.getValue();
                if (v.msg != "OK") {
                  return ("第" + (vv.length+1) + "个拐点坐标：" + v.msg);
                } else {
                  vv.push(v);
                }
              }
            }
            let k = vv.length;
            if (k < 1) {
              return (item.label  + "尚未填写坐标");
            }
          }
        })
      }
      return "";
    },
    toCoords(formItemIndex){
      console.log("toCoords-formItemIndex:" + formItemIndex)
      if(formItemIndex != -1){
        // 单独一个formItem中的coord坐标转换(地图绘制)
        let temp = this.formItems[formItemIndex].listCoord
        console.log("toCoords-temp:" + temp)
        let n  = temp.length;
        let vv = [];
        for (let i = 0; i < n; i++) {
          let visible = temp[i].visible;
          if (visible) {
            let el = this.$refs[temp[i].ref][0];
            let v = el.getValue();
            if (v.msg != "OK") {
              return null;
            } else {
              vv.push(v);
            }
          }
        }
        let k = vv.length;
        if (k < 1) {
          return null;
        }
        return this.generateCoordsAndGeojson(k, vv);
      }else{
        // 多个formItem中的listCoord转换(提交)
        let multiLatLngsArray = [];//多区域坐标组
        let result = {}; //存储全部区域的coordsString和coordsGeojson
        this.formItems.forEach(item=>{
          if(item.visible){
            let n  = item.listCoord.length;
            let vv = [];
            //循环读取当前formItem中的coord
            for (let i = 0; i < n; i++) {
              let visible = item.listCoord[i].visible;
              if (visible) {
                let el = this.$refs[item.listCoord[i].ref][0];
                let v = el.getValue();
                if (v.msg != "OK") {
                  return null;
                } else {
                  vv.push(v);
                }
              }
            }
            let k = vv.length;
            if (k < 1) {
              return null;
            }
            let latLngsArray = [];//单区域坐标组
            for (let i = 0; i < k; i++) {
              latLngsArray.push(L.latLng(vv[i].lat,vv[i].lng));
            }
            latLngsArray.push(L.latLng(vv[0].lat,vv[0].lng));
            multiLatLngsArray.push(latLngsArray)
          }
        })
        result.coordsString = '';
        result.coordsGeojson = this.convertToGeoJSON(multiLatLngsArray);
        return result;
      }
    },

    // 使用 Leaflet 的 latLngsToCoords 方法转换为 GeoJSON 格式
    convertToGeoJSON(multiLatLngsArray) {
      const multiPolygon = {
        type: "MultiPolygon",
        // coordinates: multiLatLngsArray.map(latLngs => {
        //   // 使用 latLngsToCoords 方法进行转换
        //   return L.latLngsToCoords(latLngs, 0, true); // levelsDeep=0, closed=true
        // })
        coordinates: multiLatLngsArray.map(latLngs => [
          latLngs.map(latLng => [latLng.lng, latLng.lat]) // 转换为 [lng, lat] 格式
        ])
      };
      return JSON.stringify(multiPolygon);;
    },

    //根据坐标构件coordsString和coordsGeojson
    generateCoordsAndGeojson(k, vv){
      let result = {};
      // 构建coord字段的值，以用户填写的内容为准
      let s = "";
      for (let i = 0; i < k; i++) {
        s += vv[i].xx + "," + vv[i].yy + ";";
      }
      result.coordsString = s;

      // 根据点数构建geojson
      let geo = {};
      if (k == 1) { // 一个点
        geo.type = "Point";
        geo.coordinates = [vv[0].lng, vv[0].lat];
      }
      if (k == 2) { // 一条线
        geo.type = "LineString";
        geo.coordinates = [
          [vv[0].lng, vv[0].lat],
          [vv[1].lng, vv[1].lat]
        ];
      }
      if (k > 2) {
        geo.type = "Polygon";
        let lls = [];
        for (let i = 0; i < k; i++) {
          let v = vv[i];
          lls.push([v.lng, v.lat]);
        }
        lls.push([vv[0].lng, vv[0].lat])
        geo.coordinates = [lls];
      }
      result.coordsGeojson = JSON.stringify(geo);
      return result;
    },

    onClickMap(caller){
      this.$refs.geoPlotter.msgFromCaller = caller.msg;
      this.$refs.geoPlotter.geojson = caller.geojson;
      this.$refs.geoPlotter.typeAllowed = caller.typeAllowed;
      this.$refs.geoPlotter.visible = true;
    },
    // onPlotted(s){
    //   this.mapDialogs["road"].onPlotted(s);
    //   this.mapDialogs["pipe"].onPlotted(s);
    //   this.mapDialogs["wire"].onPlotted(s);
    //   this.mapDialogs["park"].onPlotted(s);
    // },
    onPlotted(coordsString){
      // if (!this.visible) {
      //   return;
      // }
      console.log("onPlotted-coordsString:" + coordsString)
      this.fromCoords(coordsString);
    },
    fromCoords(coordsString){
      if (!coordsString) {
        return;
      }
      let ss = coordsString.split(";");
      if (ss.length == 0) {
        return;
      }
      console.log("fromCoords-ss.length:" + ss.length)
      //当前选中的formItem中的listCoord
      this.formItems[this.choiceFromItemIndex].listCoord = [];
      for (let i = 0; i < ss.length; i++) {
        if (!ss[i]) {
          continue;
        }
        this.formItems[this.choiceFromItemIndex].listCoord.push({
          ref: "formItem_" + this.choiceFromItemIndex + "_coord_" + i, // 例如 formItem_0_coord_0
          id: "formItem_" + this.choiceFromItemIndex + "_" + i,   // 例如 formItem_0_0
          visible: true,
          // ref: "coord_" + i, id: i, visible: true,
        });
        let ll = ss[i].split(",");
        if (ll.length < 2) {
          continue;
        }
        this.$nextTick(() => {
          let el = this.$refs[ this.formItems[this.choiceFromItemIndex].listCoord[i].ref][0];
          el.setValue(ll[0], ll[1]);
        });
      }
    },
    fromGeojson(geojson){
      if (!geojson) {
        return;
      }
    	this.listCoord = [];
    	let geo = JSON.parse(geojson);
    	if (geo.type == "Point") {
        if (this.readOnly) {
          this.formData.aLng = geo.coordinates[0];
          this.formData.aLat = geo.coordinates[1];
          this.formData.bLng = geo.coordinates[0];
          this.formData.bLat = geo.coordinates[1];
        } else {
          this.listCoord.push({
            ref: "coord_0", id: 0, visible: true,
          });
          this.$nextTick(() => {
            let el = this.$refs["coord_0"][0];
            el.setValue(geo.coordinates[0], geo.coordinates[1]);
          });
        }
    	}
    	if (geo.type == "LineString") {

        if (this.readOnly) {
          this.formData.aLng = geo.coordinates[0][0];
          this.formData.aLat = geo.coordinates[0][1];
          this.formData.bLng = geo.coordinates[geo.coordinates.length - 1][0];
          this.formData.bLat = geo.coordinates[geo.coordinates.length - 1][1];
        } else {
          for (let i = 0; i < geo.coordinates.length; i++) {
            this.listCoord.push({
              ref: "coord_" + i, id: i, visible: true,
            });
            let ll = geo.coordinates[i];
            this.$nextTick(() => {
              let el = this.$refs[this.listCoord[i].ref][0];
              el.setValue(ll[0], ll[1]);
            });
          }
        }

    	}
    	if (geo.type == "MultiLineString") {
        if (this.readOnly) {
          this.formData.aLng = geo.coordinates[0][0][0];
          this.formData.aLat = geo.coordinates[0][0][1];
          this.formData.bLng = geo.coordinates[geo.coordinates.length - 1][geo.coordinates[geo.coordinates.length - 1].length - 1][0];
          this.formData.bLat = geo.coordinates[geo.coordinates.length - 1][geo.coordinates[geo.coordinates.length - 1].length - 1][1];
        } else {
          let k = 0;
          for (let r = 0; r < geo.coordinates.length; r++) {
          	for (let i = 0; i < geo.coordinates[r].length; i++) {
          	  this.listCoord.push({
          	    ref: "coord_" + k, id: k, visible: true,
          	  });
          	  let ll = geo.coordinates[r][i];
          	  this.$nextTick(() => {
          	    let el = this.$refs[this.listCoord[k].ref][0];
          	    el.setValue(ll[0], ll[1]);
          	  });
          		k = k + 1;
          	}
          }
        }
    	}
    },
    //以下为采区坐标上传和解析方法
    handleImport(){
      this.upload.open = true;
      this.upload.title = "采区坐标导入"
    },
    beforeUpload(file){
      console.log("beforeUpload:" + file)
      return false;
    },
    //文件上传时触发
    handleFileChange(file) {
      // 存储选择的文件
      console.log("handleFileChange:" + file)
      this.selectedFile = file.raw; // 获取文件对象
    },
    //读取上传的excle文件
    resolveCoords(){
      if (!this.selectedFile) {
        this.$message.error('请先选择一个文件');
        return;
      }
      const reader = new FileReader();
      reader.readAsArrayBuffer(this.selectedFile);
      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // 将工作表转换为 JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        console.log("jsonData:" + jsonData)
        // 假设经纬度在第一列和第二列
        this.coordinates = jsonData.slice(1).map(row => ({
          longitude: row[0],  // 经度
          latitude: row[1],  // 纬度
        }));
        this.handleFileSuccess();
      };
    },
    // 文件上传成功处理
    handleFileSuccess() {
      // 拼接成字符串
      const coordinatesString = this.coordinates
        .map(coord => `${coord.longitude},${coord.latitude}`) // 将每个坐标格式化为 "经度,纬度;经度,纬度"
        .join(';'); // 用分号连接每个坐标
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      console.log("this.coordinates:" + coordinatesString)
      this.fromCoords(coordinatesString)
      // this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
    },
  },
};
</script>

<style scoped lang="scss">
.qrcode-title {
  margin-bottom: 15px;
  font-weight: 700;
  font-size: 16px;
}

::v-deep .el-dialog__body {
  padding: 0 20px!important;
}
.btn-table-operate{
  font-size: 20px;
}
</style>
