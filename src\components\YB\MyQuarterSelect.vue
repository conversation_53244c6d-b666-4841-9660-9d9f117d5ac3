<template>
  <div class="my-quarter-select">
    <el-form-item label="年度" prop="year" label-width="68px" style=" margin-right: 0 !important;">
    <my-select
      v-model="params.year"
      placeholder="请选择年度"
      pvalue="year"
      :clearable="false"
      @change="yearChange"
      @clear="yearClear"
    />
    </el-form-item>
    <el-form-item label="季度" prop="quarter" label-width="68px" style=" margin-right: 0 !important;">
      <el-select
        class="parent-select"
        v-model="params.quarter"
        :clearable="false"
        placeholder="请选择季度"
        @change="quarterChange"
        @clear="quarterClear"
      >
        <el-option
          v-for="item in quarterList"
          :key="item.key"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </div>

</template>

<script>
import common from '@/utils/common'

export default {
  name: 'MyQuarterSelect',
  emits: ['input','change'],
  data() {
    return {
      params: {
        year: '',
        quarter: ''
      },
      months:'',
      fullYear:'',
      currentTime:'',
      quarterList: [
        {value: '1',key:'1', label: '第一季度'},
        {value: '2',key:'2', label: '第二季度'},
        {value: '3',key:'3', label: '第三季度'},
        {value: '4',key:'4', label: '第四季度'},
      ],
      quarterList2: [
        {value: '1',key:'1', label: '第一季度',mounth:[1,2,3],startTime:`${new Date().getFullYear()}-01-16`,endTime:`${new Date().getFullYear()}-04-15`},
        {value: '2',key:'2', label: '第二季度',mounth: [4,5,6],startTime:`${new Date().getFullYear()}-04-16`,endTime:`${new Date().getFullYear()}-07-15`},
        {value: '3',key:'3', label: '第三季度',mounth: [7,8,9],startTime:`${new Date().getFullYear()}-07-16`,endTime:`${new Date().getFullYear()}-10-15`},
        {value: '4',key:'4', label: '第四季度',mounth: [10,11,12],startTime:`${new Date().getFullYear()}-10-16`,endTime:`${new Date().getFullYear()}-12-31`},
        {value: '4',key:'5', label: '第四季度',mounth: [10,11,12],startTime:`${new Date().getFullYear()}-01-01`,endTime:`${new Date().getFullYear()}-01-15`}
      ]
    }
  },
  watch: {
    params: {
      handler(newVal, oldVal) {
        if(newVal.year){
          this.params.year = newVal.year
          this.quarterList =  [
            {value: '1',key:'1', label: '第一季度'},
            {value: '2',key:'2', label: '第二季度'},
            {value: '3',key:'3', label: '第三季度'},
            {value: '4',key:'4', label: '第四季度'},
          ]
        }else {
          this.params.year = ''
          this.quarterList = []
        }
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true // 可以深度检测到 person 对象的属性值的变化
    },

  },
  methods: {
    yearClear() {
      this.params.year = ''
      this.params.quarter = ''
      this.$emit('input', this.params)
      this.$emit('change', this.params)
    },
    quarterClear() {
      this.params.quarter = ''
      this.$emit('input', this.params)
      this.$emit('change', this.params)
    },
    yearChange() {
      this.$emit('input', this.params)
      this.$emit('change', this.params)
    },
    quarterChange() {
      this.$emit('input', this.params)
      this.$emit('change', this.params)
    },
    resetQuery(){
      this.months = new Date().getMonth()
      this.currentTime = common.formatDate(new Date(),'yyyy-MM-dd')
      if(this.currentTime<=`${new Date().getFullYear()}-01-15`){
        this.params.year = String(new Date().getFullYear()-1)
      }else{
        this.params.year = String(new Date().getFullYear())
      }
      this.params.quarter = this.quarterList2.find(item => this.isDuringDate(item.startTime, item.endTime)).value
      console.log(this.params.quarter)
      this.$emit('input', this.params)
      this.$emit('change', this.params)
    },
    isDuringDate(startTime, endTime) {
      if (this.currentTime >= startTime && this.currentTime <= endTime) {
        return true;
      }else {
        return false;
      }
    }
  },
  mounted() {
    this.resetQuery()
  }

}
</script>

<style scoped>
</style>
