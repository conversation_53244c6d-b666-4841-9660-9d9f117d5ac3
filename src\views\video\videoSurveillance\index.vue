<template>
  <div class="app-container" v-cloak>
    <el-row :gutter="10" style="margin: 10px">

      <el-col :sm="6">
        <my-tree ref="myTree" :deptList="deptList" :deptTreeProps="deptTreeProps" nodeKey="deptId"
          :highlight-current="true" :deptExpandedKeys="deptExpandedKeys" @handleLefTreeClick="handleLefTreeClick">
          <template v-slot:default="slotProps">
            <span style="font-size: 14px;color: #606266;"> <i
                :class="slotProps[':data'].type == 'sand' ? 'el-icon-s-home' : 'el-icon-folder-opened'"></i>
              {{ slotProps[':node'].label }}</span>
          </template>
        </my-tree>
      </el-col>

      <el-col :sm="18">
        <el-card style="height: 80vh">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            @submit.native.prevent>
            <el-form-item label="摄像头名称" prop="name" label-width="85px">
              <el-input clearable v-model.trim="queryParams.name" placeholder="请输入摄像头名称"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="接入平台序列号" prop="platformSn" label-width="120px">
              <el-input clearable v-model.trim="queryParams.platformSn" placeholder="请输入接入平台序列号"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
          </el-row>
          <!--          <my-table url="/video/liveCamera/page" ref="liveCameraTable" row-key="id" @my-selection-change="handleSelectionChange" :minWidth="120"  :fixed="true">-->
          <my-table :url="'/video/liveCamera/page/' + dept.deptCode" ref="liveCameraTable" row-key="id" maxHeight="60vh"
            @my-selection-change="handleSelectionChange" :minWidth="120" :fixed="true">
            <el-table-column header-align="center" align="left" label="摄像头名称" min-width="120" prop="name"
              sortable="custom" column-key="NAME"></el-table-column>
            <el-table-column header-align="center" align="left" label="经度" min-width="100" prop="longitude"
              sortable="custom" column-key="LONGITUDE"></el-table-column>
            <el-table-column header-align="center" align="left" label="纬度" min-width="100" prop="latitude"
              sortable="custom" column-key="LATITUDE"></el-table-column>
            <el-table-column header-align="center" align="center" label="接入平台" min-width="100" prop="platform"
              sortable="custom" column-key="PLATFORM">
              <template slot-scope="scope">
                <my-view pvalue="platform" :value="scope.row.platform"></my-view>
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="left" label="小度云地址" min-width="200" prop="appUrl"
              sortable="custom" column-key="APP_URL"></el-table-column>
            <el-table-column header-align="center" align="left" label="序列号" min-width="140" prop="platformSn"
              sortable="custom" column-key="PLATFORM_SN"></el-table-column>
            <el-table-column header-align="center" align="left" label="通道号" min-width="120" prop="channel"
              sortable="custom" column-key="CHANNEL"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" fixed="right" align="center" min-width="120">
              <template slot-scope="scope">
                <el-button title="查看视频" size="mini" type="success" class="btn-table-operate" icon="el-icon-video-play"
                  v-hasPermi="['video:videoSurveillance:info']" @click="handleView(scope.row)"></el-button>
              </template>
            </el-table-column>
          </my-table>
          <!--查看摄像头 -->
          <Camer-Dialog :title="title" :visible.sync="open" width="800px" top="50px" :height="height"
            @close="camera = {}" append-to-body @screenChange="dialogFullscreen">
            <Camer v-if="showCamera" :height="height" :camera="camera"></Camer>
          </Camer-Dialog>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDeptTreeData } from "@/api/video/liveCamera";
import { getDeptTreeDataForUser } from '@/api/system/dept';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { handleTree } from '@/utils/ruoyi'

import MyTree
  from '@/components/YB/MyTree.vue'
import Camer
  from "@/views/video/components/Camer.vue";

export default {
  name: "VideoSurveillance",
  components: {
    Camer,
    MyTree,
  },
  data() {
    return {
      showCamera: true,
      height: '400px',
      selectVideoId: '',
      src: '',
      deptCode: '',
      sandType: 'sand',
      type: '',
      filterText: '',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: '',
        platformSn: ''
      },
      // 表单参数
      liveCamera: {},
      deptList: [],
      deptTreeProps: {
        children: 'children',
        label: 'name'
      },
      deptExpandedKeys: [],  //展开机构树
      dept: {
        deptId: '000_013',  //默认deptId
        areaCode: '',
        areaName: '',
        parentName: '',
        parentId: '-1',
        deptCode: '',
        parentCode: '',
        complaintMobile: '',
        queryMobile: '',
        imgList: [],
        simplifiedCode: '',
      },
      // 部门树选项
      deptOptions: [],
      normalizerDept(node) {
        return {
          id: node.deptId,
          label: node.name,
          children: node.children,
          type: node.type,
        };
      },
      camera: {},
      noResultsText: '未找到结果',
    };
  },
  mounted() {
    //加载机构树
    this.getDeptTreeData()
    this.getDeptTreeselect()
  },
  watch: {
    filterText(val) {
      this.$refs.leftTree.filter(val);
    },
  },
  methods: {
    dialogFullscreen(val) {
      if (val) {
        this.height = document.body.clientHeight + "px"
      } else {
        this.height = "400px"
      }
      this.showCamera = false
      this.$nextTick(() => {
        this.showCamera = true
      })
    },
    handleView(row) {
      console.log(row, '这个')
      this.camera = row
      this.open = true;
    },
    getDeptTreeData() {
      getDeptTreeData().then(r => {
        this.deptList = handleTree(r.deptList, 'deptId', 'parentId')
        var that = this
        this.$nextTick(() => {
          if (that.dept.deptId) {
            that.$refs.myTree.$refs.leftTree.setCurrentKey(that.dept.deptId)
            let node = that.$refs.myTree.$refs.leftTree.getNode(that.dept.deptId)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
          }
        })
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTreeselect() {
      getDeptTreeDataForUser().then(response => {
        this.deptOptions = handleTree(response.deptList, 'deptId', 'parentId');
      });
    },
    /** 查询摄像头表列表 */
    reload(restart) {
      //this.getDeptTreeData()
      this.$refs.liveCameraTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.liveCamera = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: null,
        deptCode: '',
        name: '',
        longitude: null,
        latitude: null,
        platform: '',
        platformSn: '',
        channel: '',
        appKey: '',
        appSecret: ''
      };
      /* this.dept = {
        deptId: '000_013',  //默认deptId
        areaCode: '',
        areaName: '',
        parentName: '',
        parentId: '-1',
        deptCode: '0001',
        parentCode: '',
        complaintMobile: '',
        queryMobile: '',
        imgList: [],
        simplifiedCode:'',
      }; */
      this.resetForm("form");
      this.selectVideoId = '';
      this.camera = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.liveCameraTable.search(this.queryParams, false);
      this.single = true;
      this.multiple = true;

    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.liveCameraTable.getSelectRowKeys()
      this.single = this.ids.length != 1
      this.multiple = !this.ids.length
    },
    handleLefTreeClick(data, treeNode, nodeObj) {
      this.dept = JSON.parse(JSON.stringify(data))
      this.deptExpandedKeys = [this.dept.deptId]
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.liveCameraTable.changeTableHeight();
  },
};
</script>

<style scoped>
iframe {
  border: 1px solid rgba(0, 0, 0, 0.16);
}
</style>
