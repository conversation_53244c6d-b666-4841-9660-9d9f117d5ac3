<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['open:partner:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['open:partner:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['open:partner:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/open/partner/page" ref="partnerTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="应用名称" min-width="150" prop="name" sortable="custom" column-key="NAME"></el-table-column>
  <el-table-column  label="appId" min-width="150" prop="appId" sortable="custom" column-key="APP_ID"></el-table-column>
  <el-table-column  label="appSecret" min-width="150" prop="secret" sortable="custom" column-key="SECRET"></el-table-column>
  <el-table-column  label="aesKey" min-width="150" prop="aesKey" sortable="custom" column-key="AES_KEY"></el-table-column>
  <el-table-column  label="状态" min-width="80" prop="status" sortable="custom" column-key="STATUS">
    <template slot-scope="scope">
      <my-view pvalue="status" :value="scope.row.status"></my-view>
    </template>
  </el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['open:partner:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['open:partner:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改合作伙伴信息表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="partner"  label-width="100px">
        <my-form-item
          label="应用名称"
          ref="name"
          prop="name"
          :rules="[{notNull:true,message:'请输入应用名称'}]">
          <my-input v-model="partner.name" placeholder="应用名称" :maxlength="128"/>
        </my-form-item>
        <my-form-item
          label="appId"
          ref="appId"
          prop="appId"
          :rules="[{notNull:true,message:'请输入appId'}]">
          <my-input v-model="partner.appId" placeholder="请输入appId" :maxlength="128"/>
        </my-form-item>
        <my-form-item
          label="appSecret"
          ref="secret"
          prop="secret"
          :rules="[{notNull:true,message:'请输入appSecret'}]">
          <my-input v-model="partner.secret" placeholder="请输入appSecret" :maxlength="128"/>
        </my-form-item>
        <my-form-item
          label="aesKey"
          ref="aesKey"
          prop="aesKey"
          :rules="[{notNull:true,message:'请输入aesKey'}]">
          <my-input v-model="partner.aesKey" placeholder="请输入aesKey" :maxlength="128"/>
        </my-form-item>
        <my-form-item
          label="状态"
          ref="status"
          prop="status"
          :rules="[{notNull:true,message:'请选择状态'}]"
        >
          <my-select pvalue="status" v-model="partner.status" placeholder="请选择状态"  />
        </my-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getPartner, delPartner, delPartnerBatch,addPartner, updatePartner } from "@/api/open/partner";

export default {
  name: "Partner",
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      partner: {},
    };
  },
  mounted() {
  },
  methods: {
    /** 查询合作伙伴信息表列表 */
    reload(restart) {
      this.$refs.partnerTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.partner = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        name: '',
        appId: '',
        secret: '',
        status: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.partnerTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加合作伙伴信息表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getPartner(id).then(r => {
        this.partner = r.partner;
        this.open = true;
        this.title = "修改合作伙伴信息表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.partner.id) {
            updatePartner(this.partner).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addPartner(this.partner).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delPartner(row.id);
        }else{
          return delPartnerBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.partnerTable.changeTableHeight();
  },
};
</script>
