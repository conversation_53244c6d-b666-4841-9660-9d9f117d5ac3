<template>
  <div id="app-container" :class="isFullScreen? 'fullScreen' : ''"
       :style="'transform: scale('+this.scalingX+','+this.scalingY+') !important;transform-origin: 0 0;'">
    <!-- 背景 -->
    <div class="background">
      <!-- Header -->
      <div class="header">
        <span class="header-title">{{project.name?project.name:'项目监管一张图'}}</span>
      </div>
      <!-- 地图容器{{project.name?project.name:'项目监管一张图'}} -->
      <div id="map" class="map-container">
      </div>

     <!-- <div class="controller">
        <el-button style="margin: 4px 0px;" type="primary" size="mini" @click="onClickFullScreen">
          {{ isFullScreen ? '窗口' : '全屏' }}
        </el-button>
        <br>
        <el-button type="primary" size="mini" @click="onClickLayersToggle">图层</el-button>
      </div> -->

      <div class="controllerView" @click="onClickResetView">
        <!-- <el-button type="primary" size="mini" @click="onClickResetView">返回视野</el-button> -->
      </div>

      <div class="panel panel-header panel-header-1">
        <div class="panel-header-num" style="color: #FFC600;">{{ dailyBillsCount }}</div>
        <div class="panel-header-text">当日采运单数</div>
      </div>
      <div class="panel panel-header panel-header-2">
        <div class="panel-header-num" style="color: #00F6FF;">{{ dailySand }}</div>
        <div class="panel-header-text">当日运砂量(吨)</div>
      </div>
      <div class="panel panel-header panel-header-3">
        <div class="panel-header-num" style="color: #7BF495;">{{ totalBillCount }}</div>
        <div class="panel-header-text">累计采运单数</div>
      </div>
      <div class="panel panel-header panel-header-4">
        <div class="panel-header-num" style="color: #D7B7FF;">{{ totalVehicle }}</div>
        <div class="panel-header-text">累计运砂量({{totalVehicleUnit}})</div>
      </div>
      <div class="panel panel-header panel-header-5" @click="showAlarmMessage">
        <div class="panel-header-num" style="color: #ff3333;">{{ alarmNum }}</div>
        <div class="panel-header-text">报警信息</div>
      </div>

      <div class="panel panel-left panel-left-1">
        <div class="panel-left-header">
          <span class="panel-left-header-text">项目信息</span>
        </div>
        <div>
          <el-row class="projectInfoUl">
            <el-col :span="6">项目名称:</el-col>
            <el-col :span="18">{{project.name}}</el-col>
          </el-row>
          <el-row class="projectInfoUl">
            <el-col :span="6">项目类型:</el-col>
            <el-col :span="18"><my-view pvalue="projectType" :value="project.type"/></el-col>
          </el-row>
          <el-row class="projectInfoUl">
            <el-col :span="6">监管部门:</el-col>
            <el-col :span="18">{{project.deptName}}</el-col>
          </el-row>
          <el-row class="projectInfoUl">
            <el-col :span="6">控制总量:</el-col>
            <el-col :span="18">{{project.totalYield}}（万吨）</el-col>
          </el-row>
          <el-row class="projectInfoUl">
            <el-col :span="12">许可证号/弃砂审批文号:</el-col>
            <el-col :span="12">{{project.licenseNo}}</el-col>
          </el-row>
        </div>
      </div>

      <div class="panel panel-left panel-left-2">
        <div class="panel-left-header">
          <span class="panel-left-header-text">采砂机具列表</span>
        </div>
        <div class="panel-left-3-table">
          <table class="jiJuTable">
            <tr>
              <th style="width: 25%">车牌号</th>
              <th style="width: 20%">车牌颜色</th>
              <th style="width: 20%">驾驶员</th>
              <th style="width: 20%">车辆类型</th>
              <th >状态</th>
            </tr>
          </table>
        </div>
        <div class="panel-left-2-table">
          <table class="jiJuTable">
            <tr v-for="item in cars" @click="moveCar(item)" style="cursor: pointer" :style="item.carPlateNum == rowCarNum?'backgroundColor: rgba(39, 147, 201, 0.43)':''">
              <td style="width: 25%">{{item.carPlateNum}}</td>
              <td style="width: 20%">{{item.carPlateColor}}</td>
              <td style="width: 20%">{{item.name?item.name:'---'}}</td>
              <td style="width: 20%">{{item.carType}}</td>
              <td v-if="item.olineStatus == '在线'">
                <span style="color: #2ddc07">{{item.olineStatus}}</span>
              </td>
              <td v-else>
                <span style="color:red;">{{item.olineStatus}}</span>
              </td>
            </tr>
          </table>
        </div>
      </div>

      <!-- <div v-if="showCarInfoFlag" class="panel panel-right panel-right-1">
        <div class="panel-right-header">
          <span class="panel-right-header-text">车辆/机具信息</span>
          <span class="panel-right-header-icon">
            <a class="a-icon" @click="showCarInfoFlag=false">
            <i class="el-icon-close"></i>
          </a>
          </span>
        </div>
        <div>
          <ul class="projectInfoUl">
            <li>车牌号:<span>{{carInfo.carPlateNum}}</span></li>
            <li>经度:<span style="margin-right:40px ">{{carInfo.lgtd}}</span>纬度:<span>{{carInfo.lttd}}</span></li>
            <li>方向:<span style="margin-right:40px ">{{carInfo.direction}}</span>速度:<span>{{carInfo.bdSpeed}}</span></li>
            <li>驾驶员:<span style="margin-right:40px ">{{carInfo.name?carInfo.name:'---'}}</span>车辆类型:<span>{{carInfo.carType}}</span></li>
            <li>车辆状态:<span style="margin-right:40px ">{{carInfo.olineStatus}}</span> </li>
            <li>时间:<span>{{carInfo.receiveTime}}</span></li>
          </ul>
        </div>
        <div style="float: right"><el-button type="primary" size="small" @click="onClickTWP(carInfo)">行车轨迹</el-button></div>
      </div> -->

      <div v-if="showAlarmInfoFlag" class="panel panel-right panel-right-2">
        <div class="panel-right-header">
          <span class="panel-right-header-text">报警信息列表</span>
        </div>
        <div class="panel-right-3-table">
          <table class="jiJuTable">
            <tr>
              <th style="width: 25%">车牌号</th>
              <th style="width: 25%">报警时间</th>
              <th style="width: 25%">报警位置</th>
              <th style="width: 25%">报警事件</th>
            </tr>
          </table>
        </div>
        <div class="panel-right-2-table">
          <table class="jiJuTable">
            <tr v-for="(item, index) in alarmMessage" :key="index">
              <td style="width: 25%">{{item.carPlateNum}}</td>
              <td style="width: 25%">{{item.time}}</td>
              <td style="width: 25%">{{item.lttd+ ',' +item.lgtd}}</td>
              <td style="width: 25%">超出可采区</td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <div>
      <!--查看摄像头 -->
      <Map-Dialog :title="title" :visible.sync="open" width="800px" top="50px" :height="height" @close="camera = {}" append-to-body @screenChange="dialogFullscreen">
        <Camer v-if="showCamera" :height="height" :camera="camera"></Camer>
      </Map-Dialog>
    </div>
    <track-with-progress ref="twp"></track-with-progress>

  </div>
</template>

<script>

import L from 'leaflet';
import K from "./map_toolkit.js";
import screenfull from 'screenfull';
import '@/plugins/leaflet-pattern/leaflet.pattern-src.js';
import HBGeoJson from '@/assets/onemap/hebei/HBGeoJson.geojson';
import yehe_K4 from '@/assets/onemap/hebei/冶河可采区K4.geojson';
import yehe_J1 from '@/assets/onemap/hebei/冶河禁采区J1.geojson';
import yehe_J2 from '@/assets/onemap/hebei/冶河禁采区J2.geojson';
import yehe_J3 from '@/assets/onemap/hebei/冶河禁采区J3.geojson';
import yehe_J4 from '@/assets/onemap/hebei/冶河禁采区J4.geojson';
import yehe_B7 from '@/assets/onemap/hebei/冶河保留区B7.geojson';
import yehe_B8 from '@/assets/onemap/hebei/冶河保留区B8.geojson';
import dashahe_K1 from '@/assets/onemap/hebei/大沙河可采区K1.geojson';
import dashahe_J1 from '@/assets/onemap/hebei/大沙河禁采区J1.geojson';
import dashahe_J2 from '@/assets/onemap/hebei/大沙河禁采区J2.geojson';
import dashahe_J3 from '@/assets/onemap/hebei/大沙河禁采区J3.geojson';
import dashahe_J4 from '@/assets/onemap/hebei/大沙河禁采区J4.geojson';
import dashahe_B1 from '@/assets/onemap/hebei/大沙河保留区B1.geojson';
import dashahe_B2 from '@/assets/onemap/hebei/大沙河保留区B2.geojson';
import dashahe_B3 from '@/assets/onemap/hebei/大沙河保留区B3.geojson';
import dashahe_B4 from '@/assets/onemap/hebei/大沙河保留区B4.geojson';
import dashahe_B5 from '@/assets/onemap/hebei/大沙河保留区B5.geojson';

import {
  getProjectListByCondition,
  getTruckRealTime,
  getTruckHistory,
  getCountByDeptId,
  getCameraListByDeptId
} from '@/api/onemap/onemap';
import {
  getVideoSurveillance
} from '@/api/video/videoSurveillance'
import {
  getProject
} from '@/api/project/project'

import TrackWithProgress from '@/components/TrackWithProgress/index.vue'

import * as turf from '@turf/turf'
import "./my_marker.js"
import Camer
  from "@/views/video/components/Camer.vue";

let layerTdtImg = null;
let layerTdtNote = null;

export default {
  name: "detail",
  components:{
    Camer,
    TrackWithProgress,
  },
  data() {
    return {
      rowCarNum:'',
      showCamera: true,
      camera: {},
      project: {
        name: '',
        type: '',
        parentId: '',
        totalYield: '',
        licenseNo: ''
      },
      title: '',
      open: false,
      src: '',
      height: '400px',
      projectId: '',
      backgroundImage: require('@/assets/onemap/images/backgroud.png'),
      headerImage: require('@/assets/onemap/images/header.png'),
      panelLeftHeaderImage: require('@/assets/onemap/images/title.png'),
      map: null,
      isFullScreen: true,
      isLayersShownInController: false,
      queryParam: {},
      carUpdateInterval: null, //数据请求定时器
      countUpdateInterval: null, //数据请求定时器
      cars: [
        // {
        // marker : null,
        // deviceId: "10641013426",
        // carPlateNum: "冀HM8888",
        // carPlateColor: "黄色",
        // lgtd: 118.0718,
        // lttd: 40.84013,
        // direction: 111,
        // tGpsStr: "2024-10-25 14:42:52",
        // idCode: "LEFYEDK46HHNA6132",
        // bdSpeed: 0,
        // setupTime: "2024-08-28",
        // bdDevSpeed: 0,
        // firstUp: null,
        // receiveTime: "2024-10-25 14:42:51",
        // pos: true,
        // tUpStr: "2024-10-25 14:42:51",
        // deviceSignalMode: "单北斗",
        // status: 524290,
        // mileage: 3060.8,
        // drift: false
        // }
      ], // 存储汽车的经纬度、朝向和marker等信息
      markerTrucks: [], //存储当前所有车辆的marker和角度
      isUpdatingCars: false, // 标志位，表示是否正在更新cars
      isUpdatingMarkers: false, // 标志位，表示是否正在更新markers


      dailyBillsCount: 0,//当日采运单数
      dailySand: 0,//当日运砂量
      totalBillCount: 0,//累计采运单数
      totalVehicleLoad: 0,//累计运砂量
      alarmNum: 0, //当前报警信息个数
      alarmMessage: [], //报警信息对象 存储报警车辆和报警时间、位置等信息
      showAlarmInfoFlag: false,

      //可采区K4 geoJsonLayer
      geoJsonLayer_K4: null,
      areaBounds_K4: null,

      //大沙河可采区
      getJsonLayer_dshK1: null,

      cameras: [],
      scalingX: 100,
      scalingY: 100,
      showCarInfoFlag: false,
      carInfo: {},


    };
  },
  computed: {
    totalVehicle: function () {
      if (this.totalVehicleLoad < 100000) {
        return (this.totalVehicleLoad > 0 ? this.totalVehicleLoad : '--');
      } else if (this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000) {
        return (this.totalVehicleLoad / 10000).toFixed(2)
      } else if (this.totalVehicleLoad >= 1000000000 && this.totalVehicleLoad < 10000000000000) {
        return (this.totalVehicleLoad / 100000000).toFixed(2)
      } else {
        return (this.totalVehicleLoad / 1000000000000).toFixed(2)
      }
    },
    totalVehicleUnit: function () {
      if (this.totalVehicleLoad < 100000) {
        return '吨'
      } else if (this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000) {
        return '万吨'
      } else if (this.totalVehicleLoad >= 1000000000) {
        return '亿吨'
      } else {
        return '万亿吨'
      }
    },
  },
  mounted() {
    var that = this;
    document.title = "项目监管详情";
    this.projectId = this.$route.params.projectId;
    console.log(this.projectId);
    window.onresize = function () {
      that.getScale();
    };
    that.getScale();

    if (screenfull.isEnabled) {
      console.log('Supported');
      screenfull.on('change', this.onChangeScreenFull);
    }

    // 监听全屏事件
    document.addEventListener('fullscreenchange', this.handleFullScreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);
    // return;
    this.initMap(); // 初始化地图
    this.fetchCarData(); // 获取后台车辆数据信息
    this.updateCars(); // 更新汽车位置和朝向
    this.generateGeoJsonLayer(); //生成图层
    this.updateCount(); //获取项目当日和总才运单数、采砂量

    this.initCamera();
    //获取项目信息
    this.getProjectInfo();
  },
  watch: {

  },

  beforeDestroy() {
    // 在组件销毁时清除定时器
    if (this.carUpdateInterval) {
      clearInterval(this.carUpdateInterval);
    }
  },

  methods: {
    dialogFullscreen(val){
      if (val){
        this.height = document.body.clientHeight + "px"
      }else {
        this.height = "400px"
      }
      this.showCamera = false
      this.$nextTick(()=> {
        this.showCamera = true
      })
    },
    //获取缩放比例
    getScale() {
      let bodyWidth = document.body.clientWidth;
      let bodyHeight = document.body.clientHeight;
      this.scalingX = Math.ceil(bodyWidth / 1920 * 100) / 100;
      this.scalingY = Math.ceil(bodyHeight / 1080 * 100) / 100;
    },
    //初始化地图
    initMap() {
      K.initMap("map",this.projectId);
      layerTdtImg = K.createLayerWmtsTdt("img", 5);
      // layerTdtImg.setOpacity(0.667);
      // layerTdtVec= K.createLayerWmtsTdt("vec", 4);
      layerTdtNote = K.createLayerWmtsTdt("cva", 9999);
      // layerTdtNote.setOpacity(0.667);
      K.switchLayer(layerTdtImg, true);
      K.switchLayer(layerTdtNote, true);

      // K.viewer.on("zoomend", this.rotateMarker);

      L.geoJSON(HBGeoJson, {
        style: {
          color: 'blue',
          weight: 2,
          opacity: 1,
          fillOpacity: 0
        }
      }).addTo(K.viewer);

      //显示下点击位置坐标，方便定位
      K.viewer.on("click", function (e) {
        console.log("e", e.latlng);
      });

    },
    //初始化采区图层
    generateGeoJsonLayer() {
      // getProjectListByCondition(this.queryParam).then(res => {
      //   const geojsonData = JSON.parse(res.list[0].geoJson)
      //   console.log("getProjectListByCondition++++++++" + geojsonData)
      // })

      // 可采区样式
      // 内部斜线图案
      var K_stripePattern = new L.StripePattern({
        weight: 2, // 线条宽度
        color: '#00ff00', // 绿
        opacity: 0.5, // 线条不透明度
        angle: 15 // 线条角度
      });
      K_stripePattern.addTo(K.viewer);

      // 边界样式
      function K_style(feature) {
        return {
          color: '#00ff00',
          weight: 3,
          opacity: 0.5,
          fillOpacity: 0.5,
          dashArray: '5, 5',
          fillPattern: K_stripePattern
        };
      }

      // 禁采区样式
      // 内部斜线图案
      var J_stripePattern = new L.StripePattern({
        weight: 2, // 线条宽度
        color: '#ff1a1a', // 红
        opacity: 0.5, // 线条不透明度
        angle: 45 // 线条角度
      });
      J_stripePattern.addTo(K.viewer);

      // 边界样式
      function J_style(feature) {
        return {
          color: '#ff1a1a',
          weight: 3,
          opacity: 0.5,
          fillOpacity: 0.5,
          dashArray: '5, 5',
          fillPattern: J_stripePattern
        };
      }

      // 保留区样式
      // 内部斜线图案
      var B_stripePattern = new L.StripePattern({
        weight: 2, // 线条宽度
        color: '#ffff66', // 黄
        opacity: 0.5, // 线条不透明度
        angle: 75 // 线条角度
      });
      B_stripePattern.addTo(K.viewer);

      // 边界样式
      function B_style(feature) {
        return {
          color: '#ffff66',
          weight: 3,
          opacity: 0.5,
          fillOpacity: 0.5,
          dashArray: '5, 5',
          fillPattern: B_stripePattern
        };
      }

      // 可采区geoJson图层
      this.geoJsonLayer_K4 = L.geoJSON(yehe_K4, {
        style: K_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      // 获取可采区域的边界
      this.areaBounds_K4 = this.geoJsonLayer_K4.getBounds();
      // 禁采区geoJson图层
      L.geoJSON(yehe_J1, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J2, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J3, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J4, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      // 保留区geoJson图层
      L.geoJSON(yehe_B7, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_B8, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

      //加载大沙河的4个可采区
      this.getJsonLayer_dshK1 = L.geoJSON(dashahe_K1, {
        style: K_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      // 禁采区geoJson图层
      L.geoJSON(dashahe_J1, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

      L.geoJSON(dashahe_J2, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

      L.geoJSON(dashahe_J3, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

      L.geoJSON(dashahe_J4, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

      L.geoJSON(dashahe_B1, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(dashahe_B2, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(dashahe_B3, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(dashahe_B4, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(dashahe_B5, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

    },

    // 后台获取车辆信息
    async fetchCarData() {
      try {
        const res = await getTruckRealTime(this.projectId);
        const newCars = res.truckRealList;

        // 设置标志位，表示开始更新
        this.isUpdatingCars = true;
        // console.log("开始更新cars-----------------------")
        // 更新现有的cars数组
        newCars.forEach(newCar => {
          const existingCarIndex = this.cars.findIndex(car => car.carPlateNum === newCar.carPlateNum);
          if (existingCarIndex !== -1) {
            // 如果车辆已存在，更新其信息
            const existingCar = this.cars[existingCarIndex];
            this.cars[existingCarIndex] = {
              ...newCar, // 更新新信息
              marker: existingCar.marker // 保留marker（如果存在）
            };
          } else {
            // 如果车辆不存在，添加新车辆
            this.cars.push({
              ...newCar,
              marker: null // 新车辆的marker初始化为null
            });
          }
        });

        // 更新完成，重置标志位
        this.isUpdatingCars = false;
        // console.log("结束更新cars-----------------------")
        this.updateMarkers(); // 更新标记
      } catch (error) {
        console.error('获取汽车数据失败:', error);
        this.isUpdatingCars = false; // 确保在发生错误时重置标志位
      }
    },
    // 更新车辆marker
    updateMarkers() {
      try{
        if (this.isUpdatingCars) {
          // 如果正在更新cars，延迟执行
          console.log('正在更新车辆数据或标记，延迟标记更新');
          setTimeout(() => this.updateMarkers(), 100); // 100ms 后重试
          return;
        }
        // console.log("开始更新markers%%%%%%%%%%%%%%%%%%%%%%")
        // 设置标志位，表示开始更新markers
        this.isUpdatingMarkers = true;

        // // 使用深拷贝的cars数组
        // const carsCopy = JSON.parse(JSON.stringify(this.cars));

        this.cars.forEach(car => {
          try {
            const {lgtd, lttd, direction, marker} = car;
            // console.log("----正在更新" + car.carPlateNum)
            if (marker) {
              // 如果marker已存在，更新位置和朝向
              marker.setLatLng([lttd, lgtd]); // 更新位置
              marker.setRotationAngle(direction);
              // const iconElement = marker.getElement();
              // if (iconElement) {
              //   let str = iconElement.style.transform;
              //   let ss = str.split(' rotate(');
              //   iconElement.style.transform = ss[0] + ' rotate(' + direction + 'deg)'; // 旋转标记
              //   iconElement.style.transformOrigin = '50% 50%';
              // }

              // 将marker存储到markerTrucks数组中，用于缩放地图重置方向
              this.markerTrucks.push({marker,deg:direction});

              if(car.carType === '挖掘机'){
                //处理车辆是否超出采区边界
                this.setOrRemoveBorderToMarker(marker,car)
              }
            } else {
              // 如果标记不存在，添加新标记
              this.addMarker(car);
            }
          }catch (error) {
              console.error("更新车辆" + car.carPlateNum +"出错: ", error); // 处理错误并继续
          }
        });
        // 更新完成，重置标志位
        this.isUpdatingMarkers = false;
        // console.log("结束更新markers%%%%%%%%%%%%%%%%%%%%%%")
      }catch(error){
        console.error('更新marker数据失败:', error);
        this.isUpdatingMarkers = false;
      }

    },

    //缩放地图时，重置marker方向
    rotateMarker() {
      this.markerTrucks.forEach(markerTruck => {
        let iconElement = markerTruck.marker.getElement();
        if (iconElement) {
          let str = iconElement.style.transform;
          let ss = str.split(' rotate(');
          iconElement.style.transform = ss[0] + ' rotate(' + markerTruck.deg + 'deg)'; // 旋转标记
          iconElement.style.transformOrigin = '50% 50%';
        }
      });
    },

    // 为每辆车创建marker
    addMarker(car) {
      const {deviceId, carPlateNum,olineStatus, carPlateColor, lgtd, lttd, direction, carType} = car;
      if (!lttd ||!lgtd) {
        return
      }
      let that=this;
      // 创建自定义图标
      let customIcon = null;
      if(carType === '挖掘机'){
        if (olineStatus === '在线'){
          customIcon = L.icon({
            iconUrl: require('@/assets/onemap/images/car1.png'), // 使用本地图标
            iconAnchor: [17.5, 38.5], // 图标锚点
            popupAnchor: [-3, -76] // 弹出框锚点
          });
        }else {
          customIcon = L.icon({
            iconUrl: require('@/assets/onemap/images/car1_ontline.png'), // 使用本地图标
            iconAnchor: [17.5, 38.5], // 图标锚点
            popupAnchor: [-3, -76] // 弹出框锚点
          });
        }

      }else if((carType === '卡车')){
        if (olineStatus === '在线'){
          customIcon = L.icon({
            iconUrl: require('@/assets/onemap/images/car5.png'), // 使用本地图标
            iconAnchor: [17.5, 38.5], // 图标锚点
            popupAnchor: [-3, -76] // 弹出框锚点
          });
        }else {
          customIcon = L.icon({
            iconUrl: require('@/assets/onemap/images/car5_ontline.png'), // 使用本地图标
            iconAnchor: [17.5, 38.5], // 图标锚点
            popupAnchor: [-3, -76] // 弹出框锚点
          });
        }

      }
      const marker = L.marker(
        [lttd, lgtd],
        { icon: customIcon },
        { rotationAngle: direction },
        { rotationOrigin: '50% 50%' },
      ).addTo(K.viewer);

      marker.bindTooltip("车牌号："+car.carPlateNum,{direction:"top",offset:[0,-5]})

      // 将marker存储到markerTrucks数组中，用于缩放地图重置方向
      this.markerTrucks.push({marker,deg:direction});


      if(car.carType === '挖掘机'){
        //处理车辆是否超出采区边界
        this.setOrRemoveBorderToMarker(marker,car)
      }

      // 为marker添加点击事件
      marker.on('click', data => {
        let closeButton = document.getElementById('close-popup');
        if(closeButton){
          closeButton.remove();
        }
        let trackButton = document.getElementById('trackButton');
        if (trackButton){
          trackButton.remove()
        }
        const carInfo = this.cars.find(car => car.carPlateNum === carPlateNum);
        if (carInfo) {
          // 显示车辆信息
          // this.showCarInfo(carInfo)
        }
        this.rowCarNum = carInfo.carPlateNum

        //点击marker在点击位置弹窗
        L.popup()
          .setLatLng(data.latlng)
          .setContent(`
            <div id="truck_info_panel" class="truck_info_panel">
              <div class="truck_info-header">
                <span class="truck_info-header-text">车辆/机具信息</span>
                <span class="truck_info-header-icon">
                  <a id="close-popup" class="a-icon">
                    <i class="el-icon-close"></i>
                  </a>
                </span>
              </div>
              <div>
                <ul class="truckInfoUl">
                  <li>车牌号:<span>${carInfo.carPlateNum}</span></li>
                  <li>经度:<span style="margin-right:40px ">${carInfo.lgtd}</span>纬度:<span>${carInfo.lttd}</span></li>
                  <li>方向:<span style="margin-right:40px ">${carInfo.direction}</span>速度:<span>${carInfo.bdSpeed}</span></li>
                  <li>驾驶员:<span style="margin-right:40px ">${carInfo.name?carInfo.name:'---'}</span>车辆类型:<span>${carInfo.carType}</span></li>
                  <li>车辆状态:<span style="margin-right:40px ">${carInfo.olineStatus}</span> </li>
                  <li>时间:<span>${carInfo.receiveTime}</span></li>
                </ul>
              </div>
              <div class="truck_info-footer" style="float: right">
                <el-button id="trackButton" class="truck_info-footer-button">
                  行车轨迹
                </el-button>
              </div>
            </div>
          `)
          .openOn(K.viewer);

        // 使用 setTimeout 确保 DOM 元素已经渲染
        setTimeout(() => {
          const leafletPopupElement = document.getElementById('truck_info_panel'); // 获取Leaflet弹窗的DOM元素
          if (leafletPopupElement) {
            that.adjustPopupPosition(leafletPopupElement);
          } else {
            console.error("Leaflet popup element not found");
          }

          // 绑定关闭弹出框的事件
          closeButton = document.getElementById('close-popup');
          if (closeButton) {
            closeButton.addEventListener('click', () => {
              console.log("Close button clicked");
              K.viewer.closePopup();
            });
          } else {
            console.error("Close button not found");
          }

          // 绑定行车轨迹按钮的事件
          trackButton = document.getElementById('trackButton');
          if (trackButton) {
            trackButton.addEventListener('click', () => {
              console.log("Track button clicked");
              this.onClickTWP(carInfo);
            });
          } else {
            console.error("Track button not found");
          }
        }, 0); // 延迟0毫秒，确保DOM渲染完成
      });


      // 将marker存储到对应的car对象中
      const carIndex = this.cars.findIndex(car => car.carPlateNum === carPlateNum);
      if (carIndex !== -1) {
        this.cars[carIndex].marker = marker; // 更新car对象中的marker
      }

    },

    //动态调整弹窗位置
    adjustPopupPosition(popupElement) {
      console.log("adjustPopupPosition:",popupElement)
      const popupBounds = popupElement.getBoundingClientRect();
      console.log("popupBounds:",popupBounds)

      // 获取视口的宽度和高度
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      console.log("viewportWidth:",viewportWidth)
      console.log("viewportHeight:",viewportHeight)

      // 计算弹窗的右边界和底部边界
      const popupRight = popupBounds.left + popupBounds.width;
      const popupBottom = popupBounds.top + popupBounds.height;
      console.log("popupRight:",popupRight)
      console.log("popupBottom:",popupBottom)

      const leafletPopupElement = document.querySelector('.leaflet-popup');
      // 如果弹窗超出右边界，调整其位置
      if (popupRight > viewportWidth) {
        console.log("超出右边界")
        leafletPopupElement.style.left = '-510px';
      }else{
        leafletPopupElement.style.left = '-25px';
      }
      // 如果弹窗超出底部边界，调整其位置
      if (popupBottom > viewportHeight) {
        console.log("超出下边界")
        leafletPopupElement.style.bottom = '350px';
      }else{
        leafletPopupElement.style.bottom = '-35px';
      }
      console.log("leafletPopupElement:", leafletPopupElement)
    },


    //右侧显示车辆信息
    showCarInfo(car) {
      this.carInfo = car;
      this.showCarInfoFlag = true;
    },

    async updateCars() {
      // 清除之前的定时器
      if (this.carUpdateInterval) {
        clearTimeout(this.carUpdateInterval);
      }
      // 检查是否正在更新标记
      if (this.isUpdatingMarkers) {
        console.log('正在更新车辆标记，延迟标记更新');
        this.carUpdateInterval = setTimeout(() => this.updateCars(), 100); // 100ms 后重试
        return;
      }

      // 获取汽车数据
      await this.fetchCarData();

      // 设置下一个更新周期
      this.carUpdateInterval = setTimeout(() => this.updateCars(), 1000*10); // 每10秒更新一次
    },
    // 给geoJson图层添加事件
    onEachFeature(feature, layer) {
      layer.on({
        click: function (e) {
          if (feature.properties) {
            // 弹出窗口显示信息
            var popupContent = `<strong>${feature.properties.name}</strong><br>${feature.properties.info}`;
            L.popup()
              .setLatLng(e.latlng)
              .setContent(popupContent)
              .openOn(K.viewer);
          }


          // // 高亮区域
          // layer.setStyle({
          //     opacity: 1,
          //     fillOpacity: 1
          // });
        }
      });
      // 高亮
      layer.on('mouseover', function () {
        layer.setStyle({
          opacity: 1,
          fillOpacity: 1
        });
      });
      // 取消高亮
      layer.on('mouseout', function () {
        layer.setStyle({
          opacity: 0.5,
          fillOpacity: 0.5
        });
      });
    },
    //全屏
    onClickFullScreen() {
      // return;
      if (!screenfull.isEnabled) {
        this.$message({message: '您的浏览器不支持全屏', type: 'warning'})
        return;
      }
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    },
    onClickLayersToggle() {
      this.isLayersShownInController = !this.isLayersShownInController;
    },
    onChangeScreenFull(e) {
      console.log(e);
      this.isFullScreen = screenfull.isFullscreen;
    },
    handleFullScreenChange() {
      const isFullScreen = document.fullscreenElement || document.webkitFullscreenElement;
      if (isFullScreen) {
        // 进入全屏模式时的处理逻辑
        this.$store.dispatch('app/toggleSideBarHide', true);
        document.querySelector('.navbar').style.display = "none"
        document.querySelector('.tags-view-container').style.display = "none"
      } else {
        // 退出全屏模式时的处理逻辑
        this.$store.dispatch('app/toggleSideBarHide', false);
        document.querySelector('.navbar').style.display = ""
        document.querySelector('.tags-view-container').style.display = ""
      }
    },
    onClickResetView() {
      K.setViewToHebei();
    },
    //定时更新个数
    updateCount() {
      if (this.countUpdateInterval) {
        clearInterval(this.countUpdateInterval);
      }
      this.getCountByDeptId();
      var that=this;
      setInterval(function () {
        that.getCountByDeptId();
      }, 30 * 1000);
    },
    //获取项目采运单、运砂量数据
    getCountByDeptId() {
      getCountByDeptId(this.projectId).then(res => {
        if (res.projectStartDto == null) {
          this.dailyBillsCount = '--'
          this.dailySand = '--'
          this.totalBillCount = '--'
          this.totalVehicleLoad = 0;
        } else {
          this.dailyBillsCount = res.projectStartDto.billCount
          this.dailySand = res.projectStartDto.dailyVehicleLoad
          this.totalBillCount = res.projectStartDto.totalBillCount
          this.totalVehicleLoad = res.projectStartDto.totalVehicleLoad
        }
      });


    },
    //获取摄像头位置，更新到地图上
    initCamera() {
      getCameraListByDeptId(this.projectId).then(r => {
          this.cameras = r.liveCameraList;
          if (this.cameras && this.cameras.length > 0) {
            this.addCameraMarkers();
          }
        }
      )
    },
    //向地图添加摄像头位置
    addCameraMarkers() {
      var that = this;
      // 创建自定义图标
      const customIcon = L.icon({
        iconUrl: require('@/assets/onemap/images/camer.png'), // 使用本地图标
        iconAnchor: [16, 16], // 图标锚点
        popupAnchor: [-3, -76] // 弹出框锚点
      });

       // 创建自定义图标
       const customIconTieta = L.icon({
        iconUrl: require('@/assets/onemap/images/tietacamer.png'), // 使用本地图标
        iconAnchor: [16, 16], // 图标锚点
        popupAnchor: [-3, -76] // 弹出框锚点
      });
      for (let i = 0; i < this.cameras.length; i++) {
        if (!this.cameras[i].latitude || !this.cameras[i].longitude) {
          continue;
        }
        let marker = L.marker([this.cameras[i].latitude, this.cameras[i].longitude], {icon: customIcon}).addTo(K.viewer);
        if (this.cameras[i].platform=="tieTa") {
          marker = L.marker([this.cameras[i].latitude, this.cameras[i].longitude], {icon: customIconTieta}).addTo(K.viewer);
        }


        marker.on("click", function () {
          console.log(that.cameras[i])
          that.showVideo(that.cameras[i], that.cameras[i].name);
        });
        marker.bindTooltip(that.cameras[i].name,{direction:"top",offset:[20,-10]})
        this.cameras[i].marker = marker;
      }
    },
    //展示视频
    showVideo(camera, cameraName) {
      this.title = '实时视频-' + cameraName;
      this.open = true;
      this.camera = camera;
    },

    //车辆轨迹
    onClickTWP(carInfo){
      const data = {
        carPlateNum: carInfo.carPlateNum, //从 car 对象中提取 carPlateNum
        carPlateColor: carInfo.carPlateColor, // 从 car 对象中提取 carPlateColor
      };
      //获取当前车辆历史数据
      getTruckHistory(data).then(res =>{
        this.$refs.twp.visible = true;
        this.$refs.twp.carPlateNum = data.carPlateNum;
        if(res.truckHisList != null&&res.truckHisList.length>0){
          this.$refs.twp.resList = res.truckHisList
        }else {
          this.$refs.twp.resList=[]
        }
      })

    },
    //获取项目信息
    getProjectInfo(){
      getProject(this.projectId).then(r => {
        this.project = r.project;
        document.title = this.project.name + "-项目监管详情";
      });
    },
    //处理车辆是否超出采区边界
    setOrRemoveBorderToMarker(marker,thisCar){
      const flashMarker = () => {
          const iconElement = marker.getElement();
          if (iconElement && !iconElement.classList.contains('marker-border')) {
              iconElement.classList.add('marker-border'); // 添加边框动画类
          }
      };
      const removeFlashMarker = () => {
          const iconElement = marker.getElement();
          if (iconElement && iconElement.classList.contains('marker-border')) {
              iconElement.classList.remove('marker-border'); // 移除边框动画类
          }
      };

      // 检查车辆是否在可采区域内
      // 使用Turf.js判断点是否在MultiPolygon内
      let turf_lng = marker.getLatLng().lng
      let turf_lat = marker.getLatLng().lat
      //看是冶河还是大沙河
      let configIds=process.env.VUE_APP_PROJECT_ID;
      let keCatQu = configIds.indexOf(this.projectId) > 10 ? dashahe_K1 : yehe_K4;
      const isInside = turf.booleanPointInPolygon(turf.point([turf_lng,turf_lat]), keCatQu.features[0]);
      console.log(isInside);
      if (!isInside) {
        flashMarker(); // 调用闪烁效果

        // 更新报警信息
        const alarmInfo = {
          carPlateNum: thisCar.carPlateNum,
          time: new Date().toLocaleString(), // 当前时间
          lgtd: thisCar.lgtd ,
          lttd: thisCar.lttd
        };

        // 检查是否已经存在该报警信息
        const existingAlarmIndex = this.alarmMessage.findIndex(alarm => alarm.carPlateNum === thisCar.carPlateNum);
        if (existingAlarmIndex === -1) {
          // 如果不存在，添加新的报警信息
          this.alarmMessage.push(alarmInfo); // 添加新的报警信息
          this.alarmNum++; // 增加报警数量
        } else {
          this.$set(this.alarmMessage, existingAlarmIndex, alarmInfo);
        }
      }else{// 在采取范围内
        removeFlashMarker();
        // 移除报警信息
        const alarmIndex = this.alarmMessage.findIndex(alarm => alarm.carPlateNum === thisCar.carPlateNum);
        if (alarmIndex !== -1) {
          this.alarmMessage.splice(alarmIndex, 1); // 移除报警信息
          this.alarmNum--; // 减少报警数量
        }
      }
    },
    showAlarmMessage(){
      if(this.showAlarmInfoFlag){
        this.showAlarmInfoFlag = false
      }else{
        this.showAlarmInfoFlag = true
      }
    },
    moveCar(val){
      this.rowCarNum = val.carPlateNum;
      console.log(val)
      if (val.lttd && val.lgtd){
        //document.querySelector('.carRow').style.background = "blue"
        let center = L.latLng(val.lttd, val.lgtd);
        K.viewer.setView(center)
      }
    },
  }
}
</script>

<style scoped lang="scss">
@font-face {
  font-family: "AlimamaShuHeiTi-Bold";
  src: url('../../../assets/onemap/font/AlimamaShuHeiTi-Bold.ttf');
}

.background {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1233;
  background-image: url("../../../assets/onemap/images/backgroud.png");
  background-position: center center;
  background-repeat: no-repeat;
}

.header {
  position: absolute;
  top: 0;
  width: 100%;
  height: 101px;
  z-index: 1235;
  background-image: url("../../../assets/onemap/images/header.png");
}

.header-title {
  text-align: center;
  line-height: 1.2;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 550px;
  height: 70px;
  left: 630px;
  top: 0px;
  font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
  font-weight: bold;
  font-size: 28px;
  color: #ddd;
  font-style: normal;
  text-transform: none;
  background: radial-gradient(0deg, #A1C4FD 0%, #C2E9FB 100%);
  overflow: hidden;
}

.map-container {
  width: 1876px;
  height: 1003px;
  left: 22px;
  right: 22px;
  bottom: 22px;
  top: 55px;
  z-index: 1234;
  clip-path: polygon(40px 0, 1836px 0, 1876px 40px,1876px 963px,1836px 1003px,40px 1003px,0px 963px,0 40px);
}

#app-container {
  position: relative;
  width: 100%;
  height: calc(100vh);
}

#app-container.fullScreen {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 1920px;
  height: 1080px;
  z-index: 1234;
  /* 不要超过2000，因为系统默认对话框的zIndex是2000~2030 */
  /* 不要低于1000，因为框架包裹的zIndex是930~1000 */
}

.controller {
  background-color: #c0c0c0c0;
  border-radius: 8px;
  padding: 8px;
  position: absolute;
  z-index: 1240;
  top: 16px;
  left: 16px;
}

.controllerView {
  background-image: url('../../../assets/onemap/reset.png');
  /* background-color: #c0c0c0c0; */
  background-repeat: round;
  position: absolute;
  z-index: 401;
  bottom: 50px;
  left: 50px;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid #ddd;;
}

.marker-icon {
  transition: transform 0.3s; /* 添加平滑过渡效果 */
}

.panel {
  position: absolute;
  user-select: none;
  background: #020F2299;
  box-shadow: 0px 0px 18px 0px #0A5075, inset 0px 0px 35px 0px #4F91B3;
  border-radius: 4px 4px 4px 4px;
  border: 2px solid #0273AE;
  z-index: 1235;
}

.panel-header {
  top: 110px;
  width: 233px;
  height: 110px;
}

.panel-header-1 {
  left: 45px;
}

.panel-header-2 {
  left: 306px;
}

.panel-header-3 {
  top: 240px;
  left: 45px;
}

.panel-header-4 {
  top: 240px;
  left: 306px;
}

.panel-header-5 {
  width: 130px;
  right: 45px;
  cursor: pointer;
}

.panel-left {
  left: 45px;
  width: 497px;
  padding: 15px;
}

.panel-right {
  right: 45px;
  width: 387px;
  height: 343px;
  padding: 15px;
}
.panel-right-1{
  top: 250px;
 }

.panel-right-2 {
  top: 270px;
  height: 710px;
}

.panel-left-1 {
  top: 380px;
  height: 303px;
}

.panel-left-2 {
  top: 710px;
  height: 280px;
}

.panel-left-header,.panel-right-header{
  position: relative;
  width: 100%;
  height: 40px;
  background-image: url("../../../assets/onemap/images/title.png");
  background-repeat: no-repeat;
}

.panel-left-header-text{
  margin-left: 18%;
  font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 38px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.panel-right-header-text{
  margin-left: 23%;
  font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 38px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.panel-right-header-icon{
  float: right;
  font-weight: bold;
  font-size: 28px;
  line-height: 38px;
  color: #FFFFFF;
  padding-right: 20px;
}

.panel-left-2-table {
  width: 95%;
  top: 95px;
  position: absolute;
  height: 160px; /* 设置固定高度 */
  overflow-y: auto; /* 超出时启用垂直滚动 */
}
.panel-left-3-table {
  width: 100%;
}
.panel-left-3-table .jiJuTable{
}

.panel-right-2-table {
  width: 95%;
  top: 95px;
  position: absolute;
  height: 580px; /* 设置固定高度 */
  overflow-y: auto; /* 超出时启用垂直滚动 */
}
.panel-right-3-table {
  width: 100%;
}

.custom-table.no-background .el-table__body-wrapper {
  background-color: transparent;
}

.custom-table .el-table__row {
  border-bottom: 1px solid #ddd;
}

.custom-table .el-table__row:hover {
  background-color: transparent;
}


.panel-header-num {
  margin-top: 10px;
  width: 100%;
  height: 34px;
  font-family: AlimamaShuHeiTi-Bold;
  font-weight: bold;
  font-size: 42px;
  color: #FFC600;
  line-height: 50px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  position: relative;
}

.panel-header-text {
  margin-top: 25px;
  width: 100%;
  height: 20px;
  font-family: MAlimamaShuHeiTi-Bold;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  position: absolute;
}
.projectInfoUl{
  list-style: none;
  font-family: MicrosoftYaHei;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  //font-weight: normal;
  text-transform: none;
}
.panel-left-1 .projectInfoUl .el-col{
  line-height: 48px;
}
.panel-right-1 .projectInfoUl li span{
  margin-left: 12px;
  line-height: 35px;
}
.jiJuTable{
  width: 100%;
  font-family: MicrosoftYaHei;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  word-wrap: anywhere;
}
.jiJuTable tr{
  height: 40px;
}
.jiJuTable .tableHead{
  position:sticky;top:0;
}

</style>

<style lang="scss">
/* 车辆超出边界报警边框闪烁样式 */
@keyframes borderBlink {
  0% {
    border-color: red; /* 边框颜色为红色 */
    opacity: 1; /* 完全可见 */
  }
  50% {
    border-color: transparent; /* 边框颜色变为透明 */
    opacity: 1; /* 仍然可见 */
  }
  100% {
    border-color: red; /* 边框颜色恢复为红色 */
    opacity: 1; /* 完全可见 */
  }
}

/* 应用到 marker 的样式 */
.marker-border {
    border: 2px solid red; /* 初始边框样式 */
    animation: borderBlink 1s infinite; /* 使用 borderBlink 动画，持续时间为 1 秒，循环播放 */
}

.truck_info_panel {
  position: absolute;
  user-select: none;
  background: #020F2299;
  box-shadow: 0px 0px 18px 0px #0A5075, inset 0px 0px 35px 0px #4F91B3;
  border-radius: 4px 4px 4px 4px;
  border: 2px solid #0273AE;
  z-index: 1235;
  width: 497px;
  height: 343px;
  padding: 15px;
}
.truck_info-header{
  position: relative;
  width: 100%;
  height: 40px;
  background-image: url("../../../assets/onemap/images/title.png");
  background-repeat: no-repeat;
}

.truck_info-header-text{
  margin-left: 18%;
  font-family: AlimamaShuHeiTi, AlimamaShuHeiTi;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  line-height: 38px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.truck_info-header-icon{
  float: right;
  font-weight: bold;
  font-size: 28px;
  line-height: 38px;
  color: #FFFFFF;
  padding-right: 20px;
}

.truckInfoUl{
  list-style: none;
  font-family: MicrosoftYaHei;
  font-size: 16px;
  color: #FFFFFF;
  text-align: left;
  //font-weight: normal;
  text-transform: none;
}
.truck_info_panel .truckInfoUl li span{
  margin-left: 12px;
  line-height: 35px;
}
.truck_info-footer{
  text-align: center;
}
.truck_info-footer-button{
  width: 120px;
  height: 40px;
  padding: 5px;
  background: #165f9b;
  border-radius: 5px;
  border: 2px solid #0273AE;
  color: #FFFFFF;
  font-size: 18px;
  cursor: pointer;
}
.leaflet-popup-content-wrapper {
    background: transparent !important; /* 去掉背景 */
    border: none !important; /* 去掉边框 */
    box-shadow: none !important; /* 去掉阴影 */
}
.leaflet-popup-tip {
    display: none; /* 隐藏箭头 */
}
</style>
