<template>
  <div>
    <el-tabs type="border-card" v-model="activeName">
      <el-tab-pane name="first" label="采砂项目监管">
        <csindex v-if="activeName=='first'"></csindex>
      </el-tab-pane>
     <!-- <el-tab-pane name="second" label="涉河项目监管" v-if="status==1">
        <shindex v-if="activeName=='second'"></shindex>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
  import csindex from './csindex.vue';
  // import shindex from './inspection/index.vue';
  export default {
    name: "Index",
    components: {
      csindex,
      // shindex
    },
    data() {
      return {
        activeName:'first',
        status:0
      };
    },
    watch: {},
    computed: {},
    created() {
      // this.getProjectList1();
      console.log(this.$store.state.user.rolenames,'哲哥哥哥哥')
      const roles = this.$store.state.user.rolenames;
      if (roles.indexOf('涉河管理员') !== -1 || roles.indexOf('涉河') !== -1) {
        // 存在目标角色
        this.status=1;
      }else{
         this.status=0;
      }
    },
    mounted() {},
    methods: {
      handleClick(){}
    }
  };
</script>

<style>
</style>
