<template>
    <div class="app-container">
        <!-- 搜索工作栏 -->
        <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="市/县" prop="cityName">
                <el-input v-model="queryParams.cityName" placeholder="请输入市/县名称" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery()">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form> -->
        <!-- 列表 -->
        <el-table v-loading="loading" :data="list" size="mini" stripe border row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :header-cell-style="{ textAlign: 'center' }" height='540px'>
            <el-table-column label="地区/项目名称" prop="areaName" show-overflow-tooltip />
            <el-table-column v-if="title == '累计运砂量'" label="采砂量（吨）" prop="totalLoad" show-overflow-tooltip
                align="center" />
            <el-table-column v-if="title == '在运单数'" label="在线运单（个）" prop="transitBill" show-overflow-tooltip
                align="center" />
            <el-table-column v-if="title == '核销单数'" label="核销单数（个）" prop="completedBill" show-overflow-tooltip
                align="center" />
            <el-table-column v-if="title == '项目数量'" label="项目数量（个）" prop="proCount" show-overflow-tooltip
                align="center" />
            <el-table-column v-if="title == '运砂人数量'" label="运砂人数量（人）" prop="driverCount" show-overflow-tooltip
                align="center" />
        </el-table>
        <!-- 分页组件
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize"
            @pagination="handleQuery" /> -->
    </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { getYunSha, getYunShaRen } from "@/api/home/<USER>";
export default {
    name: "YunSha",
    components: {
        Treeselect
    },
    props: {
        title: {
            type: String,
            default: ''
        },
        contents: {
            type: Object,
            default: {}
        },
        timeRange: {
            type: Array,
            default: []
        },
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 公司列表
            list: [],
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                cityName: null
            },
        };
    },
    computed: {
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询列表 */
        getList() {
            this.loading = true;
            // 执行查询
            // let queryParams=
            console.log(this.contents,'这个是')
            let areaCode=this.contents.areaCode;
            if(this.contents.code==''||this.contents.code==null||this.contents.code==undefined){
              console.log('到这里了')
              code='000_013';
            }
            getYunShaRen(this.timeRange?this.timeRange[0]:'', this.timeRange?this.timeRange[1]:'', areaCode, this.contents.projectId).then(response => {
                this.list = response.list;
                this.loading = false;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
    }
};
</script>
<style type="scss" scoped>
.input-full {
    width: 100%;
}

.clearfix {
    >span {
        font-size: 14px;
        font-weight: 600;
    }
}

.box-card {
    margin-bottom: 12px;
}
</style>
