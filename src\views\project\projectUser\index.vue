<template>
  <div class="app-container">
    <!--查询条件-->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="用户名" prop="username">
        <el-input v-model.trim="queryParams.username" placeholder="用户名或姓名" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model.trim="queryParams.mobile" placeholder="手机号码" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!--操作行-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="batchDelete"
          v-hasPermi="['project:projectUser:delete']">删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!-- table表格 -->
    <my-table url="project/projectUser/page" ref="userTable" row-key="userId"
      @my-selection-change="handleSelectionChange">
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="姓名" align="center" prop="showName" />
      <el-table-column label="手机号码" align="center" prop="mobile" />
      <el-table-column label="角色" align="center" prop="roleNames" />
      <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" />
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1" size="mini" title="锁定" class="btn-table-operate" icon="el-icon-lock"
            type="danger" @click="handleLock(scope.row)" v-hasPermi="['project:projectUser:lock']">
          </el-button>
          <el-button v-if="scope.row.status === 0" size="mini" title="解锁" class="btn-table-operate"
            icon="el-icon-unlock" type="primary" @click="handleUnLock(scope.row)"
            v-hasPermi="['project:projectUser:unlock']">
          </el-button>
          <el-button size="mini" title="重置密码" class="btn-table-operate" icon="el-icon-key" type="primary"
            @click="handleResetPwd(scope.row)" v-hasPermi="['project:projectUser:update']"></el-button>
          <el-button size="mini" title="删除" class="btn-table-operate" type="danger" icon="el-icon-delete"
            @click="handleDelete(scope.row)" v-hasPermi="['project:projectUser:delete']"></el-button>
        </template>
      </el-table-column>
    </my-table>
    <!-- 添加或修改用户配置对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="600px" height="70vh" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row>
          <el-col :span="12">
            <my-form-item v-if="form.userId === ''" label="用户名" ref="username" prop="username"
              :rules="[{ notNull: true, message: '用户名不能为空' }, { regExp: /^[\u4e00-\u9fa5|\da-zA-Z]+$/, message: '只能使用中文、字母或数字' }, { regExp: /[^\d]/, message: '用户名不能是纯数字' }, { fn: this.verifyUsername, message: '用户名已存在' }]">
              <el-input v-model="form.username" placeholder="请输入用户名" maxlength="30" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item v-if="form.userId === ''" label="密码" ref="password" prop="password" :rules="passwordRules">
              <el-input v-model="form.password" placeholder="请输入密码" type="password" maxlength="20" show-password />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="姓名" ref="showName" prop="showName"
              :rules="[{ required: true, message: '姓名不能为空', trigger: 'blur' }]">
              <el-input v-model="form.showName" placeholder="请输入姓名" maxlength="30" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别">
              <el-radio-group v-model="form.sex">
                <el-radio v-for="item in sexList" :key="item.value" :label="item.value">
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!--          <el-col :span="24">-->
          <!--            <my-form-item label="部门" ref="deptId" prop="deptId" :rules="[{ required: true, message: '所属部门不能为空' }]">-->
          <!--              <treeselect-->
          <!--                v-model="form.deptId"-->
          <!--                :options="deptOptions"-->
          <!--                placeholder="请选择部门"-->
          <!--                :normalizer="normalizerDept"-->
          <!--                @select="deptTreeSelect"-->
          <!--              />-->
          <!--            </my-form-item>-->
          <!--          </el-col>-->
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="手机号码" prop="mobile" :rules="[{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' },
            { fn: this.checkMobile, message: '手机号码重复' }]">
              <el-input v-model="form.mobile" placeholder="请输入手机号码" maxlength="11" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="邮箱" prop="email" :rules="[{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] },
            { fn: this.checkEmail, message: '邮箱地址重复' }]">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属区域" prop="areaId">
              <treeselect v-model="form.areaId" :options="areaOptions" placeholder="请选择区域" :normalizer="normalizerArea"
                @select="areaTreeSelect" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入详细地址" maxlength="128" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <my-form-item label="角色" prop="roleIdList"
              :rules="[{ required: true, message: '请选择角色', trigger: 'change' }]">
              <el-checkbox-group v-model="form.roleIdList">
                <el-checkbox v-for="item in roleList" :key="item.roleId" :label="item.roleId">
                  {{ item.roleName }}
                </el-checkbox>
              </el-checkbox-group>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <my-form-item label="数据权限" prop="dataGroupIdList"
              :rules="[{ required: true, message: '请选择数据权限', trigger: 'change' }]">
              <el-checkbox-group v-model="form.dataGroupIdList">
                <el-checkbox v-for="item in dataGroupList" :key="item.id" :label="item.id">
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio v-model="form.status" :label="1">正常</el-radio>
              <el-radio v-model="form.status" :label="0">禁用</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import {
  batchDelUser, addUser, updateUser,
  resetUserPwd, getDataGroupData, checkMobile, checkEmail, checkUserName, changeUserStatus
} from '@/api/system/user'
import { getDeptTreeData } from '@/api/system/dept';
import { getRoleListData } from '@/api/system/role';
import { getAreaTreeData } from '@/api/system/area';
import { handleTree } from '@/utils/ruoyi';
import configUtils from '@/utils/configUtils';
import dictUtils from '@/utils/dictUtils';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { batchDelProjectUser, delProjectUser, resetProjectUserPwd } from '@/api/project/projectUser'
export default {
  name: 'ProjectUserManage',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      search: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: [],
      normalizerDept(node) {
        return {
          id: node.deptId,
          label: node.name,
          children: node.children
        };
      },
      // 区域树选项
      areaOptions: undefined,
      normalizerArea(node) {
        return {
          id: node.id,
          label: node.areaName,
          children: node.children
        };
      },
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 密码配置项
      passwordPolicy: '',
      // 角色选项
      roleList: [],
      roleSelectList: [],
      // 数据权限选项
      dataGroupList: [],
      // 性别选项
      sexList: [],
      // 表单参数
      form: {
        roleIdList: [],
        dataGroupIdList: []
      },
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        username: null,
        mobile: ''
      },
    };
  },
  mounted() {
    this.getDeptTreeselect();
    // this.getAreaTreeselect();
    this.getRoleList();
    this.getDataGroupList();
    //调用系统配置类
    configUtils.getConfigValue('system.passwordPolicy').then(r => {
      this.passwordPolicy = r;
    });
    //调用字典工具类
    dictUtils.cList('sex').then(r => {
      this.sexList = r.sex;
    });
  },
  computed: {
    passwordRules: function () {
      if (this.passwordPolicy == 'false') {
        return [{ notNull: true, message: '密码不能为空' }, { regExp: /^[^\u4e00-\u9fa5]{0,}$/, message: '密码不可包含汉字' }];
      } else {
        return [{ notNull: true, message: '密码不能为空' }, {
          regExp: /(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,
          message: '必须包含字母和数字，长度8-30'
        }, { regExp: /^[^\u4e00-\u9fa5]{0,}$/, message: '密码不可包含汉字' }];
      }
    },
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    /** 查询用户列表 */
    getList(restart) {
      this.single = true;
      this.multiple = true;
      this.$refs.userTable.search(this.queryParams, restart);
    },
    /** 查询部门下拉树结构 */
    getDeptTreeselect() {
      getDeptTreeData().then(response => {
        this.deptOptions = handleTree(response.deptList, 'deptId', 'parentId');
      });
    },
    /** 选中部门回调事件 */
    deptTreeSelect(node) {
      this.$refs.form.clearValidate("deptId");
      this.form.deptId = node.deptId;
      this.form.deptCode = node.deptCode;
      this.form.deptName = node.name;
    },
    /** 查询区域下拉树结构 */
    getAreaTreeselect() {
      getAreaTreeData().then(response => {
        this.areaOptions = handleTree(response.areaList, 'id', 'parentCode');
      });
    },
    /** 选中区域回调事件 */
    areaTreeSelect(node) {
      this.form.areaId = node.id;
      this.form.areaCode = node.areaCode;
    },
    getRoleList() {
      getRoleListData().then(response => {
        this.roleList = response.list;
        this.roleSelectList = response.list.map(function (e) {
          return { 'name': e.roleName, 'value': e.roleId };
        });
      });
    },
    getDataGroupList() {
      getDataGroupData().then(response => {
        this.dataGroupList = response.list;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: '',
        deptId: undefined,
        deptCode: undefined,
        deptName: undefined,
        areaId: undefined,
        areaCode: undefined,
        username: undefined,
        showName: undefined,
        password: undefined,
        mobile: undefined,
        email: undefined,
        status: 1,
        sex: 'male',
        remark: '',
        roleIdList: [],
        dataGroupIdList: []
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange() {
      this.ids = this.$refs.userTable.getSelectRowKeys();
      this.single = this.ids.length !== 1;
      this.multiple = !this.ids.length;
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      /* this.$prompt('请输入"' + row.username + '"的新密码', '提示', {
         confirmButtonText: '确定',
         cancelButtonText: '取消',
         closeOnClickModal: false,
         inputPattern: /^.{5,20}$/,
         inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
       }).then(({ value }) => {
         resetUserPwd(row.userId, value).then(response => {
           this.$modal.msgSuccess('修改成功，新密码是：' + value)
         })
       }).catch(() => {})*/
      this.$modal.confirm('确定要重置' + row.showName + '的密码吗？').then(function () {
        return resetProjectUserPwd(row.userId);
      }).then(response => {
        this.getList();
        alert(response.msg);
      }).catch(() => {
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      let that = this;
      that.$refs['form'].validate((valid, errorObj) => {
        if (valid) {
          that.$modal.loading('加载中');
          if (that.form.userId !== '') {
            let cloneForm = JSON.parse(JSON.stringify(that.form));
            if (cloneForm.areaId === undefined) {
              cloneForm.areaId = '';
              cloneForm.areaCode = '';
            }
            updateUser(cloneForm).then(response => {
              that.$modal.msgSuccess('修改成功');
              that.$modal.closeLoading();
              that.open = false;
              that.getList();
            }).catch(that.$modal.closeLoading);
          } else {
            addUser(that.form).then(response => {
              that.$modal.msgSuccess('新增成功');
              that.$modal.closeLoading();
              that.open = false;
              that.getList();
            }).catch(that.$modal.closeLoading);
          }
        } else {
          this.$scrollView(errorObj);
        }
      });
    },
    /** 上锁按钮操作 */
    handleLock(row) {
      this.$modal.confirm('确定要锁定' + row.showName + '吗？').then(function () {
        changeUserStatus(row.userId, row.status);
      }).then(response => {
        this.getList();
        this.$modal.msgSuccess("操作成功");
      }).catch(() => {
      });
    },
    /** 解锁按钮操作 */
    handleUnLock(row) {
      this.$modal.confirm('确定要解锁' + row.showName + '吗？').then(function () {
        changeUserStatus(row.userId, row.status);
      }).then(response => {
        this.getList();
        this.$modal.msgSuccess("操作成功");
      }).catch(() => {
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal.confirm('是否确认删除该用户？').then(function () {
        return delProjectUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    },
    /** 批量删除按钮操作 */
    batchDelete() {
      const userIds = this.$refs.userTable.getSelectRowKeys();
      this.$modal.confirm('是否确认删除该用户？').then(function () {
        return batchDelProjectUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    },
    checkMobile() {
      var that = this;
      return new Promise(function (resolve, reject) {
        if (!that.form.mobile) {
          resolve();
        }
        checkMobile(that.form.mobile, that.form.userId).then(r => {
          if (r.result) {
            resolve();
          } else {
            reject();
          }
        }).catch(reject)
      })
    },
    checkEmail() {
      var that = this;
      return new Promise(function (resolve, reject) {
        if (!that.form.email) {
          resolve();
        }
        checkEmail(that.form.email, that.form.userId).then(r => {
          if (r.result) {
            resolve();
          } else {
            reject();
          }
        }).catch(reject)
      })
    },
    //验证用户名是否重复
    verifyUsername() {
      if (!this.form.username) {
        return Promise.resolve();
      }
      var that = this;
      return new Promise((resolve, reject) => {
        checkUserName(that.form.username, that.form.userId)
          .then(r => r.result ? resolve() : reject()).catch(reject);
      })
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.userTable.changeTableHeight();
  },
};
</script>

<style scoped>
/deep/ .my-dialog__body {
  max-height: 75vh;
  overflow: auto;
}
</style>
