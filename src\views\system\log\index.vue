<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="55px"
      @submit.native.prevent
    >
      <el-form-item label="用户名" prop="key">
        <el-input
          v-model="queryParams.key"
          placeholder="用户名/用户操作/请求参数"
          clearable
          style="width: 200px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        class="rightToolbar"
        :search.sync="search"
        :showSearch.sync="showSearch"
        @queryTable="getList">
      </right-toolbar>
    </el-row>
    <my-table url="/sys/log/list" ref="sysLogTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column min-width="70" label="用户名" align="center" prop="username" column-key="USERNAME" sortable="custom"/>
      <el-table-column min-width="70" label="IP地址" align="center" prop="ip" column-key="IP" sortable="custom"/>
      <el-table-column min-width="80" label="用户操作" align="center" prop="operation" column-key="OPERATION" sortable="custom"/>
      <el-table-column min-width="200" label="请求方法" align="center" prop="method" column-key="METHOD" sortable="custom"/>
      <el-table-column min-width="220" label="请求参数" align="center" prop="params" column-key="PARAMS" sortable="custom">
        <template v-slot="scope">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" class="toolTipStyle">{{scope.row.params}}</div>
            <div class="hiddenParams">{{scope.row.params}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column min-width="90" label="执行时长(ms)" align="center" prop="time" column-key="Time"  sortable="custom"/>
      <el-table-column min-width="120" label="创建时间" align="center" prop="createDate" column-key="CREATE_DATE" sortable="custom"/>
    </my-table>
  </div>
</template>
<script>
import Template from '../../sms/template';
export default {
  name: 'Log',
  components: { Template },
  data() {
    return {
      loading: true,
      search: true,  //控制显隐查询
      showSearch: true,  //显示搜索条件
      queryParams: {
        key: '',
      }
    }
  },
  methods: {
    //查询操作日志列表
    getList(restart) {
      this.$refs.sysLogTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    //搜索操作
    handleQuery() {
      this.getList()
    },
    //重置操作
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    //多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
  }
}
</script>
<style scoped>
.hiddenParams {
  overflow: hidden;
  text-align: justify;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.toolTipStyle {
  min-width: auto;
  max-width: 550px;
  min-height: auto;
  max-height:400px;
  overflow: auto;
}
</style>
