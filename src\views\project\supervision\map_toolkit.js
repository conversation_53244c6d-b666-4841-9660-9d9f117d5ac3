const IS_4326 = false;

//const TOKEN_TDT = "e876fd4678958170f2dbf8151806eaad";
const TOKEN_TDT = "9801a6af2d3ca13c21b3a8cf18cdfe3a";
const MAX_ZOOM = 22;
const DEFAULT_DURATION = 1;
const VIEW_YDH = { center: [40.14873573186948,114.6205703385453], zoom: 8.7, };
const VIEW_HEBEI = { center: [39.39697265625,116.08732090239953], zoom: 7, };
const VIEW_YEHE = { center: [38.137522475469815,114.12983268467774], zoom: 11.5, };
const DASHAHE_YEHE = { center: [36.98113172476122,114.27611100255137], zoom: 13, };
const VIEW_CHINA = { center: [36.03496872129995, 103.11767578125], zoom: 4, };
const SERVER_SM = "http://219.148.61.135:8090/iserver/services/";
// const SERVER_SM = "http://192.168.10.103:8090/iserver/services/";

let K = {
  viewer: null,
  wmts: L.TileLayer.extend({
    /** Default params for WMTS */
    defaultWmtsParams: {
      service: 'WMTS',
      request: 'GetTile',
      version: '1.0.0',
      layer: '',
      style: '',
      tilematrixset: '',
      format: 'image/png',
    },


    /**
     * Initialize plugin
     * @param {String} url Url to WMTS server
     * @param {Object} options List options
     */
    initialize(url, options) {
      this._url = url;

      const lOptions = {};
      const cOptions = Object.keys(options);
      cOptions.forEach(element => (
        lOptions[element.toLowerCase()] = options[element]
      ));

      const wmtsParams = L.extend({}, this.defaultWmtsParams);
      const tileSize = lOptions.tileSize || this.options.tileSize;

      if (lOptions.detectRetina && L.Browser.retina) {
        wmtsParams.width = tileSize * 2;
        wmtsParams.height = tileSize * 2;
      } else {
        wmtsParams.width = tileSize;
        wmtsParams.height = tileSize;
      }

      for (const i in lOptions) {
        // all keys that are in defaultWmtsParams options go to WMTS params
        // eslint-disable-next-line no-prototype-builtins
        if (wmtsParams.hasOwnProperty(i) && i !== 'matrixIds') {
          wmtsParams[i] = lOptions[i];
        }
      }

      this.wmtsParams = wmtsParams;
      this.matrixIds = options.matrixIds || this.getDefaultMatrix();

      L.setOptions(this, options);
    },


    /**
     * Set tile to map
     * @param {Leaflet.Map} map Leaflet map
     */
    onAdd(map) {
      this._crs = this.options.crs || map.options.crs;
      L.TileLayer.prototype.onAdd.call(this, map);
    },

    /*8888 20220906 试验超图瓦片的封装*/
    // createTile(coords, done) {
    //   const url = this.getTileUrl(coords);
    //   const img = document.createElement("img");
    //   img.setAttribute("role", "presentation");
    //   img.setAttribute("data-url", url);

    //   console.log(coords, url);
    //   console.log(1111);
    //   fetch(url, {
    //       method: "GET",
    //       // headers: {"whoareyou": "ourix"},
    //       mode: "cors",
    //       signal: new AbortController().signal
    //     }).then(async f => {
    //       const blob = await f.blob();
    //       const reader = new FileReader();
    //       reader.readAsDataURL(blob);
    //       reader.onload = () => {
    //         img.src = reader.result;
    //         img.className = "leaflet-tile leaflet-tile-loaded";
    //       };
    //     });
    //   console.log(2222);
    //   return img;
    // },

    /**
     * Generate URL for tile pieces
     * @param {Leaflet.Point} coords Position tile
     * @param {Number} coords.x Position X
     * @param {Number} coords.y Position Y
     * @param {Number} coords.z Position Z
     * @return {String} URL
     */
    getTileUrl(coords) {
      const tileSize = this.options.tileSize;

      const nwPoint = coords.multiplyBy(tileSize);
      nwPoint.x += 1;
      nwPoint.y -= 1;

      const sePoint = nwPoint.add(new L.Point(tileSize, tileSize));
      const zoom = this._tileZoom;
      const se = this._crs.project(this._map.unproject(sePoint, zoom));
      const nw = this._crs.project(this._map.unproject(nwPoint, zoom));
      const tilewidth = se.x - nw.x;

      const ident = this.matrixIds[zoom].identifier;
      // const tilematrix = `${this.wmtsParams.tilematrixset}:${ident}`;
      const tilematrix = `${ident}`;
      const X0 = this.matrixIds[zoom].topLeftCorner.lng;
      const Y0 = this.matrixIds[zoom].topLeftCorner.lat;
      const tilecol = Math.floor((nw.x - X0) / tilewidth);
      const tilerow = -Math.floor((nw.y - Y0) / tilewidth);

      const url = L.Util.template(this._url, {
        s: this._getSubdomain(coords),
      });

      return `${url}${L.Util.getParamString(this.wmtsParams, url)}` +
        `&tilematrix=${tilematrix}&tilerow=${tilerow}&tilecol=${tilecol}`;
    },


    /**
     * Set params
     * @param {Object} params Params
     * @param {Boolean} noRedraw needed redraw map
     */
    setParams(params, noRedraw) {
      L.extend(this.wmtsParams, params);

      if (!noRedraw) {
        this.redraw();
      }

      return this;
    },


    /**
     * Generate default matrix
     * @description The matrix3857 represents the projection
     * for in the IGN WMTS for the google coordinates.
     */
    getDefaultMatrix() {
      const matrixIds3857 = new Array(22);

      for (let i = 0; i < 22; i++) {
        matrixIds3857[i] = {
          identifier: String(i),
          topLeftCorner: new L.LatLng(20037508.3428, -20037508.3428),
        };
      }

      return matrixIds3857;
    },
  }),
  rest: L.TileLayer.extend({
    /** Default params for REST */
    defaultWmtsParams: {
      service: 'REST',
      request: 'GetTile',
      version: '1.0.0',
      layer: '',
      style: '',
      tilematrixset: '',
      format: 'image/png',
    },
    initialize(url, options) {
      this._url = url;

      const lOptions = {};
      const cOptions = Object.keys(options);
      cOptions.forEach(element => (
        lOptions[element.toLowerCase()] = options[element]
      ));

      const wmtsParams = L.extend({}, this.defaultWmtsParams);
      const tileSize = lOptions.tileSize || this.options.tileSize;

      if (lOptions.detectRetina && L.Browser.retina) {
        wmtsParams.width = tileSize * 2;
        wmtsParams.height = tileSize * 2;
      } else {
        wmtsParams.width = tileSize;
        wmtsParams.height = tileSize;
      }

      for (const i in lOptions) {
        // all keys that are in defaultWmtsParams options go to WMTS params
        // eslint-disable-next-line no-prototype-builtins
        if (wmtsParams.hasOwnProperty(i) && i !== 'matrixIds') {
          wmtsParams[i] = lOptions[i];
        }
      }

      this.wmtsParams = wmtsParams;
      this.matrixIds = options.matrixIds || this.getDefaultMatrix();

      L.setOptions(this, options);
    },
    onAdd(map) {
      this._crs = this.options.crs || map.options.crs;
      L.TileLayer.prototype.onAdd.call(this, map);
    },
    getTileUrl(coords) {
      // 计算origin
      let origin = "%7B%22x%22%3A8015003.337115704%2C%22y%22%3A7558415.655908377%7D";
      origin = this.getOrigin();
      const url = L.Util.template(this._url, {
        s: this._getSubdomain(coords),
      });

      return `${url}`
        + `&origin=` + this.getOrigin()
        + `&scale=` + this.getScale(coords)
        + `&x=` + coords.x + `&y=` + coords.y;
    },
    /**
     * Set params
     * @param {Object} params Params
     * @param {Boolean} noRedraw needed redraw map
     */
    setParams(params, noRedraw) {
      L.extend(this.wmtsParams, params);

      if (!noRedraw) {
        this.redraw();
      }

      return this;
    },
    /**
     * Generate default matrix
     * @description The matrix3857 represents the projection
     * for in the IGN WMTS for the google coordinates.
     */
    getDefaultMatrix() {
      const matrixIds3857 = new Array(22);
      for (let i = 0; i < 22; i++) {
        matrixIds3857[i] = {
          identifier: String(i),
          topLeftCorner: new L.LatLng(20037508.3428, -20037508.3428),
        };
      }
      return matrixIds3857;
    },
    getOrigin(coords) {
      var crs = this._crs;
      if (crs.options && crs.options.origin) {
          return JSON.stringify({
              x: crs.options.origin[0],
              y: crs.options.origin[1]
          });
      } else if (crs.projection && crs.projection.bounds) {
          var bounds = crs.projection.bounds;
          var tileOrigin = L.point(bounds.min.x, bounds.max.y);
          return JSON.stringify({
              x: tileOrigin.x,
              y: tileOrigin.y
          });
      }
    },
    getScale(coords) {
      var scale;
      if (this.scales && this.scales[coords.z]) {
          return this.scales[coords.z];
      }
      this.scales = this.scales || {};

      var crs = this._crs;
      var tileBounds = this._tileCoordsToBounds(coords);
      var ne = crs.project(tileBounds.getNorthEast());
      var sw = crs.project(tileBounds.getSouthWest());
      var tileSize = this.options.tileSize;
      var resolution = Math.max(
          Math.abs(ne.x - sw.x) / tileSize,
          Math.abs(ne.y - sw.y) / tileSize
      );
      var inchPerMeter = 1 / 0.0254;
      scale = resolution * 96 * inchPerMeter;
      scale = 1 / scale;

      this.scales[coords.z] = scale;
      return scale;
    }
  }),
};
K.initMap = function (idDiv,projectId) {
  //写死的配置，第一个是冶河的，第二个是大沙河的
  let configIds=process.env.VUE_APP_PROJECT_ID;
  let center = configIds.indexOf(projectId) > 10 ? DASHAHE_YEHE.center : VIEW_YEHE.center;
  let zoom = configIds.indexOf(projectId) > 10 ? DASHAHE_YEHE.zoom : VIEW_YEHE.zoom;
	K.viewer = L.map(idDiv, {

    //视野控制在河北
    center: center,
    zoom: zoom,

		maxZoom: MAX_ZOOM,
		minZoom: 4,
		zoomSnap: 0.1,
		zoomDelta: 0.1,
		trackResize: true,
		maxBounds: L.latLngBounds(L.latLng(6.5417496775025175, 69.58659667193923), L.latLng(55.95172994170497, 130.49169247335865)),
    wheelPxPerZoomLevel: 120,
		boxZoom: true,
		crs: IS_4326 ? L.CRS.EPSG4326 : L.CRS.EPSG3857,
		zoomControl: false, // 按钮地图缩放
		logoControl: false, // logo
		attributionControl: false // 版权
	});

	L.control.scale({maxWidth:200, metric:true, imperial:false}).addTo(K.viewer);
}
K.initWatermark = function () {
	// 创建网格
	var layer = L.gridLayer({
		zIndex: 49,
		maxZoom: MAX_ZOOM,
		opacity: 0.33,
	});
	layer.createTile = function(coords) {
		var tile = L.DomUtil.create("canvas", "leaflet-tile");
		var context = tile.getContext("2d");
		var size = this.getTileSize();
		tile.width = size.x;
		tile.height = size.y;


		context.fillStyle = "#ffffff";
		context.font = "12px 微软雅黑";
		// context.fillText(process.env.VUE_APP_TITLE, 106, 220);
		// context.fillText("@刘庭集团", 180, 250);
		context.strokeStyle = "#ffffff";
		context.beginPath();
		context.moveTo(0, 0);
    let stroke = 0.1;
		context.lineTo(size.x - stroke, 0);
		context.lineTo(size.x - stroke, size.y - stroke);
		// context.lineTo(0, size.y - stroke);
		// context.closePath();
		context.stroke();
		return tile;
	}
	layer.addTo(K.viewer);
}
K.createLayerGroup = function () {
  return new L.LayerGroup();
}
K.createLayerRestSm = function(nameService, nameLayer, zIndex, token) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same CRS. ")
  }
  //http://219.148.61.135:8090/iserver/services/
  let url = SERVER_SM + nameService + "/rest/maps/" + nameLayer + "/tileImage.png?token=" + token;
  url += "width=256&height=256&redirect=false";
  url += "&transparent=true&cacheEnabled=true&overlapDisplayed=false";
  let layer = new K.rest(url, {
    layer: nameLayer,
    style: "default",
    // tilematrixSet: "Custom_MAP-HL",
    tilematrixSet: IS_4326 ? ("" + nameLayer) : ("GoogleMapsCompatible_" + nameLayer),
    format: "image/png",

    maxZoom: MAX_ZOOM,
    maxNativeZoom: 18,
    tileSize: 256,
    zoomOffset: 1,
    zIndex: zIndex,
    minZoom: 1,
  });
  return layer;
}
/**
 * 以单幅图像的方式调用超图rest瓦片服务，须在map.on("move")里进行重复调用
 * @param {Object} nameService
 * @param {Object} nameLayer
 * @param {Object} zIndex
 */
K.createLayerRestSm_singleImaged = function(nameService, nameLayer, zIndex) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same lane. ")
  }
  let size = K.viewer.getSize();
  let pixelBounds = K.viewer.getPixelBounds();
  let sw = K.viewer.unproject(pixelBounds.getBottomLeft());
  let ne = K.viewer.unproject(pixelBounds.getTopRight());
  let top = K.viewer.latLngToLayerPoint(ne).y;
  let bottom = K.viewer.latLngToLayerPoint(sw).y;
  if (top > 0 || bottom < size.y) {
      size.y = bottom - top;
  }

  var neProjected = K.viewer.options.crs.project(ne);
  var swProjected = K.viewer.options.crs.project(sw);
  let boundsProjected = L.bounds(neProjected, swProjected);

  var projBounds = {
      leftBottom: {
          x: boundsProjected.getBottomLeft().x,
          y: boundsProjected.getTopRight().y
      },
      rightTop: {
          x: boundsProjected.getTopRight().x,
          y: boundsProjected.getBottomLeft().y
      }
  };
  let viewBounds = JSON.stringify(projBounds);
  let url = SERVER_SM + nameService + "/rest/maps/" + nameLayer + "/image.png?transparent=true";
  url += "&width=" + size.x;
  url += "&height=" + size.y;
  url += "&viewBounds=" + viewBounds;

  let layer = new L.ImageOverlay(url, K.viewer.getBounds(), {
    // opacity: 0,
    zIndex: zIndex,
    interactive: false,
  });
  return layer;
}
K.createLayerWmtsSm = function(nameService, nameLayer, zIndex) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same CRS. ")
  }
  let layer = new K.wmts(SERVER_SM + nameService + "/wmts100", {
    layer: nameLayer,
    style: "default",
    // tilematrixSet: "Custom_MAP-HL",
    tilematrixSet: IS_4326 ? ("" + nameLayer) : ("GoogleMapsCompatible_" + nameLayer),
    format: "image/png",

    maxZoom: MAX_ZOOM,
    maxNativeZoom: 18,
    tileSize: 256,
    zoomOffset: 1,
    zIndex: zIndex,
    minZoom: 1,
  });
  return layer;
}
K.createLayerWmtsTdt = function (type, zIndex) {
  let base = "http://t0.tianditu.gov.cn/" + type + "_c/wmts?tk=" + TOKEN_TDT;
  if (IS_4326) {
    let url = base + "&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Style=default";
    url += "&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&Layer=" + type;
    let layer = L.tileLayer(url, {
      maxZoom: MAX_ZOOM,
      maxNativeZoom: 18,
      tileSize: 256,
      zoomOffset: 1,
      zIndex: zIndex,
      minZoom: 1,
    });
    return layer;
  } else {
    let layer = new K.wmts("http://t0.tianditu.gov.cn/" + type + "_w/wmts?tk=" + TOKEN_TDT, {
      layer: type,
      style: "default",
      tilematrixset: "w",
      format: "tiles",

      maxZoom: MAX_ZOOM,
      maxNativeZoom: 18,
      tileSize: 256,
      zoomOffset: 1,
      zIndex: zIndex,
      minZoom: 1,
    });
    return layer;
  }
}
K.switchLayer = function (layer, on) {
  if (on) {
    layer.addTo(K.viewer);
  } else {
    layer.removeFrom(K.viewer);
  }
}
K.setViewToBounds = function (bounds, duration) {
	if (bounds) {
		// 重新定位地图视角
		if (duration) {
			K.viewer.flyToBounds(bounds, {
				animate: true,
				duration: duration,
			});
		} else {
			K.viewer.flyToBounds(bounds, {
				animate: false,
			});
		}
	} else {
		K.setViewToCurrentLocation();
	}
}
K.setViewToCurrentLocation = function () {
	//
	// resetMapViewToLatLng([latInit, lngInit]);
}
K.setViewToLatLng = function (pos, duration) {
  let n = K.viewer.getBounds().getNorth();
  let s = K.viewer.getBounds().getSouth();
  let e = K.viewer.getBounds().getEast();
  let w = K.viewer.getBounds().getWest();

  let dx = e - w;
  let dy = n - s;

  let ll = L.latLng(pos.lat - dy * 0.2, pos.lng + dx * 0.1);
	K.viewer.panTo(ll, {
		animate: true,
		duration: duration ? duration : DEFAULT_DURATION,
	});
}
K.setViewToProject = function (pos) {
  K.viewer.setView(pos, 12.5, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.setViewToHebei = function() {
  K.viewer.setView(VIEW_HEBEI.center, VIEW_HEBEI.zoom, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.setViewToYDH = function() {
  K.viewer.setView(VIEW_YDH.center, VIEW_YDH.zoom, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.addBoundaryHebei = function(url) {
  L.geoJSON(url, {
  	style: function(feature) {
  		return {
  			color: "#008686",
  			opacity: 1,
  			weight: 4,
  			fill: true,
  			fillColor: "#008686",
  			fillOpacity: 0.2,
  			interactive: false,
  		};
  	},
  }).addTo(K.viewer);
}

export default K;
