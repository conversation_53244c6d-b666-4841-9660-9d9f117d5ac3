import request from '@/utils/request'

// 查询南水北调责任人报表详细
export function getDiversionPersonReport(id) {
  return request({
    url: '/reports/diversionPersonReport/info/' + id,
    method: 'post'
  })
}

// 新增南水北调责任人报表
export function addDiversionPersonReport(data) {
  return request({
    url: '/reports/diversionPersonReport/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改南水北调责任人报表
export function updateDiversionPersonReport(data) {
  return request({
    url: '/reports/diversionPersonReport/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除南水北调责任人报表
export function delDiversionPersonReport(id) {
  return request({
    url: '/reports/diversionPersonReport/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除南水北调责任人报表
export function delDiversionPersonReportBatch(ids) {
  return request({
    url: '/reports/diversionPersonReport/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

// 导入采砂监管责任人
export function importDiversionPersonReport(data,reportId,version) {
  return request({
    url: '/reports/diversionResponsiblePerson/diversionImport/'+reportId+'/'+version,
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//县级审核
export function checkPassXian(data) {
  return request({
    url: '/reports/diversionPersonReport/xianExamine',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//市级审核
export function checkPassShi(data) {
  return request({
    url: '/reports/diversionPersonReport/shiExamine',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//打回
export function diversionBack(data) {
  return request({
    url: '/reports/diversionPersonReport/shengBack',
    method: 'post',
    data: data,
    showLoading: true,
  })
}


