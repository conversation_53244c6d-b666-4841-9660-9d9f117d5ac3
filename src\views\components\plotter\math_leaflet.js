import * as turf from "@turf/turf";

export function toGeometry(type, lls) {
  if (lls.length == 0) {
    return undefined;
  }
	let types = [
		"Point",
		"LineString",
		"Polygon",
	];
	let coords = [];
	if (type == 0) {
		coords = [lls[0].lng, lls[0].lat];
	}
	if (type == 1) {
		for (let i = 0; i < lls.length; i++) {
			coords.push([lls[i].lng, lls[i].lat]);
		}
	}
	if (type == 2) {
		coords.push([]);
		for (let i = 0; i < lls.length; i++) {
			coords[0].push([lls[i].lng, lls[i].lat]);
		}
	}
	let result = {
		type: types[type],
		coordinates: coords,
	};
  return JSON.stringify(result);
}

export function fromGeometry(geojson) {
  let types = [
  	"POINT",
  	"LINESTRING",
  	"POLYGON",
  ];
  let obj = JSON.parse(geojson);
  let t = types.indexOf(obj.type.toUpperCase());
  let result = { type: t, points: [] };
  if (t == 0) {
    result.points.push( { lng: obj.coordinates[0], lat: obj.coordinates[1] } ) ;
  } else if (t == 1) {
    let cc = obj.coordinates;
    for (let c = 0; c < cc.length; c++) {
      result.points.push( { lng: cc[c][0], lat: cc[c][1] } ) ;
    }
  } else {
    let cc = obj.coordinates[0];
    for (let c = 0; c < cc.length - 1; c++) {
      result.points.push( { lng: cc[c][0], lat: cc[c][1] } ) ;
    }
  }
  return result;
}

export function isPolygonValid(lls) {
	let N = lls.length;
	if (N < 4) {
		return true;
	}
	for (let i = 0; i < N; i++) {
		let segA = [lls[i], lls[(i+1)%N]];
		for (let j = 0; j < N-3; j++) {
			let segB = [lls[(i+2+j)%N], lls[(i+3+j)%N]];
			let cross = isPairOfSegmentCrossing(segA, segB);
			if (isPairOfSegmentCrossing(segA, segB)) {
				return false;
			}
		}
	}
  return true;
}

function isPairOfSegmentCrossing(segA, segB) {
	var a = segA[0];
	var b = segA[1];
	var c = segB[0];
	var d = segB[1];

	var dad = substractPoint(a, d);
	var dcd = substractPoint(c, d);
	var dbd = substractPoint(b, d);
	var cadcd = crossPoint(dad, dcd);
	var cbdcd = crossPoint(dbd, dcd);
	var abovercd = cadcd * cbdcd < 0;

	var dda = substractPoint(d, a);
	var dba = substractPoint(b, a);
	var dca = substractPoint(c, a);
	var cdaba = crossPoint(dda, dba);
	var ccaba = crossPoint(dca, dba);
	var cdoverad = cdaba * ccaba < 0;

	return cdoverad && abovercd;
}

function crossPoint(pa, pb) {
	return pa.lat * pb.lng - pa.lng * pb.lat;
}

function substractPoint(pa, pb) {
	return {
		lat: pa.lat - pb.lat,
		lng: pa.lng - pb.lng,
	}
}
