import request from '@/utils/request'

// 查询运砂人白名单表详细
export function getDriver(id) {
  return request({
    url: '/project/driver/info/' + id,
    method: 'post'
  })
}

// 新增运砂人白名单表
export function addDriver(data) {
  return request({
    url: '/project/driver/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改运砂人白名单表
export function updateDriver(data) {
  return request({
    url: '/project/driver/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除运砂人白名单表
export function delDriver(id) {
  return request({
    url: '/project/driver/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除运砂人白名单表
export function delDriverBatch(ids) {
  console.log("111")
  return request({
    url: '/project/driver/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

// 下载导入模板
export function downloadTemplate(){
  return request({
    url:'/project/driver/downloadTemplate',
    method: 'post',
  })

}


// 导入运砂人信息
export function driverImport(data){
  return request({
    url:'/project/driver/driverImport',
    data: data,
    method: 'post',
  })
}

// 获取当前用户所在部门可查看的项目名称
export function listProjectByDeptId() {
  return request({
    url: '/project/project/listProjectByDeptId',
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

