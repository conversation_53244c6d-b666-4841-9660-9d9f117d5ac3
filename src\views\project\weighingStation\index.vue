<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:weighingStation:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:weighingStation:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:weighingStation:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/weighingStation/page" ref="weighingStationTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
  <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
  <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
  <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
  <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
  <el-table-column  label="项目ID" min-width="80" prop="projectId" sortable="custom" column-key="PROJECT_ID"></el-table-column>
  <el-table-column  label="磅站名称" min-width="80" prop="name" sortable="custom" column-key="NAME"></el-table-column>
  <el-table-column  label="经度" min-width="80" prop="longitude" sortable="custom" column-key="LONGITUDE"></el-table-column>
  <el-table-column  label="纬度" min-width="80" prop="latitude" sortable="custom" column-key="LATITUDE"></el-table-column>
  <el-table-column  label="详细地址;通过地图api获取" min-width="80" prop="address" sortable="custom" column-key="ADDRESS"></el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['project:weighingStation:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['project:weighingStation:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改磅站表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="weighingStation"  label-width="80px">
              <my-form-item label="创建人id" ref="createUserId" prop="createUserId" :rules="[{notNull:true,message:'请输入创建人id'}]">
        <my-input v-model="weighingStation.createUserId" placeholder="请输入创建人id" />
      </my-form-item>
              <my-form-item label="修改人id" ref="updateUserId" prop="updateUserId" :rules="[{notNull:true,message:'请输入修改人id'}]">
        <my-input v-model="weighingStation.updateUserId" placeholder="请输入修改人id" />
      </my-form-item>
              <my-form-item label="创建时间" ref="createTime" prop="createTime" :rules="[{notNull:true,message:'请输入创建时间'}]">
        <my-input v-model="weighingStation.createTime" placeholder="请输入创建时间" />
      </my-form-item>
              <my-form-item label="修改时间" ref="updateTime" prop="updateTime" :rules="[{notNull:true,message:'请输入修改时间'}]">
        <my-input v-model="weighingStation.updateTime" placeholder="请输入修改时间" />
      </my-form-item>
              <my-form-item label="项目ID" ref="projectId" prop="projectId" :rules="[{notNull:true,message:'请输入项目ID'}]">
        <my-input v-model="weighingStation.projectId" placeholder="请输入项目ID" />
      </my-form-item>
              <my-form-item label="磅站名称" ref="name" prop="name" :rules="[{notNull:true,message:'请输入磅站名称'}]">
        <my-input v-model="weighingStation.name" placeholder="请输入磅站名称" />
      </my-form-item>
              <my-form-item label="经度" ref="longitude" prop="longitude" :rules="[{notNull:true,message:'请输入经度'}]">
        <my-input v-model="weighingStation.longitude" placeholder="请输入经度" />
      </my-form-item>
              <my-form-item label="纬度" ref="latitude" prop="latitude" :rules="[{notNull:true,message:'请输入纬度'}]">
        <my-input v-model="weighingStation.latitude" placeholder="请输入纬度" />
      </my-form-item>
              <my-form-item label="详细地址;通过地图api获取" ref="address" prop="address" :rules="[{notNull:true,message:'请输入详细地址;通过地图api获取'}]">
        <my-input v-model="weighingStation.address" placeholder="请输入详细地址;通过地图api获取" />
      </my-form-item>
          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWeighingStation, delWeighingStation, delWeighingStationBatch,addWeighingStation, updateWeighingStation } from "@/api/project/weighingStation";

export default {
  name: "WeighingStation",
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      weighingStation: {},
    };
  },
  mounted() {
  },
  methods: {
    /** 查询磅站表列表 */
    reload(restart) {
      this.$refs.weighingStationTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.weighingStation = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        projectId: '',
        name: '',
        longitude: null ,
        latitude: null ,
        address: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.weighingStationTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加磅站表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getWeighingStation(id).then(r => {
        this.weighingStation = r.weighingStation;
        this.open = true;
        this.title = "修改磅站表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.weighingStation.id) {
            updateWeighingStation(this.weighingStation).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addWeighingStation(this.weighingStation).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delWeighingStation(row.id);
        }else{
          return delWeighingStationBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.weighingStationTable.changeTableHeight();
  },
};
</script>
