import request from '@/utils/request'

//查询登录日志列表
export function getListData(queryParams) {
  return request({
    url: '/sys/loginlog/list',
    method: 'post',
    params: queryParams
  })
}

//批量删除登录日志
export function batchDelLoginLog(ids) {
  return request({
    url: '/sys/loginlog/delete',
    method: 'post',
    data: ids
  })
}

//根据id删除单条记录
export function delOneLoginLog(id) {
  return request({
    url: '/sys/loginlog/delete/' + id,
    method: 'post',
  })
}
