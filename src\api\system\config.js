import request from '@/utils/request'

// 根据配置code，获取value
export function getConfigValueByCode(configCode) {
  return request({
    url:"sys/config/getConfigValue",
    method: 'post',
    params:{configCode: configCode},
    headers: {allowRepeatSubmit: true}
  })
}

// 查询配置类别
export function getCategoryList() {
  return request({
    url: '/sys/configitem/category',
    method: 'post'
  })
}

// 查询配置类别下的配置项
export function getListByConfigItem(configCategory) {
  return request({
    url: '/sys/config/list/'+configCategory,
    method: 'post'
  })
}

// 保存配置
export function addConfig(data,configCategory) {
  return request({
    url: '/sys/config/save/' + configCategory,
    method: 'post',
    data: data
  })
}

