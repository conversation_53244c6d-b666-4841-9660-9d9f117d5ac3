<template>
  <el-form ref="form" :model="user" label-width="80px">
    <my-form-item label="姓名" prop="showName" :rules="[{ required: true, message: '用户昵称不能为空', trigger: 'blur' }]">
      <el-input v-model="user.showName" maxlength="30" />
    </my-form-item>
    <my-form-item label="手机号码" prop="mobile" :rules="[{ required: true, message: '手机号码不能为空', trigger: 'blur' },
                                                        {pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message: '请输入正确的手机号码',trigger: 'blur'},
                                                        {fn: this.checkMobile, message: '手机号码重复'}]">
      <el-input v-model="user.mobile" maxlength="11" />
    </my-form-item>
    <my-form-item label="邮箱" prop="email" :rules="[{ required: true, message: '邮箱地址不能为空', trigger: 'blur' },
                                                    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] },
                                                    {fn: this.checkEmail, message: '邮箱地址重复'}]">
      <el-input v-model="user.email" maxlength="50" />
    </my-form-item>
    <el-form-item label="性别">
      <el-radio-group v-model="user.sex">
        <el-radio label="male">男</el-radio>
        <el-radio label="female">女</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserInfo,checkMobile,checkEmail } from "@/api/system/user";

export default {
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserInfo(this.user).then(response => {
            this.$modal.msgSuccess("修改成功");
          });
        }
      });
    },
    checkMobile(){
      var that = this;
      return new Promise(function(resolve, reject) {
        checkMobile(that.user.mobile,that.user.userId).then(r => {
          if (r.result) {
            resolve();
          } else {
            reject();
          }
        }).catch(reject)
      })
    },
    checkEmail(){
      var that = this;
      return new Promise(function(resolve, reject) {
        checkEmail(that.user.email,that.user.userId).then(r => {
          if (r.result) {
            resolve();
          } else {
            reject();
          }
        }).catch(reject)
      })
    },
    close() {
      this.$tab.closePage();
    }
  }
};
</script>
