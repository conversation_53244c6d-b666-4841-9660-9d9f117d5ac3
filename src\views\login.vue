<template>
  <div class="login">
<!--    <div v-show="showDiv=='login'">-->
<!--      <el-form  ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">-->
<!--        <div v-show="config.showWxLoginBtn" class='switch-container'>-->
<!--          <div @click="showWxLogin" class="btn-link switch-wrapper" type="button"><i aria-hidden="true"-->
<!--                                                                                        class="fa fa-qrcode switch-icon"></i>-->
<!--          </div>-->
<!--        </div>-->
<!--        <h3 class="title">{{config.webSiteName}}</h3>-->
<!--        <el-form-item prop="username">-->
<!--          <el-input-->
<!--            v-model="loginForm.username"-->
<!--            type="text"-->
<!--            auto-complete="off"-->
<!--            placeholder="账号"-->
<!--          >-->
<!--            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item prop="password">-->
<!--          <el-input-->
<!--            v-model="loginForm.password"-->
<!--            type="password"-->
<!--            auto-complete="off"-->
<!--            placeholder="密码"-->
<!--            @keyup.enter.native="handleLogin"-->
<!--          >-->
<!--            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item prop="code" v-if="captchaOnOff">-->
<!--          <el-input-->
<!--            v-model="loginForm.code"-->
<!--            auto-complete="off"-->
<!--            placeholder="验证码"-->
<!--            style="width: 63%"-->
<!--            @keyup.enter.native="handleLogin"-->
<!--          >-->
<!--            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />-->
<!--          </el-input>-->
<!--          <div class="login-code">-->
<!--            <img :src="codeUrl" @click="getCode" class="login-code-img"/>-->
<!--          </div>-->
<!--        </el-form-item>-->
<!--        <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>-->
<!--        <el-form-item style="width:100%;margin-bottom: 10px;">-->
<!--          <el-button-->
<!--            :loading="loading"-->
<!--            size="medium"-->
<!--            type="primary"-->
<!--            style="width:100%;"-->
<!--            @click.native.prevent="handleLogin"-->
<!--          >-->
<!--            <span v-if="!loading">登 录</span>-->
<!--            <span v-else>登 录 中...</span>-->
<!--          </el-button>-->
<!--          <div style="float: right;" v-if="register">-->
<!--            <router-link class="link-type" :to="'/register'">立即注册</router-link>-->
<!--          </div>-->
<!--        </el-form-item>-->
<!--        <p v-show="config.showWxLoginBtn" class="other_type">其他方式：<i @click="showWxLogin" class="fa fa-weixin"></i></p>-->
<!--      </el-form>-->
<!--    </div>-->

    <div class="main"  v-show="showDiv=='login'">
      <div class="login-main">
        <div class="title">
          {{ config.webSiteName }}
        </div>
        <el-form ref="loginForm" :model="loginForm" style="position: relative">
          <div v-show="config.showWxLoginBtn" class="switch-container">
            <div @click="showWxLogin" class="btn-link switch-wrapper" type="button"><i aria-hidden="true" class="fa fa-qrcode switch-icon"
            ></i>
          </div>
          </div>
            <div class="login-form">
            <div class="login-font">用户登录</div>
            <div class="login-user">
              <div>
                <my-form-item prop="username" :rules="[{notNull:true,message:'用户名不能为空'}]">
                  <input class="input" placeholder="用户名" v-model="loginForm.username">
                </my-form-item>
                <div class="input-user">
                  <my-form-item prop="password" :rules="[{notNull:true,message:'密码不能为空'}]">
                    <input class="input" type="password" autocomplete="new-password" v-model="loginForm.password"
                           @keyup.enter="handleLogin" placeholder="密码"
                    >
                  </my-form-item>
                </div>
                <div v-if="captchaOnOff" class="input-user">
                  <my-form-item prop="code" :rules="[{notNull:true,message:'验证码不能为空'}]">
                    <input class="input" v-model="loginForm.code" @keyup.enter="handleLogin" placeholder="验证码"/>
                    <span class="yzm-btn">
                      <img :src="codeUrl" @click="getCode" style="height: 50px;width: 140px"  class="pointer"
                    ></span>
                  </my-form-item>
                </div>
                <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 5px 0px;">记住密码</el-checkbox>
                <!-- 登录按钮 -->
                <el-button
                  :loading="loading"
                  size="medium"
                  type="primary"
                  class="login-btn" id="loginBtn"
                  @click.native.prevent="handleLogin"
                >
                  <span v-if="!loading">登 录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
                <div style="float: right;" v-if="register">
                  <router-link class="link-type" :to="'/register'">立即注册</router-link>
                </div>
              </div>
            </div>
          </div>
          <p v-show="config.showWxLoginBtn" class="other_type">其他方式：<i @click="showWxLogin" class="fa fa-weixin"></i></p>
        </el-form>
      </div>

    </div>
<!--    微信扫码登录 -->
    <div class="main" v-show="showDiv=='wxLogin'">
      <div class="login-main" >
        <el-form ref="wxLoginForm" class="login-form" style="position: relative">
          <div class='switch-container'>
            <div @click="reload" class="btn-link switch-wrapper" type="button"><i aria-hidden="true" class="fa fa-desktop switch-icon switch-icon-pc"></i>
            </div>
          </div>
          <h3 class="login-font" style="margin-top: 0px">微信登录</h3>
          <div id="qrcode_container"  class="form-group has-feedback qrcode_container">
            <div id="wx_qr_expired" class="wx-qr-expired" v-if="wxQrcodeExpired" @click="showWxLogin">
              <div class="wx-qr-expired-text">
                二维码过期<br>
                <span class="text-blue">请点击刷新</span>
              </div>
            </div>
          </div>
          <div class="text-center">
            <p><i class="fa fa-weixin text-green" aria-hidden="true"></i> 微信扫码<span
              class="text-green text-bold">关注公众号</span></p>
            <p>登录更快更安全</p>
          </div>
          <el-form-item>
            <div class="text-center">
              <el-button type="text" @click="reload">密码登录</el-button>
            </div>
          </el-form-item>
        </el-form>

      </div>
    </div>
<!-- 绑定新用户 -->
    <div v-show="showDiv=='bindNewAccount'">
      <el-form ref="bindNewAccountForm" :model="newUser" class="login-form">
        <h3 class="title">完善资料</h3>
        <p class="">您已使用微信扫码</p>
        <my-form-item prop="username" :rules="[{notNull:true,message:'用户名不能为空'},{regExp:/^[\u4e00-\u9fa5|\da-zA-Z]+$/,message:'只能使用中文、字母或数字'}, {regExp:/[^\d]/,message:'用户名不能是纯数字'},{fn:this.verifyUsername,message:'用户名已存在'}]">
          <my-input placeholder="用户名" v-model.trim="newUser.username">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="showName" class="form-group has-feedback"  :rules="[{notNull:true,message:'姓名不能为空'}]">
          <my-input placeholder="真实姓名" v-model="newUser.showName">
            <svg-icon slot="prefix" icon-class="icon" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="password" :rules="passwordRules">
          <my-input  autocomplete="new-password" placeholder="密码" type="password" v-model="newUser.password">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="confirmPassword" :rules="[{notNull:true,message:'密码不能为空'},{fn:this.verifyPassword,message:'两次密码不一致'}]">
          <my-input autocomplete="new-password" placeholder="确认密码"
                    type="password" v-model="newUser.confirmPassword">
            <svg-icon slot="prefix" icon-class="checkbox" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="email" :rules="[{isEmail:true,message:'邮箱不正确'},{fn:this.verifyEmail,message:'邮箱已存在'}]">
          <my-input placeholder="邮箱" v-model="newUser.email" maxlength="20">
            <svg-icon slot="prefix" icon-class="email" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="mobile"  :rules="[{isMobile:true,message:'手机号格式不正确'},{fn:this.verifyMobile,message:'手机号已存在'}]">
          <my-input  v-model="newUser.mobile" placeholder="手机号" maxlength="20">
            <svg-icon slot="prefix" icon-class="phone" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="captcha" v-if="captchaOnOff" :rules="[{notNull:true,message:'验证码不能为空'}]">
          <my-input
            v-model="newUser.captcha"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter.native="bindNewAccount"
          >
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </my-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </my-form-item>

        <my-form-item style="width:100%;margin-bottom: 10px;">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width:100%;"
            @click.native.prevent="bindNewAccount"
          >
            <span v-if="!loading">注册并绑定</span>
            <span v-else>注 册 中...</span>
          </el-button>
        </my-form-item>
        <el-form-item>
          <div class="text-center">
            <el-button type="text" @click="showBindOldAccount">选择已有账户</el-button>
          </div>
        </el-form-item>
      </el-form>


    </div>
<!--    绑定已存在用户-->
    <div v-show="showDiv=='bindExistingAccount'">
      <el-form ref="bindExistingAccountForm" :model="oldUser" class="login-form">
        <h3 class="title">完善资料</h3>
        <p class="">您已使用微信扫码</p>
        <my-form-item prop="username" :rules="[{notNull:true,message:'用户名不能为空'}]">
          <my-input placeholder="用户名" v-model.trim="oldUser.username">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="password" :rules="[{notNull:true,message:'密码不能为空'}]">
          <my-input  autocomplete="new-password" placeholder="密码" type="password" v-model="oldUser.password">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </my-input>
        </my-form-item>

        <my-form-item prop="captcha" v-if="captchaOnOff" :rules="[{notNull:true,message:'验证码不能为空'}]">
          <my-input
            v-model="oldUser.captcha"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter.native="bindExistingAccount"
          >
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </my-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </my-form-item>

        <my-form-item style="width:100%;margin-bottom: 10px;">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width:100%;"
            @click.native.prevent="bindExistingAccount"
          >
            <span v-if="!loading">登陆并绑定</span>
            <span v-else>登 陆 中...</span>
          </el-button>
        </my-form-item>
        <el-form-item>
          <div class="text-center">
            <el-button type="text" @click="showBindNewAccount">注册一个新账号</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span style="color: #ffffff;font-size: 14px">{{ config.webSiteCopyright }}&nbsp;&nbsp;<a v-if="config.webSiteICP" href="https://beian.miit.gov.cn/">{{config.webSiteICP}}</a></span>
    </div>
  </div>
</template>

<script>
import {
  checkEmail, checkMobile,
  checkUserName, doBind,
  getCodeImg,
  getConfig,
  getSecret,
  getSessionId,
  getTicketQrCode,
  polling, registerAndBind
} from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import {rsaEncoding} from '@/utils/rsa';
import { Base64 } from 'js-base64';
import configUtils from "@/utils/configUtils";
import QRCode from 'qrcodejs2'
import {setToken} from "@/utils/auth";
import cache from '@/plugins/cache';
import {projectCode} from '@/settings'


export default {
  name: "Login",
  data() {
    return {
      showDiv: 'login',
      // showDiv: 'bindNewAccount',
      //配置信息
      config: {
        webSiteName: '',
        webSiteCopyright: '',
        webSiteICP: '',
        showWxLoginBtn: false,
        passwordPolicy: '',
      },
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      newUser: {
        username: '',
        showName: '',
        password: "",
        confirmPassword: '',
        email: '',
        mobile: '',
        captcha: '',
      },
      oldUser: {
        username: '',
        password: "",
        captcha: '',
      },
      //加密后的登录参数
      loginParams: {
        a: '',
        b: '',
        c: '',
        d: '',
        k: '',
        l: '',
        code: '',
        uuid: '',
      },
      loading: false,
      // 验证码开关
      captchaOnOff: false,
      // 注册开关
      register: false,
      redirect: undefined,
      wxQrcodeExpired: false,
      qrCode: null,
      pollingTimerId: 0,
      pollingTimerStartAt: 0,
      sessionId: '',
    };
  },
  computed: {
    passwordRules: function () {
      if (this.config.passwordPolicy == 'false') {
        return [{notNull: true, message: '密码不能为空'}, {regExp: /^[^\u4e00-\u9fa5]{0,}$/, message: '密码不可包含汉字'}];
      } else {
        return [{notNull: true, message: '密码不能为空'}, {
          regExp: /(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}/,
          message: '必须包含字母和数字，长度8-30'
        }, {regExp: /^[^\u4e00-\u9fa5]{0,}$/, message: '密码不可包含汉字'}];
      }
    },
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCookie();
    this.getConfig();
    this.getSessionId();
  },
  methods: {
    //返回页面初始状态
    reload: function () {
      this.showDiv = 'login';
      this.getCode();

      //停止轮询后端是否收到微信扫码回调。
      this.stopPolling();
    },
    //显示绑定新用户
    showBindNewAccount() {
      this.showDiv = 'bindNewAccount';
      this.getCode();
    },
    //显示绑定已有账户
    showBindOldAccount() {
      this.showDiv = 'bindExistingAccount';
      this.getCode();
    },
    //显示微信登陆二维码
    showWxLogin() {
      this.wxQrcodeExpired = false;
      this.showDiv = "wxLogin";
      //获取ticket二维码url
      this.getTicketQrCode();

      //开始轮询后端，是否收到微信扫码回调
      this.startPolling();
    },
    //获取ticket二维码url
    getTicketQrCode() {
      getTicketQrCode(this.sessionId).then(r=>{
        if (this.qrCode) {
          this.qrCode.clear();
          this.qrCode.makeCode(r.url);
        }else{
          this.qrCode = new QRCode(document.getElementById("qrcode_container"), {
            text: r.url,
            width: 147,
            height: 147,
          });
        }
        this.qrCode._el.title = '';
      });
    },
    //轮询扫码结果
    startPolling() {
      if (this.pollingTimerId) {
        return;
      }
      var that = this;
      var func = function () {
        //二维码等待超时，要求刷新二维码
        if (Date.now() - that.pollingTimerStartAt >= 60 * 1000) {
          that.wxQrcodeExpired = true;
          that.stopPolling();
          return;
        }
        polling(that.sessionId).then(function (r) {
          if (r.scanned) {
            that.stopPolling();
            //登陆跳转 (清除 cache sessionId)

            if (!r.bound) {
              //没有绑定账号。注册新账号，或者绑定现有账号。
              that.showBindNewAccount();
            } else {
              that.loginSuccess(r.token);
            }
          }
        }).catch(function (err) {
          that.stopPolling();
          //登录失败
        });
      };
      this.pollingTimerStartAt = Date.now();
      this.pollingTimerId = setInterval(func, 1500);
    },
    //扫码登陆成功
    loginSuccess(token) {
      //否则做登录跳转
      setToken(token)
      this.$store.commit('SET_TOKEN', token)
      this.$router.push({path: this.redirect || "/"}).catch((error) => {
        console.log(error);
      });
    },
    //停止轮询
    stopPolling() {
      clearInterval(this.pollingTimerId);
      this.pollingTimerId = 0;
    },
    //登录前获取配置
    getConfig() {
      getConfig().then(config => {
        this.config = config;
        configUtils.setConfigValue("webSite.name", this.config.webSiteName);
        let localVersion = cache.local.get("webSite.innerVersion");
        if (this.config.innerVersion !== localVersion) {
          console.log("reload");
          cache.local.set("webSite.innerVersion", this.config.innerVersion);
          window.location.reload(true);
        }
      });
    },
    //获取一个uuid作为用户唯一标识
    getSessionId() {
      let sessionId = cache.session.get(projectCode + '.sessionId');
      if(sessionId){
        this.sessionId = sessionId;
        this.getCode();
      }else{
        getSessionId().then(r =>{
          this.sessionId = r.sessionId;
          cache.session.set(projectCode + '.sessionId', this.sessionId)
          this.getCode();
          }
        );
      }
    },
    getCode() {
      getCodeImg(this.sessionId).then(res => {
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          getSecret().then(
            res=>{
              this.loginParams.code = this.loginForm.code;
              this.loginParams.uuid = this.loginForm.uuid;
              let userNameBase64 = Base64.encode(this.loginForm.username.trim() + "&&&&" + this.loginForm.password);
              this.loginParams.a = rsaEncoding(res.b, res.a, userNameBase64);
              this.loginParams.b = rsaEncoding(res.d, res.c, userNameBase64);
              this.loginParams.c = rsaEncoding(res.f, res.e, this.loginForm.password);
              this.loginParams.d = rsaEncoding(res.h, res.g, this.loginForm.password);
              this.loginParams.k = res.k;
              this.loginParams.l = res.l;
              this.loginParams.sessionId = this.sessionId;

              this.$store.dispatch("Login", this.loginParams).then(() => {
                this.$router.push({ path: this.redirect || "/" }).catch(()=>{
                });
              }).catch((r) => {
                this.loading = false;
                this.captchaOnOff = r.captchaOnOff;
                if(this.captchaOnOff){
                  this.getCode();
                }
              });
            }
          ).catch(error=>{
            console.log(error);
            this.loading = false;
            if (this.captchaOnOff) {
              this.getCode();
            }
          });

        }
      });
    },
    bindNewAccount: function () {
      this.$refs.bindNewAccountForm.validate(valid=>{
        if (valid) {
          this.loading = true;
          registerAndBind(this.newUser, this.newUser.captcha, this.loginForm.uuid, this.sessionId)
            .then(r => {
              this.loading = false;
              this.loginSuccess(r.token);
            }).catch(error => {
            console.log(error);
            this.loading = false;
            if (this.captchaOnOff) {
              this.getCode();
            }
          });
        }
      })
    },
    bindExistingAccount: function () {
      this.$refs.bindExistingAccountForm.validate(valid=>{
        if (valid) {
          this.loading = true;
          doBind(this.oldUser.username, this.oldUser.password, this.oldUser.captcha, this.loginForm.uuid, this.sessionId)
            .then(r => {
              this.loading = false;
              this.loginSuccess(r.token);
            }).catch(error => {
            console.log(error);
            this.loading = false;
            if (this.captchaOnOff) {
              this.getCode();
            }
          });

        }
      })
    },
    //验证用户名是否重复
    verifyUsername() {
      if (!this.newUser.username) {
        return Promise.resolve();
      }
      return new Promise((resolve, reject)=>{
        checkUserName(this.newUser.username)
          .then(r => r.result ? reject() : resolve()).catch(reject);
      })
    },
    //验证密码是否相同
    verifyPassword() {
      if (this.newUser.confirmPassword && this.newUser.password) {
        return this.newUser.confirmPassword === this.newUser.password?Promise.resolve():Promise.reject();
      } else {
        return Promise.resolve();
      }
    },
    //验证邮箱是否重复
    verifyEmail() {
      if (!this.newUser.email) {
        return Promise.resolve();
      }
      return new Promise((resolve, reject)=>{
        checkEmail(this.newUser.email)
          .then(r => r.result ? reject() : resolve()).catch(reject);
      })
    },
    //验证手机号是否重复
    verifyMobile() {
      if (!this.newUser.mobile) {
        return Promise.resolve();
      }
      return new Promise((resolve, reject)=>{
        checkMobile(this.newUser.mobile)
          .then(r => r.result ? reject() : resolve()).catch(reject);
      })
    },
  }
};
</script>

<style src="@/assets/styles/login.css" scoped>

</style>

<style rel="stylesheet/scss" lang="scss">

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("/image/loginBackground.jpg");
  background-position: center center  ;
  background-repeat: no-repeat;
  background-size: cover;
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.el-login-footer a:hover {
  color: #067cf1;
}
.login-code-img {
  height: 38px;
}
.other_type{
  margin-top: 5px;
  font-size: 14px;
}
.other_type i{
  color:#00a65a
}

.switch-container {
  top: 10px;
  right: 0;
  overflow: hidden;
  position: absolute;
  width: calc(60px * 1.2);
  height: calc(60px * 1.2);
}

.switch-wrapper {
  overflow: hidden;
  position: absolute;
  transform-origin: 0 0;
  transform: skewX(36deg) translateX(-4px);
  color: mediumseagreen;
  cursor: pointer;
}

.switch-wrapper:hover {
  color: palegreen;
}

.switch-icon {
  transform: skewX(-36deg) translateX(-4px);
  transform-origin: 0 0;
  font-size: 60px;
  width: calc(60px * 1.2);
  height: calc(60px * 1.2);
  text-align: center;
  line-height: calc(60px * 1.2);
}

.switch-icon-pc {
  font-size: 56px;
}

.qrcode_container {
  width: 100%;
  height: 147px;
  /*overflow: hidden;*/
}

.qrcode_container img {
  width: 147px;
  height: 147px;
  margin: 0 auto;
}

.wx-qr-expired {
  width: 100%;
  height: 147px;
  position: absolute;
  background-color: rgba(255, 255, 255, .9);
  font-weight: bolder;
  left: 0;
}

.wx-qr-expired-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-shadow: 1px 1px 2px #FC0, 0 0 1em #FC0, 0 0 0.2em #FC0;
}

.text-center p{
  font-size: 14px;
}


</style>
