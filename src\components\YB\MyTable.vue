<template>
<div>
  <el-table ref="innerTable"
            :data="page.list"
            border
            v-bind="$attrs"
            v-on="$listeners"
            v-loading="loading"
            :height="inTableHeight"
            :max-height="maxHeight"
            :highlight-current-row="!multiselect"
            :cell-class-name="cellClassName"
            @row-click="handleRowClick"
            @select="handleSelectionChange"
            @select-all="handleSelectionChange"
            @current-change="handleCurrentChange"
            @sort-change="handleSortChange"
            :row-key="rowKey"
  >
    <el-table-column :fixed="fixed" v-if="multiselect" align="center" type="selection" :selectable="selectable"></el-table-column>
    <el-table-column :fixed="fixed" v-if="showRowNumber" label="序号" width="55" align="center" :index="page.pageSize * (page.currPage - 1) + 1" type="index"></el-table-column>
    <slot></slot>
  </el-table>
  <el-pagination
    style="margin-top: 5px;margin-right: 30px;text-align: right;"
    v-if="showPager"
    background
    @size-change="handleSizeChange"
    @current-change="handleCurrentPageChange"
    :current-page="page.currPage"
    :page-sizes="pageSizes"
    :page-size="page.pageSize"
    :total="page.totalCount"
    layout="total, sizes, prev, pager, next, jumper"
  ></el-pagination>
</div>
</template>

<script>
import {isArray} from "@/utils/validate";
import request from "@/utils/request";
/**
 * <li>功能描述：table组件，封装分页等查询方法，简化写法。支持跨页选择、自动高度、行点击选中、后端排序</li>
 * 当后端排序时，需要设置el-table-column的属性 sortable="custom" 并通过column-key指定排序的字段名称。
 *
 * props: 支持el-table的所有props属性，另外添加了一些属性。除url属性外，其他属性都有默认值
 * <li>datatype: 数据类型 json-根据设置的url去后台获取;local-本地数据 需要通过pageData属性，传递page对象</li>
 * <li>pageData: 外部传递分页对象,当dataType=local时需要设置这个值</li>
 * <li>url: 请求后端的url，当dataType=url时，需要设置这个值。约定需要分页是后端返回page对象，不分页时后端返回list对象</li>
 * <li>multiselect: 是否显示复选框</li>
 * <li>height: 表格高度，不设置时为自动高度</li>
 * <li>offset: 自动高度的高度偏差，不设置时组件自动计算，设置时使用设置的值</li>
 * <li>showPager：是否显示分页</li>
 * <li>pageSize：初始加载的分页大小</li>
 * <li>pageSizes：可选分页大小数组</li>
 * <li>rowKey: 当需要复选框操作时，或初始加载选中某些行数时，指定行主键，默认为id</li>
 * <li>selectKeys：组件加载时需要选中的数据主键数组</li>
 * <li>selectObjs： 组件加载时需要选中的数据列表，给定数据列表时，将覆盖selectKeys的值，这两个设置一个就可以了</li>
 * <li>fixed： 是否固定左侧序号列和多选框列,默认值false</li>
 * <li>showRowNumber： 是否显示序号列,默认值true</li>
 * <li>defaultQueryParams： 默认查询条件，请求后台时固定不变的查询条件，默认值：{}</li>
 * <li>localSort： 是否前端排序，默认是指false</li>
 * <li>selectable： Function(row,index),类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选</li>
 * <li>autoRequest： 是否在加载表格时自动发一次请求。默认为 true</li>
 *
 * 已支持的el-table事件：sort-change,my-selection-change,my-row-click，current-change
 * 已支持的el-pagination事件： page-size-change，current-page-change
 * 自定义事件：query-success(r),参数为后端返回的响应体
 *
 * 为外部提供的方法：
 * <li>search(q,restart) : 发起查询请求，第一个参数为key-value的查询条件，第二个参数为是否从第一页开始</li>
 * <li>getSelectRowKeys(): 获取已选中的数据主键数组，支持跨页选择</li>
 * <li>getSelectRowObjs(): 获取已选中的数据数组，支持跨页选择</li>
 * <li>getPageData(): 获取当前表格数据对应的page对象</li>
 *
 * @author: <EMAIL>
 * @date: 2022/1/28 17:11
 */
export default {
  name: "MyTable",
  props:{
    //数据类型 json-根据设置的url去后台获取，local-本地数据 需要通过pageData属性，传递page对象
    datatype: {
      type: String,
      default: 'json'
    },
    //请求后端的url，约定需要分页是后端返回page对象，不分页时后端返回list对象
    url: {
      type: String,
      default: ""
    },
    //是否显示复选框
    multiselect: {
      type: Boolean,
      default: true
    },
    //单选时是否显示radio，当multiselect=false 时生效
    showRadio: {
      type: Boolean,
      default: true,
    },
    //是否显示行序号列
    showRowNumber: {
      type: Boolean,
      default: true
    },
    //自动高度的高度偏差，不设置时组件自动计算，设置时使用设置的值
    offset: {
      type: Number,
      default: null
    },
    //外部传递分页对象
    pageData: {
      type: Object,
      default: null
    },
    //是否分页
    showPager: {
      type: Boolean,
      default: true
    },
    //高度
    height: {
      type: [String,Number],
      default: null
    },
    //可选分页组
    pageSizes: {
      type: Array,
      default: function () {
        return [10, 20, 30, 50, 100];
      }
    },
    //默认分页大小
    pageSize: {
      type: Number,
      default: 10,
    },
    //默认查询条件
    defaultQueryParams: {
      type: Object,
      default: function () {
        return {};
      },
    },
    //唯一标识，
    rowKey: {
      type: String,
      default: 'id'
    },
    //组件加载时需要选中的数据唯一标识
    selectKeys: {
      type: Array,
      default: null
    },
    //组件加载时需要选中的数据列表，给定数据列表时，将覆盖selectKeys的值
    selectObjs: {
      type: Array,
      default: null
    },
    //是否固定左侧序号列和多选框列
    fixed: {
      type: Boolean,
      default: false,
    },
    //是否前端排序
    localSort: {
      type: Boolean,
      default: false,
    },
    //类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选
    selectable: {
      type: Function,
      default: null
    },
    cellClassName:{
      type: String,
      default: "el-table-max-cell3"
    },
    maxHeight:{
      type:String,
      default:null
    },
    autoRequest: {
      type:Boolean,
      default: true,
    },
  },
  data: function () {
    return {
      //分页数据
      page: {
        list: [],
        totalCount: 0,
        pageSize: 10,
        totalPage: 0,
        currPage: 1
      },
      //加载数据标记
      loading: false,
      //表格高度
      inTableHeight: 300,
      //查询参数，q对象
      queryParams: {},
      //选中的rowKey,支持跨页选择
      finalSelectRowKeys: [],
      //选中的数据，支持跨页选择。
      finalSelectObjs: [],
      radioCheckObj: {},
    };
  },
  methods: {
    //给外部调用的，发起请求方法
    search: function (q, restart) {
      // if (typeof (restart) == "boolean" && restart) {
      if (this.selectObjs) {
        this.finalSelectObjs = this.selectObjs;
        this.finalSelectRowKeys = this.selectObjs.map(function (e) {
          return e[this.rowKey];
        });
      } else if (this.selectKeys) {
        this.finalSelectRowKeys = this.selectKeys;
      } else {
        this.finalSelectObjs = [];
        this.finalSelectRowKeys = [];
      }
      // }
      this.queryParams = q || {};
      this.page.currPage = typeof (restart) == "boolean" && restart ? 1 : this.page.currPage;
      this.queryPageData();
    },
    //给外部提供一个获取选中id的方法
    getSelectRowKeys: function () {
      return this.finalSelectRowKeys;
    },
    //给外部提供一个获取选中对象的方法
    getSelectRowObjs: function () {
      return this.finalSelectObjs;
    },
    //给外部提供获取page对象的方法
    getPageData: function () {
      return this.page;
    },
    //点击排序
    handleSortChange: function (opt) {
      if (this.localSort) {
        return;
      }
      if (opt.order == 'descending') {
        this.queryParams.sidx = opt.column.columnKey || opt.prop;
        this.queryParams.order = "desc";
      } else if (opt.order == 'ascending') {
        this.queryParams.sidx = opt.column.columnKey || opt.prop;
        this.queryParams.order = "asc";
      } else {
        this.queryParams.sidx = "";
        this.queryParams.order = "";
      }
      this.page.currPage = 1;
      this.queryPageData();
      // this.$emit("sort-change", opt);
      this.$emit("my-sort-change", opt,{
        "sidx": this.queryParams.sidx,
        "order": this.queryParams.order
      });
    },
    //行选择发生变化。selection-change
    handleSelectionChange: function (selection) {
      var that = this;
      //选中的加入到最终列表
      var checkRowKeys = [];
      for (var i = 0; i < selection.length; i++) {
        var rowKey = selection[i][that.rowKey];
        checkRowKeys.push(rowKey);
        //不重复添加
        if (that.finalSelectRowKeys.indexOf(rowKey) < 0) {
          that.finalSelectRowKeys.push(rowKey);
          that.finalSelectObjs.push(selection[i]);
        }
      }
      //没选中的移出最终列表，
      var pageRowKeys = that.page.list.map(function (e) {
        return e[that.rowKey];
      });
      that.finalSelectRowKeys = that.finalSelectRowKeys.filter(function (e) {
        return pageRowKeys.indexOf(e) < 0 ? true : checkRowKeys.indexOf(e) >= 0;
      });
      that.finalSelectObjs = that.finalSelectObjs.filter(function (e) {
        return that.finalSelectRowKeys.indexOf(e[that.rowKey]) >= 0;
      });
      //最终数据抛出到父组件
      that.$emit("my-selection-change", that.finalSelectObjs);
    },
    //行点击事件
    handleRowClick: function (row, column, event) {
      if (!column ||column.columnKey == 'caozuo' || column.label == '操作') {
        return;
      }

      let that = this;
      if (this.multiselect) {
        if (this.selectable && !this.selectable(row)) {
          return;
        }

        var rowKey = row[this.rowKey];
        if (this.finalSelectRowKeys.indexOf(rowKey) < 0) {
          this.$refs.innerTable.toggleRowSelection(row, true);
          this.finalSelectRowKeys.push(rowKey);
          this.finalSelectObjs.push(row);
        } else {
          this.$refs.innerTable.toggleRowSelection(row, false);
          this.finalSelectRowKeys = this.finalSelectRowKeys.filter(function (e) {
            return e != rowKey;
          });
          that.finalSelectObjs = that.finalSelectObjs.filter(function (e) {
            return that.finalSelectRowKeys.indexOf(e[that.rowKey]) >= 0;
          });
        }
        that.$emit("my-selection-change", that.finalSelectObjs);
      }else{
        that.radioCheckObj = row;
        that.finalSelectRowKeys = [row[that.rowKey]];
        that.finalSelectObjs = [row];
        that.$emit("my-selection-change", that.finalSelectObjs);
      }
      this.$emit("my-row-click", row, column, event);
    },
    //单选时，选中行
    handleCurrentChange: function (row, oldRow) {
      if (row) {
        if (!this.multiselect) {
          this.finalSelectObjs = [row];
          this.finalSelectRowKeys = [row[this.rowKey]];
        }
      }else{
        this.finalSelectObjs = [];
        this.finalSelectRowKeys = []
      }
      this.$emit("my-current-change", row, oldRow);
    },
    //分页大小变化
    handleSizeChange: function (pageSize) {
      this.page.pageSize = pageSize;
      this.queryPageData();
      this.$emit("page-size-change", pageSize);
    },
    //当前页变化
    handleCurrentPageChange: function (currPage) {
      this.page.currPage = currPage;
      this.queryPageData();
      this.$emit("current-page-change", currPage);
    },
    //请求后台获取数据
    queryPageData: function () {
      if (!this.url) {
        return;
      }
      var that = this;
      that.loading = true;
      this.queryParams.page = this.page.currPage;
      this.queryParams.limit = this.page.pageSize;
      this.queryParams.t = new Date().getTime();
      //默认查询条件参数
      if (this.defaultQueryParams) {
        for (let key in this.defaultQueryParams) {
          this.queryParams[key] = this.defaultQueryParams[key];
        }
      }
      //为了兼容旧版本后台，这里查询条件通过请求体，请求参数两种方式发送，后台推荐用Query类@RequestBody接收数据
      request({
        url: this.url,
        method: 'post',
        data: this.queryParams,
        params: this.queryParams,
      }).then(function (r) {
        // 出发请求成功事假
        that.$emit("query-success", r)
        that.loading = false;
        if (that.showPager) {
          that.page = r.page;
        } else {
          that.page.list = r.list;
        }
        that.$nextTick(function () {
          that.toggleRowSelection();
        });
      }).catch(function (r) {
        that.loading = false;
      });
    },
    //处理数据反选
    toggleRowSelection: function () {
      var that=this;
      //选中以前选的数
      for (var i = 0; i < that.page.list.length; i++) {
        var rowKey = that.page.list[i][that.rowKey];
        if (!rowKey) {
          alert("row-key属性设置错误")
          break
        }
        if (that.finalSelectRowKeys.indexOf(rowKey) >= 0) {
          that.$refs.innerTable && that.$refs.innerTable.toggleRowSelection(that.page.list[i], true);
          //当传递select-keys时，有可能在finalSelectObjs中没有值，这里处理一次，
          let find=that.finalSelectObjs.find(function (e) {
            return e[that.rowKey] == rowKey;
          });
          if(!find) {
            that.finalSelectObjs.push(that.page.list[i]);
          }
          //单选的选中
          that.radioCheckObj = that.page.list[i];

        }else{
          that.$refs.innerTable && that.$refs.innerTable.toggleRowSelection(that.page.list[i], false);
        }
      }
      that.changeTableHeight();
    },
    //设置表格高度
    changeTableHeight: function () {
      if (!this.$refs.innerTable) {
        return;
      }
      if (this.height) {
        //如果有传进来高度就取消自适应
        this.inTableHeight = this.height;
        this.doLayout();
        return;
      }
      var tableHeight = window.innerHeight || document.body.clientHeight;
      if (this.offset) {
        //prop给了偏差高度，按照给定的设置
        tableHeight -= this.offset;
        this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
        this.doLayout();
        return;
      }
      //计算表格上方的元素高度，和底部的元素高度，自动计算一个值
      var disTop = this.$refs.innerTable.$el;

      //如果表格上方有元素则减去这些高度适应窗口，66是底下留白部分
      tableHeight -= disTop.offsetTop + 60 + 84;
      /*if (disTop.offsetParent) {
        tableHeight -= disTop.offsetParent.offsetTop;
      }*/
      this.$nextTick(function() {
        this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
      })

      //重绘表格
      this.doLayout();
    },
    //重绘eltable表格，解决错位问题
    doLayout: function () {
      var that=this;
      this.$nextTick(function () {
        that.$refs.innerTable.doLayout();
        //这个延时是个双保险
        setTimeout(function () {
          that.$refs.innerTable && that.$refs.innerTable.doLayout();
        }, 500);
      });
    },
  },
  watch: {
    url: function (nVal) {
      this.queryPageData();
    },
    pageData: {
      handler: function (nValue) {
        if (nValue) {
          if (isArray(nValue)) {
            this.page = {
              list: nValue,
              totalCount: nValue.length,
              pageSize: nValue.length,
              totalPage: 1,
              currPage: 1
            };
          } else {
            this.page = nValue;
          }
          this.$nextTick(function () {
            this.changeTableHeight();
          });
        }
      },
      deep: true
    },
    height: function (nval) {
      if (nval) {
        this.inTableHeight = nval;
        this.$nextTick(function () {
          this.changeTableHeight();
        });
      }
    },
    selectKeys: function (n) {
      this.finalSelectRowKeys = n;
      this.toggleRowSelection();
    },
    selectObjs: function (n) {
      let that=this;
      this.finalSelectObjs = n;
      if (n) {
        this.finalSelectRowKeys = n.map(function (ele) {
          return ele[this.rowKey];
        }.bind(this));
      }else{
        this.finalSelectRowKeys = [];
      }
      this.$nextTick(function () {
        that.toggleRowSelection();
      });
    }
  },
  mounted: function () {
    var that = this;
    //允许外部指定分页大小
    if (this.pageSize) {
      this.page.pageSize = this.pageSize;
    }

    //获取行数据
    if (this.datatype == 'json' && this.autoRequest) {
      this.queryPageData();
    } else if (this.dataType == 'local' && this.pageData) {
      if (isArray(this.pageData)) {
        this.page = {
          list: this.pageData,
          totalCount: this.pageData.length,
          pageSize: this.pageData.length,
          totalPage: 1,
          currPage: 1
        };
      } else {
        this.page = this.pageData;
      }
    }
    this.$nextTick(function () {
      //加载时需要选中的数据
      if (that.selectObjs) {
        that.finalSelectObjs = that.selectObjs;
        that.finalSelectRowKeys = that.selectObjs.map(function (e) {
          return e[that.rowKey];
        });
        that.toggleRowSelection();
      } else if (that.selectKeys) {
        that.finalSelectRowKeys = that.selectKeys;
        that.toggleRowSelection();
      }
      if (this.height) {
        this.inTableHeight = this.height;
        this.changeTableHeight();
      }

      //表格高度自适应浏览器大小
      window.onresize = function () {
        that.changeTableHeight();
      };
    });
    //监听浏览器标签页切换
    document.addEventListener("visibilitychange", function () {
      if (!document.hidden) {
        that.changeTableHeight();
      }
    });
  }
}
</script>

