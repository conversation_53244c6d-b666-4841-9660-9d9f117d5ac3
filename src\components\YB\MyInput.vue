<template>
  <el-input ref="innerInput" v-bind="$attrs" v-on="inputListeners" :placeholder="placeholder" :value="value" :key="defaultKey">
<!--    输入框头部内容-->
    <template v-slot:prefix>
      <slot name="prefix"></slot>
    </template>
<!--    输入框尾部内容-->
    <template v-slot:suffix>
      <slot name="suffix"></slot>
    </template>
<!--    输入框前置内容-->
    <template v-slot:prepend>
      <slot name="prepend"></slot>
    </template>
<!--    输入框后置内容-->
    <template v-slot:append>
      <slot name="append"></slot>
    </template>
  </el-input>
</template>

<script>
export default {
  name: "MyInput",
  components: {},

  props:{
    defaultKey: {
      default: "my_input_" + Math.random(),
    },
    placeholder: '',
    value: {},
  },
  computed:{
    inputListeners: function () {
      var vm = this
      // `Object.assign` 将所有的对象合并为一个新对象
      return Object.assign({},
        // 我们从父级添加所有的监听器
        this.$listeners,
        // 然后我们添加自定义监听器，
        // 或覆写一些监听器的行为
        {
          // 这里确保组件配合 `v-model` 的工作
          input: function (event) {
            vm.$emit('input', event)
          },
          change: function (event) {
            vm.$emit('change', event);
          },
        }
      )
    }
  }
}
</script>

<style scoped>

</style>
