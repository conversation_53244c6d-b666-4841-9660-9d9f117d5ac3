<template>
  <div>
    <my-dialog title="采砂监管责任人详情" height="70vh" v-bind="$attrs" :visible.sync="detailsOpen" width="1000px" append-to-body @close="cancel">
      <el-form ref="form" :model="responsiblePersion" disabled label-width="200px">

        <el-divider content-position="center">
          <i class="el-icon-info"></i>基本信息
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="cityName" :rules="[{notNull:true,message:'请输入所在市'}]" label="所在市" prop="cityName">
              <my-input readonly v-model.trim="responsiblePersion.cityName" :maxlength="128" placeholder="请输入所在市"/>
            </my-form-item>
          </el-col>
          <el-col :span="12" v-if="responsiblePersion.districtName">
            <my-form-item ref="districtName"  label="所在县/区" prop="districtName">
              <my-input readonly v-model.trim="responsiblePersion.districtName" :maxlength="128" placeholder="请输入所在县/区"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="reverName" :rules="[{notNull:true,message:'请输入河道名称'}]" label="河道名称" prop="reverName">
              <my-input v-model.trim="responsiblePersion.reverName" :maxlength="128" placeholder="请输入河道名称"/>
            </my-form-item>
          </el-col>
          <!--          <el-col :span="12">-->
          <!--            <my-form-item ref="areaCode" :rules="[{notNull:true,message:'请输入所属地区'}]" label="所属地区" prop="areaCode">-->
          <!--              <my-input v-model.trim="responsiblePersion.areaCode" :maxlength="128" placeholder="请输入所属地区"/>-->
          <!--            </my-form-item>-->
          <!--          </el-col>-->
          <el-col :span="12">
            <my-form-item ref="startAddr" :rules="[{notNull:true,message:'请输入起始位置'}]" label="起始位置" prop="startAddr">
              <my-input v-model.trim="responsiblePersion.startAddr" :maxlength="128" placeholder="请输入起始位置"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddr" :rules="[{notNull:true,message:'请输入终止位置'}]" label="终止位置" prop="endAddr">
              <my-input v-model.trim="responsiblePersion.endAddr" :maxlength="128" placeholder="请输入终止位置"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>县级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleXian" :rules="[{notNull:true,message:'请输入县级责任人'}]" label="县级责任人" prop="responsibleXian" >
              <my-input v-model.trim="responsiblePersion.responsibleXian" :maxlength="128" placeholder="请输入县级责任人"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionXian" :rules="[{notNull:true,message:'请输入县名称及职务'}]" label="县名称及职务" prop="positionXian" >
              <my-input v-model.trim="responsiblePersion.positionXian" :maxlength="128" placeholder="请输入县名称及职务"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrXian" :rules="[{notNull:true,message:'请输入县级责任河段起始位置'}]" label="河段起始位置" prop="startAddrXian">
              <my-input v-model.trim="responsiblePersion.startAddrXian" :maxlength="128" placeholder="请输入县级责任河段起始位置"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrXian" :rules="[{notNull:true,message:'请输入县级责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrXian"
            >
              <my-input v-model.trim="responsiblePersion.endAddrXian" :maxlength="128"
                        placeholder="请输入县级责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>乡镇河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleXiang" :rules="[{notNull:true,message:'请输入乡级责任人'}]" label="乡级责任人"
                          prop="responsibleXiang"
            >
              <my-input v-model.trim="responsiblePersion.responsibleXiang" :maxlength="128" placeholder="请输入乡级责任人"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionXiang" :rules="[{notNull:true,message:'请输入乡镇名称及职务'}]" label="乡镇名称及职务"
                          prop="positionXiang"
            >
              <my-input v-model.trim="responsiblePersion.positionXiang" :maxlength="128"
                        placeholder="请输入乡镇名称及职务"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrXiang" :rules="[{notNull:true,message:'请输入乡级责任河段起始位置'}]" label="河段起始位置"
                          prop="startAddrXiang"
            >
              <my-input v-model.trim="responsiblePersion.startAddrXiang" :maxlength="128"
                        placeholder="请输入乡级责任河段起始位置"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrXiang" :rules="[{notNull:true,message:'请输入乡级责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrXiang"
            >
              <my-input v-model.trim="responsiblePersion.endAddrXiang" :maxlength="128"
                        placeholder="请输入乡级责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>村级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleCun" :rules="[{notNull:true,message:'请输入村级责任人'}]" label="村级责任人"
                          prop="responsibleCun"
            >
              <my-input v-model.trim="responsiblePersion.responsibleCun" :maxlength="128" placeholder="请输入村级责任人"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionCun" :rules="[{notNull:true,message:'请输入村名称及职务'}]" label="村名称及职务"
                          prop="positionCun"
            >
              <my-input v-model.trim="responsiblePersion.positionCun" :maxlength="128" placeholder="请输入村名称及职务"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrCun" :rules="[{notNull:true,message:'请输入河段起始位置'}]" label="河段起始位置"
                          prop="startAddrCun"
            >
              <my-input v-model.trim="responsiblePersion.startAddrCun" :maxlength="128"
                        placeholder="请输入村级责任河段起始位置"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrCun" :rules="[{notNull:true,message:'请输入河段终止位置'}]" label="河段终止位置"
                          prop="endAddrCun"
            >
              <my-input v-model.trim="responsiblePersion.endAddrCun" :maxlength="128"
                        placeholder="请输入村级责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>县级水行政主管部门责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任人'}]" label="水行政主管部门责任人"
                          prop="responsibleCompetentDept"
            >
              <my-input v-model.trim="responsiblePersion.responsibleCompetentDept"
                        :maxlength="128" placeholder="请输入县级水行政主管部门责任人"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门单位及职务'}]" label="水行政主管部门单位及职务"
                          prop="positionCompetentDept"
            >
              <my-input v-model.trim="responsiblePersion.positionCompetentDept"
                        :maxlength="128" placeholder="请输入县级水行政主管部门单位及职务"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任河段起始位置'}]"
                          label="河段起始位置"
                          prop="startAddrCompetentDept"
            >
              <my-input v-model.trim="responsiblePersion.startAddrCompetentDept"
                        :maxlength="128" placeholder="请输入县级水行政主管部门责任河段起始位置"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrCompetentDept"
            >
              <my-input v-model.trim="responsiblePersion.endAddrCompetentDept"
                        :maxlength="128" placeholder="请输入县级水行政主管部门责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">
          <i class="el-icon-info"></i>现场监管责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleSupervise" :rules="[{notNull:true,message:'请输入现场监管责任人'}]" label="现场监管责任人"
                          prop="responsibleSupervise"
            >
              <my-input v-model.trim="responsiblePersion.responsibleSupervise" :maxlength="128"
                        placeholder="请输入现场监管责任人"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionSupervise" :rules="[{notNull:true,message:'请输入现场监管单位及职务'}]" label="现场监管单位及职务"
                          prop="positionSupervise"
            >
              <my-input v-model.trim="responsiblePersion.positionSupervise" :maxlength="128"
                        placeholder="请输入现场监管单位及职务"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrSupervise" :rules="[{notNull:true,message:'请输入现场监管责任河段起始位置'}]" label="河段起始位置"
                          prop="startAddrSupervise"
            >
              <my-input v-model.trim="responsiblePersion.startAddrSupervise" :maxlength="128"
                        placeholder="请输入现场监管责任河段起始位置"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrSupervise" :rules="[{notNull:true,message:'请输入现场监管责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrSupervise"
            >
              <my-input v-model.trim="responsiblePersion.endAddrSupervise" :maxlength="128"
                        placeholder="请输入现场监管责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>行政执法责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleEnforce" :rules="[{notNull:true,message:'请输入行政执法责任人'}]" label="行政执法责任人"
                          prop="responsibleEnforce"
            >
              <my-input v-model.trim="responsiblePersion.responsibleEnforce" :maxlength="128"
                        placeholder="请输入行政执法责任人"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionEnforce" :rules="[{notNull:true,message:'请输入行政执法单位及职务'}]" label="行政执法单位及职务"
                          prop="positionEnforce"
            >
              <my-input v-model.trim="responsiblePersion.positionEnforce" :maxlength="128"
                        placeholder="请输入行政执法单位及职务"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrEnforce" :rules="[{notNull:true,message:'请输入行政执法责任河段起始位置'}]" label="河段起始位置"
                          prop="startAddrEnforce"
            >
              <my-input v-model.trim="responsiblePersion.startAddrEnforce" :maxlength="128"
                        placeholder="请输入行政执法责任河段起始位置"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrEnforce" :rules="[{notNull:true,message:'请输入行政执法责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrEnforce"
            >
              <my-input v-model.trim="responsiblePersion.endAddrEnforce" :maxlength="128"
                        placeholder="请输入行政执法责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center" v-if="responsiblePersion.remark">
          <i class="el-icon-info"></i>备注
        </el-divider>
        <el-row>
          <el-col :span="24" v-if="responsiblePersion.remark">
            <my-form-item ref="remark" :rules="[{notNull:true,message:'请输入备注'}]" label="备注" prop="remark">
              <my-input type="textarea" v-model.trim="responsiblePersion.remark" placeholder="请输入备注" :maxlength="255"/>
            </my-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">
          <i class="el-icon-info"></i>审核流程
        </el-divider>
        <el-row>
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item,index) in processList" :key="item.id">
              <td style="width: 30%">{{item.createTime}}</td>
              <td >{{item.createUser}}</td>
              <td >
                <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>
              </td>
              <td >{{item.opinion}}</td>
            </tr>
          </table>
        </el-row>
      </el-form>
    </my-dialog>
  </div>

</template>

<script>
import { getProcessList, getResponsiblePersion } from '@/api/reports/responsiblePersion'
import { getUser } from '@/api/statistics/projectStatistics'

export default {
  name: 'MyResponsibleReportDetails',
  emits: ["close"],
  props:{
    detailsOpen: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    cityName: {
      type: String,
      default: ''
    },
    districtName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      responsiblePersion:{},
      processList:''
    }
  },
  watch:{
    detailsOpen(nvl){
      if(nvl){
        this.getDetails()
      }
    }
  },
  methods:{
    async getDetails(){
      const res = await getResponsiblePersion(this.id)
      this.responsiblePersion = res.responsiblePersion
      this.responsiblePersion.cityName = this.cityName
      this.responsiblePersion.districtName = this.districtName
      const response = await getProcessList(this.id)
      this.processList = response.page.list
    },
    cancel(){
      this.$emit('close')
    }
  }
}
</script>


<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
