import request from '@/utils/request';

// 查询数据组表 详细
export function getDataGroup(id) {
  return request({
    url: '/sys/dataGroup/info/' + id,
    method: 'post'
  });
}

// 新增数据组表
export function addDataGroup(data) {
  return request({
    url: '/sys/dataGroup/save',
    method: 'post',
    data: data
  });
}

// 修改数据组表
export function updateDataGroup(data) {
  return request({
    url: '/sys/dataGroup/update',
    method: 'post',
    data: data
  });
}

// 删除数据组表
export function delDataGroup(id) {
  return request({
    url: '/sys/dataGroup/delete/' + id,
    method: 'post'
  });
}

// 批量删除数据组表
export function delDataGroupBatch(ids) {
  return request({
    url: '/sys/dataGroup/delete',
    method: 'post',
    data: ids
  });
}

// 选择用户弹窗选中角色回显
export function getRolesSelected(id) {
  return request({
    url: '/sys/dataGroup/userIdsByDataGroup/' + id,
    method: 'post'
  });
}

// 选择用户弹窗 确定操作
export function submitRolesSelected(id, data) {
  return request({
    url: '/sys/dataGroup/assignUsers/' + id,
    method: 'post',
    data: data
  });
}
