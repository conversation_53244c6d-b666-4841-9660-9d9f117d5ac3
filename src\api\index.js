import request from '@/utils/request'

// 累计运砂量统计
export function totalVehicleLoad() {
  return request({
    url: '/indexStat/totalVehicleLoad',
    method: 'post'
  })
}

// 运单数量
export function billCount() {
  return request({
    url: '/indexStat/billCount',
    method: 'post'
  })
}

// 运砂人数量
export function driverCountNum(areaCode,projectId,startTime,endTime) {
  return request({
    url: '/indexStat/driverCount',
    data:{areaCode: areaCode, projectId: projectId,startTime:startTime,endTime:endTime},
    method: 'post'
  })
}

// 项目数量
export function projectCount() {
  return request({
    url: '/indexStat/projectCount',
    method: 'post'
  })
}

//各市采砂横向对比（年）
export function getProvinceLoad(startTime, endTime, areaCode,projectId) {
  return request({
    url: '/indexStat/getProvinceLoad',
    data: {startTime: startTime, endTime: endTime, areaCode: areaCode,projectId:projectId},
    method: 'post'
  })
}

export function getCityLoad(code, startTime, endTime,projectId) {
  return request({
    url: '/indexStat/getCityLoad',
    data: {code: code, startTime: startTime, endTime: endTime, projectId: projectId},
    method: 'post'
  })
}

export function getUserInfo() {
  return request({
    url: '/indexStat/getUserInfo',
    method: 'post'
  })
}

export function getProjectLoad(startTime, endTime){
  return request({
    url: '/indexStat/getProjectLoad',
    data: {startTime: startTime, endTime: endTime},
    method: 'post'
  })

}
