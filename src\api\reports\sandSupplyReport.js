import request from '@/utils/request'

// 查询砂石量统计-砂石供应报表详细
export function getSandSupplyReport(id) {
  return request({
    url: '/reports/sandSupplyReport/info/' + id,
    method: 'post'
  })
}

// 新增砂石量统计-砂石供应报表
export function addSandSupplyReport(data) {
  return request({
    url: '/reports/sandSupplyReport/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改砂石量统计-砂石供应报表
export function updateSandSupplyReport(data) {
  return request({
    url: '/reports/sandSupplyReport/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除砂石量统计-砂石供应报表
export function delSandSupplyReport(id) {
  return request({
    url: '/reports/sandSupplyReport/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除砂石量统计-砂石供应报表
export function delSandSupplyReportBatch(ids) {
  return request({
    url: '/reports/sandSupplyReport/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


