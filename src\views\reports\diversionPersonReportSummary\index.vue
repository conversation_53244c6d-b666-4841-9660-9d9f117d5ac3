<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item>
        <my-quarter-select style="width: 573px;height: 0"  ref="myQuarterSelect"  v-model="params" @change="handleChange"></my-quarter-select>
      </el-form-item>
      <el-form-item label="所属区域" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          :disabled="!params.quarter||!params.year"
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reports:diversionResponsiblePerson:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>


    <my-table :row-style="{height: '35px'}"
              :cell-style="{padding: '0px'}"  url="/reports/diversionPersonReport/list" :show-pager="false" :tree-props="{children: 'children'}" :default-query-params="queryParams" ref="diversionPersonReportTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="所在城市" min-width="120" prop="districtName" header-align="center" align="left" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="年度" min-width="120" prop="year" header-align="center" align="center"  column-key="YEAR"></el-table-column>
      <el-table-column  label="季度" min-width="120" prop="quarter" header-align="center" align="center"  column-key="QUARTER">
        <template  #default="scope">
          <my-view pvalue="quarter" :value="scope.row.quarter"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="数据状态" min-width="120" prop="status" header-align="center" align="center"  column-key="STATUS">
        <template  #default="scope">
          <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center" min-width="150">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status"
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-document-copy"
            title="查看详情"
            v-has-permi="['reports:diversionPersonReport:info']"
            @click="handleView(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </my-table>

    <diversionDetils
      :detailsOpen="detailsOpen"
      :process-list="processList"
      @close="close"
      :report-id="reportId"
      :status="status"
      :title="title"
      :version="String(version)"
    />

  </div>
</template>

<script>
import { getDiversionPersonReport, delDiversionPersonReport, delDiversionPersonReportBatch,addDiversionPersonReport, updateDiversionPersonReport } from "@/api/reports/diversionPersonReport";
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyQuarterSelect from '@/components/YB/MyQuarterSelect.vue'
import Template from '@/views/sms/template/index.vue'
import DiversionDetils from '@/views/reports/commponents/diversionDetils.vue'
import ResponsibleDetils from '@/views/reports/commponents/responsibleDetils.vue'
import { getProcessList } from '@/api/reports/responsiblePersion'

export default {
  name: "DiversionPersonReportSummary",
  components: { ResponsibleDetils, DiversionDetils, Template, MyQuarterSelect, MyAreaSelect },
  data() {
    return {
      detailsOpen: false,
      status: '',
      version: '',
      title: '',
      reportId: '',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        status: 'yiTongGuo',
        areaCode: '',
      },
      // 表单参数
      diversionPersonReport: {},
      params:{},
      processList:[]
    };
  },
  mounted() {
  },
  methods: {
    close(){
      this.detailsOpen = false;
      this.$refs.diversionPersonReportTable &&this.$refs.diversionPersonReportTable.search(this.queryParams);
      this.$refs.diversionResponsiblePersonTable && this.$refs.diversionResponsiblePersonTable.search(this.queryParamsView);
    },
    /** 查询南水北调责任人报表列表 */
    reload(restart) {
      this.$refs.diversionPersonReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.diversionPersonReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        year: '',
        quarter: '',
        status: '',
        reportUserId: '',
        reportTime: '',
        version: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.myQuarterSelect.resetQuery();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.diversionPersonReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加南水北调责任人报表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getDiversionPersonReport(id).then(r => {
        this.diversionPersonReport = r.diversionPersonReport;
        this.open = true;
        this.title = "修改南水北调责任人报表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.diversionPersonReport.id) {
            updateDiversionPersonReport(this.diversionPersonReport).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addDiversionPersonReport(this.diversionPersonReport).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        if(row.id) {
          return delDiversionPersonReport(row.id);
        }else{
          return delDiversionPersonReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleFill(row){
      this.detailsOpen = true;
      this.title = "南水北调责任人填报";
      this.diversionPersonReport = row;
      this.reportId = row.id;
      this.status = row.status;
      this.version = row.version;
      this.handleClick()
    },
    handleView(row){
      this.detailsOpen = true;
      this.title = "南水北调责任人详情";
      this.diversionPersonReport = row;
      this.status = ''
      this.reportId = row.id;
      this.version = row.version;
      this.handleClick()
    },
    handleHitBack(row){

    },
    handleClick() {
      this.processList = []
      getProcessList(this.reportId).then(res=>{
        this.processList = res.list
      })
    },
    handleChange(value){
      this.queryParams.year = value.year;
      this.queryParams.quarter =  value.quarter;
    },
    // 导出
    handleExport(){
      this.download('/reports/diversionResponsiblePerson/export', {
        ...this.queryParams
      }, `南水北调负责人_${new Date().getTime()}.xlsx`, 'application/json');
    },
    //下载模板
    handleDownload(){
      this.download("/reports/diversionResponsiblePerson/downloadTemplate", {},"南水北调责任人导入模板.xlsx")
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.diversionPersonReportTable.changeTableHeight();
  },
};
</script>
