<template>
  <span>{{configName}}</span>
</template>

<script>
import ConfigUtils from "@/utils/configUtils";

export default {
  name: "MyConfig",
  props: {
    configCode:{
      type:String,
      default: '',
    }
  },
  data() {
    return {
      configName: "",
    }
  },
  watch: {
    configCode: function (n) {
      if (n) {
        ConfigUtils.getConfigValue(n)
          .then(configName => this.configName = configName);
      }
    },
  },
  created() {
    if (this.configCode) {
      ConfigUtils.getConfigValue(this.configCode)
        .then(configName => this.configName = configName);
    }

  }
}
</script>

<style scoped>

</style>
