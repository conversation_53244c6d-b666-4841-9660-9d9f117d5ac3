<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="name">
        <my-input
          v-model="queryParams.name"
          placeholder="名称"
          clearable
          style="width: 200px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sys:dataGroup:save']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-delete"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sys:dataGroup:update']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sys:dataGroup:delete']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar
        class="rightToolbar"
        :search.sync="search"
        :showSearch.sync="showSearch"
        @queryTable="reload"
      ></right-toolbar>
    </el-row>
    <my-table url="/sys/dataGroup/list" ref="dataGroupTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column label="名称" min-width="80" align="center" prop="name" sortable="custom" column-key="NAME"/>
      <el-table-column label="类型" min-width="80" align="center" prop="type" sortable="custom" column-key="TYPE">
        <template v-slot="scope">
           <my-view pvalue="dataGroupType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="80" align="center" prop="createTime" sortable="custom" column-key="CREATE_TIME"/>
      <el-table-column label="创建人" min-width="80" align="center" prop="createUserName" sortable="custom" column-key="CREATE_USER_NAME"/>
      <el-table-column label="操作" min-width="120" align="center" column-key="caozuo" fixed="right">
        <template slot-scope="scope">
<!--          <el-button
            size="mini"
            type="primary"
            class="btn-table-operate"
            icon="el-icon-user"
            title="选择用户"
            @click="handleSelectUser(scope.row.id)"
            v-hasPermi="['sys:dataGroup:selectUser']"
          ></el-button>-->
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            title="编辑"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sys:dataGroup:update']"
          ></el-button>
          <el-button
            size="mini"
            type="danger"
            title="删除"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sys:dataGroup:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>
    <!-- 新增修改弹窗 -->
    <my-dialog :title="title" :visible.sync="open1" width="380px" append-to-body>
      <el-form ref="form" :model="dataGroup" label-width="80px">
        <my-form-item label="名称" prop="name" :rules="[{notNull:true,message:'请输入名称'}]">
          <my-input
            v-model="dataGroup.name"
            placeholder="请输入名称"
            style="width: 240px"
          />
        </my-form-item>
        <my-form-item label="类型" prop="type" :rules="[{notNull:true,message:'请选择类型'}]">
          <my-select
            v-model="dataGroup.type"
            pvalue="dataGroupType"
            style="width: 240px"
            placeholder="请选择类型"
          />
        </my-form-item>
        <my-form-item
          v-if="dataGroup.type==='custom'"
          label="关联部门"
          prop="deptIds"
          :rules="[{notNull:true,message:'关联部门不能为空'}]"
          class="myTreeSelectMulti"
        >
          <treeselect
            :multiple="true"
            :flat="true"
            :default-expand-level="1"
            v-model="dataGroup.deptIds"
            :options="deptOptions"
            placeholder="请选择关联部门"
            :normalizer="normalizerDept"
            @select="deptTreeSelect"
            style="width: 240px"
          >
            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }" :class="labelClassName">
              <el-tooltip :content="node.label" placement="right" effect="light" >
                <span> {{ node.label }}</span>
              </el-tooltip>
            </label>
          </treeselect>
        </my-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
    <!-- 选择用户弹窗 -->
    <my-dialog title="分配用户" :visible.sync="open2" width="1000px" append-to-body>
      <el-form
        :model="selectParams"
        ref="selectForm"
        size="small"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="用户名" prop="username">
          <my-input
            v-model="selectParams.username"
            placeholder="用户名或姓名"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSelect"
          />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <my-select
            v-model="selectParams.roleId"
            :options="roleSelectList"
            style="width: 200px"
            placeholder="请选择角色"
          ></my-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSelect">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetSelect">重置</el-button>
        </el-form-item>
      </el-form>
      <my-table
        url="/sys/user/list"
        ref="usersTable"
        row-key="userId"
        :offset="320"
        :select-keys="currentDataUserIds"
        @my-selection-change="handleUserSelectionChange">
        <el-table-column label="用户名" min-width="80" align="center" prop="username"/>
        <el-table-column label="姓名" min-width="80" align="center" prop="showName"/>
        <el-table-column label="所属部门" min-width="80" align="center" prop="deptName"/>
        <el-table-column label="角色" min-width="100" align="center" prop="roleNames"/>
        <el-table-column label="最后登录" min-width="120" align="center" prop="lastLoginTime"/>
        <el-table-column label="状态" min-width="80" align="center">
          <template slot-scope="scope">
            <span class="table-label label-danger" v-if="scope.status===0">禁用</span>
            <span class="table-label label-success">正常</span>
          </template>
        </el-table-column>
      </my-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUser">确 定</el-button>
        <el-button @click="userCancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { getDataGroup, delDataGroup, delDataGroupBatch, addDataGroup, updateDataGroup } from '@/api/system/dataGroup';
import { getDeptTreeData2} from '@/api/system/dept';
import { getRoleListData } from '@/api/system/role';
import { handleTree } from '@/utils/ruoyi';
//VueTreeSelect——API文档——https://www.vue-treeselect.cn/#basic-features
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { getRolesSelected, submitRolesSelected } from '../../../api/system/dataGroup';
import Template from '../../sms/template';
export default {
  name: 'DataGroup',
  components: { Template, Treeselect },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      //控制显隐查询
      search: true,
      //显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 查询参数
      queryParams: {},
      // 角色数组
      roleSelectList: [],
      // 类型字典值
      typeList: [],
      // 是否显示新增修改弹窗
      open1: false,
      // 表单参数
      dataGroup: {
        name: '',
        type: '',
        deptIds: []
      },
      // 部门树相关
      deptOptions: [],
      normalizerDept(node) {
        return {
          id: node.deptId,
          label: node.name,
          children: node.children
        };
      },
      // 是否显示选择用户弹窗
      open2: false,
      // 非单个禁用
      userSingle: true,
      // 非多个禁用
      userMultiple: true,
      // 选择用户弹窗查询参数
      selectParams: {},
      // 缓存dataGroupId
      currentDataGroupId: undefined,
      // 选择用户弹窗 选中数组
      currentDataUserIds: []
    };
  },
  mounted() {
    this.getRoleList();
    this.getDeptTreeselect();
  },
  methods: {
    /** 查询数据组表 列表 */
    reload(restart) {
      this.$refs.dataGroupTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    /** 查询部门下拉树结构 */
    getDeptTreeselect() {
      getDeptTreeData2().then(response => {
        this.deptOptions = handleTree(response.deptList, 'deptId', 'parentId');
      });
    },
    /** 查询用户角色 列表 */
    userReload(restart) {
      this.$refs.usersTable.search(this.selectParams, restart);
    },
    /** 获取角色选项 */
    getRoleList() {
      getRoleListData().then(response => {
        this.roleSelectList = response.list.map(function(e) {
          return { 'name': e.roleName, 'value': e.roleId };
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open1 = true;
      this.title = '新增数据组表';
      this.dataGroup = {};
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getDataGroup(id).then(r => {
        this.dataGroup = r.dataGroup;
        this.open1 = true;
        this.title = '修改数据组表';
      });
    },
    /** 选中部门回调事件 */
    deptTreeSelect(node) {
      this.dataGroup.deptIds.push(node.deptId);
    },
    /** 提交按钮操作 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中');
          if (this.dataGroup.id) {
            if (this.dataGroup.type !== 'custom') {
              this.dataGroup.deptIds = [];
            }
            updateDataGroup(this.dataGroup).then(() => {
              this.$modal.msgSuccess('修改成功');
              this.open1 = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          } else {
            addDataGroup(this.dataGroup).then(() => {
              this.$modal.msgSuccess('新增成功');
              this.open1 = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      });
    },
    /** 多选框选中数据 */
    handleSelectionChange() {
      this.ids = this.$refs.dataGroupTable.getSelectRowKeys();
      this.single = this.ids.length !== 1;
      this.multiple = !this.ids.length;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that = this;
      that.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        if (row.id) {
          return delDataGroup(row.id);
        } else {
          return delDataGroupBatch(that.ids);
        }
      }).then(() => {
        that.reload();
        that.$modal.msgSuccess('删除成功');
      }).catch(() => {});
    },
    /** 取消按钮 */
    cancel() {
      this.open1 = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.resetForm('form');
    },
    /** 选择用户按钮操作 */
    handleSelectUser(dataGroupId) {
      this.open2 = true;
      this.currentDataGroupId = dataGroupId;
      this.currentDataUserIds = [];
      if (dataGroupId) {
        getRolesSelected(dataGroupId).then(r => {
          this.currentDataUserIds = r.userIds;
          this.userReload(true);
        });
      }
    },
    /** 选择用户弹窗-搜索操作 */
    handleSelect() {
      this.userReload(true);
    },
    /** 选择用户弹窗-重置操作 */
    resetSelect() {
      this.resetForm('selectForm');
      this.handleSelect();
    },
    /** 选择用户弹窗-多选框选中数据 */
    handleUserSelectionChange() {
      this.currentDataUserIds = this.$refs.usersTable.getSelectRowKeys();
      this.userSingle = this.currentDataUserIds.length !== 1;
      this.userMultiple = !this.currentDataUserIds.length;
    },
    /** 选择用户弹窗-确定操作 */
    submitUser() {
      var userIdsParams = this.$refs.usersTable.getSelectRowKeys();
      var that = this;
      this.$modal.loading('加载中')
      submitRolesSelected(that.currentDataGroupId, userIdsParams).then(() => {
        that.$modal.msgSuccess('操作成功');
        that.open2 = false;
        this.$modal.closeLoading()
      }).catch(this.$modal.closeLoading)
     .catch(() => {})  //捕获异常错误
    },
    /** 选择用户弹窗-取消按钮 */
    userCancel() {
      this.open2 = false;
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.dataGroupTable.changeTableHeight();
  },

};
</script>
