import request from '@/utils/request'

// 查询车辆管理详细
export function getCar(id) {
  return request({
    url: '/project/car/info/' + id,
    method: 'post'
  })
}

// 新增车辆管理
export function addCar(data) {
  return request({
    url: '/project/car/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改车辆管理
export function updateCar(data) {
  return request({
    url: '/project/car/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除车辆管理
export function delCar(id) {
  return request({
    url: '/project/car/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除车辆管理
export function delCarBatch(ids) {
  return request({
    url: '/project/car/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


