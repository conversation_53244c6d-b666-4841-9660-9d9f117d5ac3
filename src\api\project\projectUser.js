import request from '@/utils/request'

// 删除运砂人
export function delProjectUser(userId) {
  return request({
    url: '/project/projectUser/delete/' + userId,
    method: 'post'
  })
}

// 批量删除运砂人
export function batchDelProjectUser(userIds) {
  return request({
    url: '/project/projectUser/delete',
    method: 'post',
    data:userIds,
  })
}

// 运砂人密码重置
export function resetProjectUserPwd(userId) {
  return request({
    url: '/sys/user/reset/'+userId,
    method: 'post',
  })
}
