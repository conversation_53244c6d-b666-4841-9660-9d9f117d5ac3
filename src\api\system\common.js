import request from '@/utils/request';

// 通用上传接口
export function uploadFileList(file, type) {
  return request({
    url: '/com/uploadfile/upload/' + type,
    method: 'post',
    data: file,
    headers: {allowRepeatSubmit: true}
  })
}

// 根据ownerId和type获取上传的文件
export function fileListByOwner(ownerId, type) {
  return request({
    url: '/com/uploadfile/fileListByOwner',
    method: 'post',
    params: {
      ownerId: ownerId,
      type: type
    },
    headers: {allowRepeatSubmit: true}
  })
}
