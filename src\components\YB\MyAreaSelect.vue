<template>
  <div class="content">
    <!-- 地市级选择 -->
    <el-select
      clearable
      filterable
      class="parent-select"
      v-model="parentValue"
      :placeholder="componentType?'请选择地市级':'地市级'"
      @change="parentChange"
      @clear="parentClear"
      :disabled="disabled?disabled:parentDisabled"
    >
      <el-option
        v-for="item in subList"
        :key="item.areaCode"
        :label="item.areaName"
        :value="item.areaCode"
      />
    </el-select>
    <!-- 区县级选择 -->
    <el-select
      clearable
      filterable
      v-model="childrenValue"
      :placeholder="!disabled?componentType?'请选择区县级':'区县级':''"
      @change="childrenChange"
      @clear="childrenClear"
      :disabled="disabled?disabled:childrenDisabled"
    >
      <el-option
        v-for="item in subChildList"
        :key="item.areaCode"
        :label="item.areaName"
        :value="item.areaCode"
      />
    </el-select>
  </div>
</template>

<script>
import {getSubList} from "@/api/system/area";
export default {
  name: "MyAreaSelect",
  emits: ["input"],
  props: {
    value: {
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    componentType: {
      type: Boolean,
      default: false
    },
    childrenDisabled: {
      type: Boolean,
      default: false
    },
    parentDisabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      subList: [],
      subChildList: [],
      parentValue: '',
      childrenValue: '',
      parentName: '',
      childrenName: '',
    }
  },
  methods: {
    parentChange() {
      this.childrenValue = "";
      this.$emit("input", this.parentValue);
      const areaItem = this.subList.find(item => item.areaCode === this.parentValue);
      this.$emit("change", areaItem ? areaItem.areaName : "");
    },
    parentClear() {
      this.childrenValue = "";
      this.subChildList = [];
      this.$emit("input", "");
      this.$emit("change", "")
    },
    childrenChange() {
      this.$emit("input", this.childrenValue);
      const areaItem1 = this.subList.find(item => item.areaCode === this.parentValue);
      const areaItem2 = this.subChildList.find(item => item.areaCode === this.childrenValue);
      this.$emit("change", (areaItem1 ? areaItem1.areaName:"") + (areaItem2?areaItem2.areaName:""));
    },
    childrenClear() {
      this.childrenValue = "";
      this.$emit("input", this.parentValue);
      const areaItem = this.subList.find(item => item.areaCode === this.parentValue);
      this.$emit("change", areaItem ? areaItem.areaName : "");
    },
  },
  watch: {
    value(nval, oval) {
      if(this.value&&this.value.length==11){
        this.parentValue =this.value
        this.childrenValue = ''
      }else {
        this.parentValue = '';
        this.childrenValue = ''
        this.subChildList = []
      }
      if (nval) {
        if (this.parentValue && !this.childrenValue) {
          getSubList(nval).then(res => {
            this.subChildList = res.areaList;
          });
        } else if (!this.parentValue && !this.childrenValue) {
          if (this.value) {
            this.parentValue = this.value.slice(0, 11);
            getSubList(this.parentValue).then(res => {
              this.subChildList = res.areaList;
            });
            this.childrenValue = this.value;
          }
        }
      } else {
        console.log(3333)
        if (this.parentValue) {
          this.childrenValue = "";
          this.parentValue = "";
          this.subChildList = []
        }
      }
    }
  },
  mounted() {
    getSubList('000_013').then(res => {
      this.subList = res.areaList;
    });
    if (this.value.length>11){
      if (this.value) {
        this.parentValue = this.value.slice(0, 11);
        getSubList(this.parentValue).then(res => {
          this.subChildList = res.areaList;
        });
        this.childrenValue = this.value;
      }
    }else {
      if (this.value) {
        this.parentValue = this.value.slice(0, 11);
        getSubList(this.parentValue).then(res => {
          this.subChildList = res.areaList;
        });
        this.childrenValue = []
      }
    }
  }
}
</script>

<style scoped>
.content {
  width: 100%;
}

.content .el-select {
  max-width: 50%;
  min-width: calc(50% - 2.5px);
}

.parent-select {
  margin-right: 5px;
}
</style>
