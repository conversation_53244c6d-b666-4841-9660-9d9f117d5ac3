<template>
    <div class="app-container">
      <el-row>
        <el-col :span="24">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-row style="margin-bottom: 10px">
              <el-form-item label="项目位置" prop="areaCode" style="margin-bottom: 0px"  v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
                <my-area-select
                  v-model="queryParams.areaCode"
                  @change="getAreaName"
                  :external-parent-value="parentAreaCode"
                  :external-children-value="childrenAreaCode"
                  :read-only-parent="isReadOnlyParent"
                  :read-only-children="isReadOnlyChildren"
                  :is-clear="isClear"
                  style="font-size: medium;"/>
              </el-form-item>
              <el-form-item label="项目名称" prop="projectSelectName"  style="margin-bottom: 0px;"  v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
                <my-select
                  id="name"
                  v-model="queryParams.name"
                  placeholder="项目名称"
                />
              </el-form-item>
              <el-form-item label="标段" prop="projectSelectName" label-width="40px" style="margin-bottom: 0px;"  v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
                <my-select
                  id="sectionName"
                  v-model="queryParams.sectionName"
                  placeholder="标段"
                />
              </el-form-item>
            </el-row>

            <el-row style="margin-bottom: 10px">
              <el-form-item label="统计时间" prop="timeRange" style="margin-bottom: 0px" v-hasPermi="['mobile:puichasingOrderManage:dateRange']">
                <el-date-picker
                  v-model="timeRange"
                  type="daterange"
                  @change="handleDateChange"
                  value-format="yyyy-MM-dd"
                  align="right"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptions"
                  :clearable="false">
                </el-date-picker>
              </el-form-item>
              <el-form-item  prop="selectedTime" style="margin-bottom: 0px" v-hasPermi="['mobile:puichasingOrderManage:timeTags']">
                <el-radio-group v-model="selectedTime" @change="handleTimeChange">
                  <el-radio-button  label="本年"></el-radio-button>
                  <el-radio-button  label="本月"></el-radio-button>
                  <el-radio-button  label="本日"></el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-row>


          </el-form>
        </el-col>
        <el-col :span="24" style="padding: 0 5px" v-show="provinceShow"  v-hasPermi="['mobile:puichasingOrderManage:provinceSandMap']">
          <el-card>
            <div slot="header" class="clearfix">
              <span style="font-weight: bold;">{{ provinceSandMapName }}采砂统计</span>
            </div>
            <div ref="provinceSandMap" style="height: 364px"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-col :span="24" style="padding: 0 5px" v-hasPermi="['mobile:puichasingOrderManage:provinceSandMap']">
          <el-card>
            <my-table
              url="/project/statistics/page"
              ref="projectTable"
              row-key="deptId"
              :fixed="true"
              cell-class-name="el-table-max-cell2"
              @my-selection-change="handleSelectionChange"
              @my-sort-change ="handleSortChange"
              class="el-table-auto-height"
            >
              <el-table-column
                label="采区名称"
                fixed="left"
                header-align="center"
                align="left"
                min-width="250"
                prop="name"

                column-key="project.NAME">
                <template #default="scope">
                  <el-tooltip :content="scope.row.name" placement="top" effect="light">
                    <div>{{ scope.row.name }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                label="标段名称"
                align="left"
                header-align="center"
                min-width="160"
                prop="sectionName"

                column-key="project.SECTION_NAME">
              </el-table-column>
              <el-table-column
                label="项目位置"
                align="left"
                header-align="center"
                min-width="160"
                prop="areaName"

                column-key="project.AREA_NAME">
              </el-table-column>
              <el-table-column
                label="项目类型"
                align="center"
                header-align="center"
                min-width="160"
                prop="type"

                column-key="project.TYPE">
                <template slot-scope="scope">
                  <my-view pvalue="projectType" :value="scope.row.type"></my-view>
                </template>
              </el-table-column>
              <el-table-column
                label="控制总量(吨)"
                align="right"
                header-align="center"
                min-width="150"
                prop="totalYield"

                column-key="project.TOTAL_YIELD">
              </el-table-column>
              <el-table-column
                label="已采总量(吨)"
                align="right"
                header-align="center"
                min-width="150"
                prop="mined"

                column-key="project.MINED">
              </el-table-column>
              <el-table-column
                label="时间段内采砂总量(吨)"
                align="right"
                header-align="center"
                min-width="150"
                prop="minedTime"

                column-key="project.MINED_TIME">
              </el-table-column>
              <el-table-column
                label="采区剩余总量(吨)"
                align="right"
                header-align="center"
                min-width="150"
                prop="remainingSand"

                column-key="project.REMAINING_SAND">
              </el-table-column>
            </my-table>
          </el-card>
        </el-col>
      </el-row>
  </div>
</template>

<script>
import {
  driverCountNum,
  getProvinceLoad,
  getUserInfo} from '@/api'
import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
import * as echarts from "echarts";

export default {
  name: "Index",
  components: {
    MyAreaSelect: MyAreaSelect
  },
  data() {
    return {
      totalVehicleLoad: '',// 累计采砂量
      billCount:'',// 运单数量
      completedBillCount:'',// 已完成运单数量
      transitBillCount:'',// 核销运单数量
      driverCount:'',// 采砂人数量
      projectCount:'',// 项目数量

      activeNames: ['1','2','3'],  // 折叠面板
      currentTime:'',  //当前日期时间

      selectedTime: '',

      // 区域选择框默认选中
      parentAreaCode: '',
      childrenAreaCode: '',
      isReadOnlyParent: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren: false, // 控制子下拉框是否可编辑
      isClear:true, // 父选择框是否可清空

      // 显示搜索条件
      showSearch: true,
      //检索参数
      queryParams: {
        name: '1234',
        sectionName:'1111',
        areaCode: '000-013-001'
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      timeRange: [this.formatDate(this.getLastMonthStart()), this.formatDate(new Date())], // 默认选中最近一年,

      //provinceSandMap
      provinceSandMap: null,  //echarts图表对象
      provinceSandMapName: '',
      provinceSandMapXAxis: [],
      provinceSandMapTotalLoad: [],
      provinceSandMapUnit: '吨',
      provinceSandMapCompleted: [],
      provinceSandMapTranit: [],
      provinceSandMapProject: [],
      provinecSandStartTime : this.formatDate(this.getLastMonthStart()),
      provinecSandEndTime : this.formatDate(new Date()),
      provinceShow : true,
    };
  },
  watch: {
    provinceShow(newVal) {
      this.$nextTick(() => {
        console.log("provinceShow:" + newVal);
        if (this.provinceSandMap && newVal) {
          this.provinceSandMap.resize();
        }
      });
    }
  },
  computed:{
    totalVehicle:function(){

      if (this.totalVehicleLoad < 100000) {
        return (this.totalVehicleLoad)
      }
      else if ( this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000){
        return (this.totalVehicleLoad / 10000).toFixed(2)
      }
      else if (this.totalVehicleLoad >= 1000000000 && this.totalVehicleLoad <10000000000000){
        return (this.totalVehicleLoad / 100000000).toFixed(2)
      }
      else{
        return (this.totalVehicleLoad / 1000000000000).toFixed(2)
      }
    },
    unit:function(){
      if (this.totalVehicleLoad < 100000){
        return '吨'
      }else if(this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000) {
        return '万吨'
      }else if(this.totalVehicleLoad >= 1000000000) {
        return '亿吨'
      }else {
        return '万亿吨'
      }
    },
    remainingSand:function(){
      console.log("MyTable.page.list:" + MyTable.page.list)
    }
  },
  created() {
    this.getList();
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    //折叠面板
    handleChange(val) {
        console.log(val);
    },

    updateCurrentTime() {
      const now = new Date();
      this.currentTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
      // this.currentTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    },

    /** 查询服务器信息 */
    getList() {
      // totalVehicleLoad().then(res=>{
      //   this.totalVehicleLoad = res.totalVehicleLoad;
      // })
      // billCount().then(res=>{
      //   console.log(res)
      //   this.billCount = res.count
      // })
      driverCountNum().then(res=>{
        this.driverCount = res.count
      })
      // projectCount().then(res=>{
      //   this.projectCount = res.count
      // })
    },

    //根据窗口更改图表大小
    resizeCharts() {
      this.provinceSandMap.resize();
    },

    //获取当前登录用户信息
    getUserInfo() {
      console.log("********getUserInfo:")
      getUserInfo().then(res => {
        const { deptCode, areaCode, areaName, parentCode, parentName } = res.userInfo;
        let deptCodeLength = deptCode.length;

        // 定义公共属性
        this.defaultCityCode = areaCode;
        this.citySandMapName = areaName;

        switch (deptCodeLength) {
            case 4: // 省级用户
                this.parentAreaCode = ''; // 默认显示石家庄市
                this.provinceSandMapName = '河北省';
                this.defaultCityCode = '000_013_001';
                this.citySandMapName = '石家庄市';
                break;
            case 9: // 市级用户
                this.provinceSandMapName = areaName;
                this.parentAreaCode = areaCode;
                this.isReadOnlyParent = true;
                break;
            case 14: // 区县级用户
                this.parentAreaCode = parentCode;
                this.childrenAreaCode = areaCode;
                this.citySandMapName = parentName + areaName;
                this.isReadOnlyParent = true;
                this.isReadOnlyChildren = true;
                this.provinceShow = false;
                break;
            case 19: // 项目级用户
                this.projectSandFlag = true;
                this.projectShow = true;
                break;
            default:
                return;
        }

        this.initCharts();
      });
    },

    //初始化图表
    initCharts() {
      console.log("初始化initCharts执行方法:")
      if(this.projectSandFlag){ //项目级别信息图表初始化
      }else{ //省市区级别信息图表初始化
        this.provinceSandMap = echarts.init(this.$refs.provinceSandMap);
        this.getProvinceLoadByYear();
      }
      window.addEventListener('resize', () => { this.resizeCharts() });
      document.addEventListener('fullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('webkitfullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('mozfullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('msfullscreenchange', () => { this.resizeCharts() });
    },
    //各市/各区采砂信息横向对比图表
    getProvinceLoadByYear(){
      //获取各市采砂和项目信息
      getProvinceLoad(this.provinecSandStartTime,this.provinecSandEndTime).then(res=>{
        this.provinceSandMapXAxis = [];
        this.provinceSandMapTotalLoad = [];
        this.provinceSandMapCompleted = [];
        this.provinceSandMapTranit = [];
        this.provinceSandMapProject = [];
        res.list.forEach(item => {
            this.provinceSandMapXAxis.push(item.areaName);
            this.provinceSandMapTotalLoad.push(item.totalLoad !== null ? item.totalLoad : 0);
            this.provinceSandMapCompleted.push(item.completedBill !== null ? item.completedBill : 0);
            this.provinceSandMapTranit.push(item.transitBill !== null ? item.transitBill : 0);
            this.provinceSandMapProject.push(item.proCount !== null ? item.proCount : 0);
        });
        // 计算总和
        this.totalVehicleLoad = this.provinceSandMapTotalLoad.reduce((acc, val) => acc + val, 0).toFixed(2);
        this.completedBillCount = this.provinceSandMapCompleted.reduce((acc, val) => acc + val, 0);
        this.transitBillCount = this.provinceSandMapTranit.reduce((acc, val) => acc + val, 0);
        this.projectCount = this.provinceSandMapProject.reduce((acc, val) => acc + val, 0);

        //判断是否需要转变数据位数和单位
        if (this.provinceSandMapTotalLoad.length > 0) {
          if (this.provinceSandMapTotalLoad.some(value => value >= 10000000000000)){
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 1000000000000).toFixed(2)); //万亿吨
            this.provinceSandMapUnit = '万亿吨'
          }else if (this.provinceSandMapTotalLoad.some(value => value >= 1000000000 && value < 10000000000000)) {
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 100000000).toFixed(2)); // 亿吨
            this.provinceSandMapUnit = '亿吨'
          }else if (this.provinceSandMapTotalLoad.some(value => value >= 100000 && value < 1000000000)) {
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 10000).toFixed(2)); // 万吨
            this.provinceSandMapUnit = '万吨'
          }else if (this.provinceSandMapTotalLoad.every(value => value < 100000)) {
            this.provinceSandMapUnit = '吨'
          }
        }
        // // 找出 totalLoad 的最大值和最小值
        // const totalLoadMax = Math.max(...this.provinceSandMapTotalLoad);
        // const totalLoadMin = Math.min(...this.provinceSandMapTotalLoad);

        // // 找出 completed、transit、project 的最大值和最小值
        // const combinedValues = [
        //     ...this.provinceSandMapCompleted,
        //     ...this.provinceSandMapTranit,
        //     ...this.provinceSandMapProject
        // ];
        // const combinedMax = Math.max(...combinedValues);
        // const combinedMin = Math.min(...combinedValues);

        // // 计算 Y 轴的范围和间隔
        // const totalLoadRange = [totalLoadMin, totalLoadMax];
        // const combinedRange = [combinedMin, combinedMax];
        // const totalLoadInterval = (totalLoadMax - totalLoadMin) / 5;
        // const combinedInterval = (combinedMax - combinedMin) / 5;
        const freshColors = ['#1890FF', '#13CE66', '#FFBA00', '#FFA4A4','#827CFA'];
        var option = {
          title: {
            // text: '河北省采砂统计'
          },
          color:freshColors,
          tooltip: {
            trigger: 'axis',
            // axisPointer: {
            //   type: 'cross',
            //   crossStyle: {
            //     color: '#999'
            //   }
            // }
          },
          // toolbox: {
          //   feature: {
          //     dataView: { show: true, readOnly: false },
          //     magicType: { show: false, type: ['line', 'bar'] },
          //     restore: { show: false },
          //     // saveAsImage: { show: true }
          //   },
          //   right:"5%",
          // },
          legend: {
            data: ['累计采砂量', '在运单数', '核销单数', '项目数量'],
            left: 'center',
            orient: 'horizontal'
          },
          grid: {
              left: '5%',    // 距离左边的距离可以是像素值或者百分比
              right: '5%',   // 距离右边的距离
              top: '10%',     // 距离顶部的距离
              bottom: '15%'   // 距离底部的距离
          },
          xAxis: [
            {
              type: 'category',
              data: this.provinceSandMapXAxis,
              axisPointer: {
                type: 'shadow'
              },
              axisLine: {
                lineStyle: {
                    color: '#808080'
                }
              },
              axisLabel: {
                  textStyle: {
                      color: '#808080'
                  },
                  rotate: 30,
                  interval: 0
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '累计采砂量(' + this.provinceSandMapUnit +')',
              // min: totalLoadRange[0],
              // max: totalLoadRange[1] + totalLoadInterval,
              // interval: totalLoadInterval,
              axisLabel: {
                formatter: '{value} '
              },
              splitLine: {
                show: false,
                lineStyle: {
                    color: '#e6e6e6'
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                    color: '#808080'
                }
              },
              axisTick: {
                show: true,
              }
            },
            {
              type: 'value',
              name: '个数',
              // min: combinedRange[0],
              // max: combinedRange[1] + combinedInterval,
              // interval: combinedInterval,
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: false // 不显示x轴网格线
              },
              axisLine: {
                show: true,
                lineStyle: {
                    color: '#808080'
                }
              },
              axisTick: {
                show: true,
              }
            }
          ],
          series: [
            {
              name: '累计采砂量',
              type: 'line',
              tooltip: {
                valueFormatter: function (value) {
                  return value + this.provinceSandMapUnit;
                }
              },
              data: this.provinceSandMapTotalLoad
            },
            {
              name: '在运单数',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + '个';
                }
              },
              data: this.provinceSandMapTranit
            },
            {
              name: '核销单数',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 个';
                }
              },
              data: this.provinceSandMapCompleted
            },
            {
              name: '项目数量',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 个';
                }
              },
              data: this.provinceSandMapProject
            }
          ]
        };
        this.provinceSandMap.setOption(option)
        if(this.provinceSandMapXAxis.length > 11){
          // 动态添加dataZoom
          this.provinceSandMap.setOption({
              dataZoom: [
              {
                type: 'inside',
                start: 0,
                end: 50,
                bottom: 5,
                height: 20,     // 组件的高度
                backgroundColor: 'rgba(47,69,84,0)', // 背景颜色
                fillerColor: 'rgba(167,183,204,0.4)', // 填充颜色
                handleColor: 'rgba(89,105,128,0.7)', // 滑块颜色
              },
              {
                start: 0,
                end: 50,
                bottom: 5,
                height: 20,
                backgroundColor: 'rgba(47,69,84,0)', // 背景颜色
                fillerColor: 'rgba(167,183,204,0.4)', // 填充颜色
                handleColor: 'rgba(89,105,128,0.7)', // 滑块颜色
              }
            ],
          });
        }
      })
    },

    //获取检索开始日期
    getStartOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth()+1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if(value == '本年'){
        return `${currentYear}-01-01`; // 返回格式为 yyyy-MM-dd
      }else if(value == '本月'){
        return `${currentYear}-${currentMonth}-01`;
      }else{
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    //获取检索结束日期
    getEndOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth()+1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if(value == '本年'){
        return `${currentYear}-12-31`; // 返回格式为 yyyy-MM-dd
      }else if(value == '本月'){
        return `${currentYear}-${currentMonth}-31`;
      }else{
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    //根据吨数换算采砂重量单位
    weightConversion(load){
      if (load == null)
        return 0;
      if (load < 100000) {
        return (load)
      }
      else if ( load >= 100000 && load < 1000000000){
        return (load / 10000).toFixed(2)
      }
      else if (load >= 1000000000 && load <10000000000000){
        return (load / 100000000).toFixed(2)
      }
      else{
        return (load / 1000000000000).toFixed(2)
      }
    },

    //项目地址change后更新城市采砂信息图表
    getAreaName(areaName) {
      console.log("项目地址更新执行方法:" + areaName)
      this.queryParams.areaName = areaName
      this.citySandMapName = areaName
      if(this.queryParams.areaCode == ''){
        return
      }
      this.getProvinceLoadByYear()
    },
    //时间选择器change后更新城市采砂信息图表
    handleDateChange(value){
      console.log("时间选择器执行方法:" + value)

      this.selectedTime ='';
      if(this.projectSandFlag){
      }else{
        this.provinecSandStartTime = value[0];
        this.provinecSandEndTime = value[1];
        this.getProvinceLoadByYear();
      }

    },
    //时间选择器默认选择最近一个月
    getLastMonthStart() {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
      return start;
    },
    //时间格式化
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // 格式化为 yyyy-MM-dd
      },
    //时间快捷切换
    handleTimeChange(value) {
      this.timeRange = [this.getStartOfTime(value),this.getEndOfTime(value)]
      if(this.projectSandFlag){
      }else{
        this.provinecSandStartTime = this.getStartOfTime(value);
        this.provinecSandEndTime = this.getEndOfTime(value);
        this.getProvinceLoadByYear();
      }

    },

    /** 项目列表-多选框选中数据 */
    handleSelectionChange() {
      this.deptIds = this.$refs.projectTable.getSelectRowKeys();
      this.single = this.deptIds.length!==1;
      this.multiple = !this.deptIds.length;
    },
    handleSortChange(opt,obj){
      this.queryParams.sidx = obj.sidx
      this.queryParams.order = obj.order
    }
  }
};
</script>

<style scoped>
*{
  margin: 0;
}

.card-box {
  /* padding-bottom: 20px; */
}


.cardOne {
  background-color: #1890FF;
  height: 120px;
  position: relative;
}

.content {
  color: #FFFFFF;
  font-size: 38px;
  font-weight: bold;
  padding-bottom: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.title {
  color: #FFFFFF;
  text-align: left;
}
.icon {
  font-size: 80px;
  position: absolute;
  top: 20px;
  right: 15px;
  color: rgba(0,0,0,0.15);
  transition: all .3s linear;
}

.card-box:hover .icon
{
  font-size: 90px;
  position: absolute;
  top: 15px;
  right: 15px;
}

.cardTwo {
  background-color: #13CE66;
  height: 120px;
  position: relative;

}
.cardFive {
  background-color: #FFBA00;
  height: 120px;
  position: relative;

}
.cardThree {
  background-color: #FFA4A4;
  height: 120px;
  position: relative;

}
.cardFour {
  background-color: #827CFA;
  height: 120px;
  position: relative;

}

.el-card ::v-deep .el-card__header {
  border-bottom: none;
  min-height: 0px;
  padding: 10px 20px 0px 20px;
}

.el-card ::v-deep .el-card__body {
  padding: 10px 20px 0px 20px;
}

@media (min-width: 992px) {

/* 中等屏幕的断点 */
.custom-md-col {
  width: 20%;
  /* 您自定义的中等屏幕宽度 */
}
}

@media (min-width: 1200px) {

/* 超大屏幕的断点 */
.custom-xl-col {
  width: 20%;
  /* 您自定义的超大屏幕宽度 */
}
}

.custom-form-item >>> .el-form-item__content {
  width: 80% !important;
}



.el-table-auto-height {
  height: auto;
}
</style>
