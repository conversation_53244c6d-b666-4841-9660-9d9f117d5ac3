<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px" @submit.native.prevent>
      <el-form-item  prop="name" label="模板名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入模板名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sms:template:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sms:template:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sms:template:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/sms/template/page" ref="templateTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column label="模板名称" min-width="80" prop="name" sortable="custom" column-key="NAME"></el-table-column>
      <el-table-column label="模板类型" min-width="80" prop="type" sortable="custom" column-key="TYPE">
        <template slot-scope="scope">
          <my-view pvalue="smsType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="模板编码" min-width="80" prop="code" sortable="custom" column-key="CODE"></el-table-column>
      <el-table-column label="模板内容" min-width="80" prop="content" sortable="custom" column-key="CONTENT"></el-table-column>
      <el-table-column label="更新时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
      <el-table-column label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            title="修改"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sms:template:update']"
          ></el-button>
          <el-button
            size="mini"
            title="删除"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sms:template:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改短信模板表 对话框 -->
    <my-dialog  v-el-drag-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="template" label-width="80px">
        <my-form-item label="模板名称" prop="name" :rules="[{notNull:true,message:'请输入模板名称'}]">
          <my-input v-model="template.name" placeholder="请输入模板名称"/>
        </my-form-item>
        <my-form-item label="模板类型" prop="type"  :rules="[{notNull:true,message:'请选择模板类型',trigger:'change'}]">
          <my-select  id="type" pvalue="smsType"  v-model="template.type" placeholder="-模板类型-"/>
        </my-form-item>
        <my-form-item label="模板编码"  prop="code" :rules="[{notNull:true,message:'请输入模板编码'}]">
          <my-input v-model="template.code" placeholder="请输入模板编码"/>
        </my-form-item>
        <my-form-item label="模板内容"  prop="content" :rules="[{notNull:true,message:'请输入模板内容'}]">
          <el-input type="textarea" id="content" class="form-control" rows="5" style="width:380px" v-model="template.content" placeholder="模板内容" maxlength="512"></el-input>
        </my-form-item>
        <my-form-item label="备注" prop="remark">
          <el-input type="textarea" id="remark" class="form-control" rows="5" style="width:380px" v-model="template.remark" placeholder="备注" maxlength="255"></el-input>
        </my-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getTemplate, delTemplate, delTemplateBatch,addTemplate, updateTemplate } from "@/api/sms/template";
import elDragDialog from '@/directive/dialog/drag'
export default {
  name: "Template",
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      template: {},
    };
  },
  methods: {
    /** 查询短信模板表 列表 */
    reload(restart) {
      this.$refs.templateTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple=true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.template = {
        ID: '',
        CREATE_USER_ID: '',
        CREATE_TIME: '',
        UPDATE_USER_ID: '',
        UPDATE_TIME: '',
        NAME: '',
        TYPE: '',
        CODE: '',
        CONTENT: '',
        REMARK: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.templateTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加短信模板表 ";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getTemplate(id).then(r => {
        this.template = r.template;
        this.open = true;
        this.title = "修改短信模板表 ";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中')
          if (this.template.id) {
            updateTemplate(this.template).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          } else {
            addTemplate(this.template).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        this.$modal.loading('加载中')
         if(row.id) {
          return delTemplate(row.id);
        }else{
          return delTemplateBatch(this.ids);
        }
      }).then(() => {
        this.$modal.closeLoading()
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
  }
};
</script>
