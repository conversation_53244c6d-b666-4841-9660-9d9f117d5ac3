<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <my-input style="width: 205px" v-model.trim="queryParams.name" placeholder="项目名称"
          @keyup.enter.native="handleQuery('project')" />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input style="width: 205px" v-model.trim="queryParams.sectionName" placeholder="标段名称"
          @keyup.enter.native="handleQuery('project')" />
      </el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <my-select id="type" pvalue="projectType" v-model="queryParams.type" placeholder="项目类型" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery('project')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery('queryForm')">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/billAdditionRecord/pages" :fixed="true" ref="projectTable" row-key="deptId"
      cell-class-name="el-table-max-cell2" @my-selection-change="handleSelectionChange">
      <el-table-column label="项目名称" header-align="center" align="left" fixed="left" min-width="250" prop="name"
        sortable="custom" column-key="project.NAME">
        <template #default="scope">
          <!-- <el-tooltip :content="scope.row.name" placement="top" effect="light">
            <div>{{ scope.row.name }}</div>
          </el-tooltip> -->
          <div>{{ scope.row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="标段名称" align="left" fixed="left" header-align="center" min-width="160" prop="sectionName"
        sortable="custom" column-key="project.SECTION_NAME">
      </el-table-column>
      <el-table-column label="项目位置" align="left" header-align="center" min-width="160" prop="areaName" sortable="custom"
        column-key="project.AREA_NAME">
      </el-table-column>
      <el-table-column label="项目类型" align="center" header-align="center" min-width="160" prop="type" sortable="custom"
        column-key="project.TYPE">
        <template slot-scope="scope">
          <my-view pvalue="projectType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="采砂人（供砂人）" header-align="center" align="center" min-width="150" prop="leaderName"
        sortable="custom" column-key="project.LEADER_NAME">
      </el-table-column>
      <el-table-column label="每日授权开单数量" header-align="center" align="right" min-width="150" prop="dailyBillCount"
        sortable="custom" column-key="project.DAILY_BILL_COUNT">
      </el-table-column>
      <el-table-column label="当日追加数量" header-align="center" align="right" min-width="150" prop="addCount"
        sortable="custom" column-key="billAdditionRecord.ADD_COUNT">
      </el-table-column>
      <el-table-column label="当日已开单数量" header-align="center" align="right" min-width="120" prop="billCount"
        :sortable="false" column-key="bill.billCount">
      </el-table-column>
      <el-table-column label="当日已开单量（吨）" header-align="center" align="right" min-width="120" prop="dailyVehicleLoad"
        :sortable="false">
        <template #default="scope">
          {{common.toThousands(scope.row.dailyVehicleLoad,2,',')}}
        </template>
      </el-table-column>

      <el-table-column label="当月已开单数量" header-align="center" align="right" min-width="120" prop="monthBillCount"
        :sortable="false">
      </el-table-column>
      <el-table-column label="当月已开单量（吨）" header-align="center" align="right" min-width="120" prop="monthVehicleLoad"
        :sortable="false">
        <template #default="scope">
          {{common.toThousands(scope.row.monthVehicleLoad,2,',')}}
        </template>
      </el-table-column>
      <el-table-column label="累计开单数量" header-align="center" align="right" min-width="120" prop="totalBillCount"
        :sortable="false">
      </el-table-column>
      <el-table-column label="累计开单量（吨）" header-align="center" align="right" min-width="120" prop="totalVehicleLoad"
        :sortable="false">
        <template #default="scope">
          {{common.toThousands(scope.row.totalVehicleLoad,2,',')}}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-document-copy" title="追加开单数量"
            @click="handleView(scope.row)" v-hasPermi="['project:billAdditionRecord:add']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-edit" title="开单配置"
            @click="handleSetCount(scope.row)" v-hasPermi="['project:billAdditionRecord:setCount']"></el-button>
        </template>
      </el-table-column>
    </my-table>
    <!-- 设置每日开单授权数量 -->
    <my-dialog :title="title2" :visible.sync="open2" width="800px" append-to-body>
      <el-form ref="project" :model="project" label-width="180px">
        <my-form-item label="项目名称" ref="name" prop="name">
          <my-input v-model="project.name" disabled placeholder="请输入项目名称" />
        </my-form-item>
        <my-form-item label="每日授权开单数量" ref="dailyBillCount" prop="dailyBillCount"
          :rules="[{notNull:true,message:'请输入每日授权开单数量'}, {isDigit:true, message: '只能是整数', trigger:['change','blur']},]">
          <my-input v-model="project.dailyBillCount" placeholder="请输入每日授权开单数量" @input="$forceUpdate()" />
        </my-form-item>
        <my-form-item label="经办人签名有效时长(分钟)" ref="leaderAuthDuration" prop="leaderAuthDuration"
          :rules="[{isDigit:true, message: '只能是整数', trigger:['change','blur']}]">
          <my-input v-model="project.leaderAuthDuration" placeholder="请输入经办人签名有效时长(分钟)" @input="$forceUpdate()" />
        </my-form-item>
        <my-form-item label="监理签名授权时长(分钟)" ref="supervisorAuthDuration" prop="supervisorAuthDuration"
          :rules="[{isDigit:true, message: '只能是整数', trigger:['change','blur']}]">
          <my-input v-model="project.supervisorAuthDuration" placeholder="请输入监理签名授权时长(分钟)" @input="$forceUpdate()" />
        </my-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('project')">确 定</el-button>
        <el-button @click="cancel('project')">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 添加或修改项目开单数量追加记录对话框 -->
    <my-dialog :title="title" @close="close" :visible.sync="open" width="1000px" height="70vh" append-to-body>
      <el-form :model="queryParams1" ref="queryForm1" size="small" :inline="true" v-show="showSearch" label-width="68px"
        @submit.native.prevent>
        <el-form-item label="日期" prop="createTime">
          <el-date-picker v-model="queryParams1.createTime" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
            placeholder="选择日期" :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini"
            @click="handleQuery('billAdditionRecord')">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery('queryForm1')">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()"
            v-hasPermi="['project:billAdditionRecord:save']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['project:billAdditionRecord:delete']">删除
          </el-button>
        </el-col>
      </el-row>
      <my-table :url="'/project/billAdditionRecord/page/'+ projectId" maxHeight="390px" :autoRequest="false"
        ref="billAdditionRecordTable" row-key="id" @my-selection-change="handleSelectionChange1">
        <el-table-column label="追加数量" min-width="80" header-align="center" align="right" prop="addCount"
          sortable="custom" column-key="ADD_COUNT"></el-table-column>
        <el-table-column label="日期" min-width="80" header-align="center" align="center" prop="createTime"
          sortable="custom" column-key="CREATE_TIME"></el-table-column>
        <el-table-column label="追加人" min-width="80" header-align="center" align="center" prop="showName"
          sortable="custom" column-key="user.SHOW_NAME"></el-table-column>
      </my-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </my-dialog>

    <my-dialog top="30vh" :title="title1" :visible.sync="open1" width="700px" append-to-body>
      <el-form ref="form" :model="billAdditionRecord" label-width="80px">
        <my-form-item label="追加数量" ref="addCount" prop="addCount"
          :rules="[{notNull:true,message:'请输入追加数量'},{isDigit:true, message: '只能是整数', trigger:['change','blur']},]">
          <my-input v-model="billAdditionRecord.addCount" placeholder="请输入追加数量" :maxlength="11" />
        </my-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('billAdditionRecord')">确 定</el-button>
        <el-button @click="cancel('billAdditionRecord')">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
  import {
    getBillAdditionRecord,
    delBillAdditionRecord,
    delBillAdditionRecordBatch,
    addBillAdditionRecord,
    updateBillAdditionRecord,
    setCount
  } from '@/api/project/billAdditionRecord'
  import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
  import {
    formatDate
  } from '@/utils'
  import {
    getProjectConfig
  } from '@/api/project/project'
  import Template from '@/views/sms/template/index.vue'
  import common from '../../../utils/common'

  export default {
    name: "BillAdditionRecordManage",
    computed: {
      common() {
        return common
      }
    },
    components: {
      Template,
      MyAreaSelect
    },
    data() {
      return {
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 弹出层标题
        title: "",
        title1: "",
        title2: "",
        // 是否显示弹出层
        open: false,
        open1: false,
        open2: false,
        // 查询参数
        queryParams: {
          name: '',
          areaCode: '',
          type: '',
          sectionName: '',
        },
        queryParams1: {
          createTime: this.getNowFormatDay(),
        },
        projectId: '',
        // 表单参数
        billAdditionRecord: {},
        project: {},
        createTime: '',
        projectConfig: {}
      };
    },
    mounted() {},
    methods: {
      //时间格式化
      getNowFormatDay() {
        var date = new Date();
        var nowMonth = date.getMonth() + 1;
        var strDate = date.getDate();
        var seperator = "-";
        if (nowMonth >= 1 && nowMonth <= 9) {
          nowMonth = "0" + nowMonth;
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
        }
        return date.getFullYear() + seperator + nowMonth + seperator + strDate;
      },
      /** 查询项目开单数量追加记录列表 */
      reload(restart, params) {
        if (params == 'project') {
          this.$refs.projectTable.search(this.queryParams, restart);
        }
        if (params == "billAdditionRecord") {
          this.$nextTick(() => {
            this.$refs.billAdditionRecordTable.search(this.queryParams1, restart);
          })
        }
        this.single = true;
        this.multiple = true;
      },
      // 取消按钮
      cancel(params) {
        if (params == "billAdditionRecord") {
          this.open1 = false;
        }
        if (params == "project") {
          this.open2 = false;
        }
        this.reset(params);

      },
      // 表单重置
      reset(params) {
        if (params == "project") {
          this.project = {
            deptId: '',
            dailyBillCount: 0,
            name: '',
            supervisorAuthDuration: null,
            leaderAuthDuration: null
          };
        }
        if (params == 'billAdditionRecord') {
          this.billAdditionRecord = {
            id: '',
            createUserId: '',
            createTime: '',
            projectId: '',
            addCount: null,
            deleteUserId: '',
            deleteTime: '',
            deleted: null
          };
        }
        this.resetForm(params);
      },
      /** 搜索按钮操作 */
      handleQuery(params) {
        this.reload(true, params);
      },
      /** 重置按钮操作 */
      resetQuery(params) {
        this.resetForm(params);
        if (params == "queryForm") {
          this.handleQuery('project');
        } else {
          this.handleQuery('billAdditionRecord');
        }
      },
      //关闭按钮
      close() {
        this.open = false;
        this.resetQuery('queryForm1')
        this.resetQuery('queryForm')
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = this.$refs.projectTable.getSelectRowKeys()
        this.single = this.ids.length != 1
        this.multiple = !this.ids.length
      },
      // 多选框选中数据
      handleSelectionChange1(selection) {
        this.ids = this.$refs.billAdditionRecordTable.getSelectRowKeys()
        this.single = this.ids.length != 1
        this.multiple = !this.ids.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.open1 = true;
        this.title1 = "追加开单数量";
      },
      handleView(row) {
        this.open = true;
        this.title = "追加开单数量";
        this.billAdditionRecord.projectId = row.deptId
        this.projectId = row.deptId
        this.handleQuery('billAdditionRecord');
      },
      async handleSetCount(row) {
        await getProjectConfig(row.deptId).then(r => {
          if (r.projectConfig) {
            this.projectConfig = r.projectConfig
          }
        });
        this.open2 = true;
        this.title2 = "项目开单配置";
        this.project.deptId = row.deptId
        this.project.name = row.name
        this.project.dailyBillCount = row.dailyBillCount
        this.project.leaderAuthDuration = this.projectConfig.leaderAuthDuration;
        this.project.supervisorAuthDuration = this.projectConfig.supervisorAuthDuration;
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids[0];
        getBillAdditionRecord(id).then(r => {
          this.billAdditionRecord = r.billAdditionRecord;
          this.open = true;
          this.title = "修改项目开单数量追加记录";
        });
      },
      /** 提交按钮 */
      submitForm(params) {
        if (params == "billAdditionRecord") {
          this.$refs["form"].validate((valid, errorObj) => {
            if (valid) {
              if (this.billAdditionRecord.id) {
                updateBillAdditionRecord(this.billAdditionRecord).then(r => {
                  this.$modal.msgSuccess("修改成功");
                  this.open1 = false;
                  this.reload(true, params);
                });
              } else {
                addBillAdditionRecord(this.billAdditionRecord).then(r => {
                  this.$modal.msgSuccess("新增成功");
                  this.open1 = false;
                  this.reload(true, params);
                  this.billAdditionRecord.addCount = null
                });
              }
            } else {
              this.$scrollView(errorObj);
            }
          });
        }
        if (params == "project") {
          this.$refs["project"].validate((valid, errorObj) => {
            if (valid) {
              ;
              setCount(this.project).then(res => {
                this.$modal.msgSuccess("修改成功");
                this.open2 = false;
                this.reload(true, params);
                this.project.dailyBillCount = 0
              });
            } else {
              this.$scrollView(errorObj);
            }
          });
        }
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this;
        this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
          if (row.id) {
            return delBillAdditionRecord(row.id);
          } else {
            return delBillAdditionRecordBatch(that.ids);
          }
        }).then(() => {
          this.reload(true, 'billAdditionRecord');
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
    },
    activated() {
      //组件被激活时重绘表格
      this.$refs.billAdditionRecordTable.changeTableHeight();
    },
  };
</script>
