<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item>
        <my-quarter-select style="width: 573px;height: 0"  ref="myQuarterSelect"  v-model="params" @change="handleChange"></my-quarter-select>
      </el-form-item>
      <el-form-item label="所属区域" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="数据状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择数据状态"
          pvalue="dataStatus"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              :cell-style="{padding: '0px'}" url="/reports/responsiblePersionReport/waitShiPage" :default-query-params="queryParams" ref="responsiblePersionReportTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="所在市" min-width="120" prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="所在县/区" min-width="120" prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="年度" min-width="120" prop="year" header-align="center" align="center" sortable="custom" column-key="YEAR"></el-table-column>
      <el-table-column  label="季度" min-width="120" prop="quarter" header-align="center" align="center" sortable="custom" column-key="QUARTER"></el-table-column>
      <el-table-column  label="数据状态" min-width="120" prop="status" header-align="center" align="center" sortable="custom" column-key="STATUS">
        <template  #default="scope">
          <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            class="btn-table-operate"
            icon="el-icon-finished"
            title="审核"
            @click="handleFill(scope.row)"
            v-has-permi="['reports:responsiblePersionReport:shiExamine']"
          >审核</el-button>
        </template>
      </el-table-column>
    </my-table>
    <responsibleDetils
      :detailsOpen="detailsOpen"
      @close="close"
      :report-id="reportId"
      :status="status"
      :isAudit="true"
      :isCityExamine="true"
      :title="title"
      :process-list="processList"
      :version="String(version)"/>
  </div>
</template>

<script>
import {
  getResponsiblePersionReport,
  delResponsiblePersionReport,
  delResponsiblePersionReportBatch,
  addResponsiblePersionReport,
  updateResponsiblePersionReport,
  importResponsiblePersionReport, checkPassXian
} from '@/api/reports/responsiblePersionReport'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyQuarterSelect from '@/components/YB/MyQuarterSelect.vue'
import Template from '@/views/sms/template/index.vue'
import { getProcessList } from '@/api/reports/responsiblePersion'
import ResponsibleDetils from '@/views/reports/commponents/responsibleDetils.vue'

export default {
  name: "responsiblePersionCityExamine",
  components: { ResponsibleDetils, Template, MyQuarterSelect, MyAreaSelect },
  data() {
    return {
      detailsOpen:false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showSearchView: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        status: '',
        areaCode: '',
      },
      params:{},
      queryParamsView: {},
      // 表单参数
      responsiblePersionReport: {},
      reportId:'',
      version:'',
      status:'',
      activeName:'1',
      processList:[]
    };
  },
  mounted() {
  },
  methods: {
    close(){
      this.detailsOpen = false;
      this.$refs.responsiblePersionReportTable &&this.$refs.responsiblePersionReportTable.search(this.queryParams);
      this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.search(this.queryParamsView);
    },
    /** 查询采砂监管责任人填报表列表 */
    reload(restart) {
      this.$refs.responsiblePersionReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 表单重置
    reset() {
      this.responsiblePersionReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        year: '',
        quarter: '',
        status: '',
        reportUserId: '',
        reportTime: '',
        version: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.myQuarterSelect.resetQuery();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.responsiblePersionReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采砂监管责任人填报表";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        if(row.id) {
          return delResponsiblePersionReport(row.id);
        }else{
          return delResponsiblePersionReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleFill(row){
      this.detailsOpen = true;
      this.title = "采砂监管责任人市级审核";
      this.responsiblePersionReport = row;
      this.reportId = row.id;
      this.status = row.status;
      this.version = row.version;
      this.handleClick()
    },
    handleClick(){
      this.processList = []
      getProcessList(this.reportId).then(res=>{
        this.processList = res.list
      })
    },
    handleChange(value){
      this.queryParams.year = value.year;
      this.queryParams.quarter =  value.quarter;
    },
    change(e){
      this.$forceUpdate()
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.responsiblePersionReportTable && this.$refs.responsiblePersionReportTable.changeTableHeight();
    this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.changeTableHeight();
  },
};
</script>
<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
