<template>
  <div>
    <svg-icon class="menuIcon" :icon-class="icon"></svg-icon>
      <span v-if="title.length > 5" slot="title" :title=title>{{ title }}</span>
      <span v-else slot="title">{{ title }}</span>
    <template v-if="todoCount > 0">
      <el-tag v-if="menuType === 0" class="toDoUrlTagParent" size="small" effect="dark">NEW</el-tag>
      <el-tag v-else class="toDoUrlTagChildren" size="small" effect="dark">{{ todoCount }}</el-tag>
    </template>
  </div>
</template>
<script>
export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    menuType: {
      type: Number,
      default: null
    },
    todoUrl: {
      type: String,
      default: null,
    },
    todoCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
}
</script>
<style scoped>
/* 菜单图标 */
.menuIcon {
  margin-right: 20px!important;
  vertical-align: -0.23em!important;
}
/* 父级菜单待办提示 */
.toDoUrlTagParent {
  background-color: #f39c12;
  border-color: #f39c12;
  padding-left: 5px;
  padding-right: 5px;
  height: 18px;
  line-height: 16px;
  margin-left: 10px;
}
/* 子级菜单待办提示 */
.toDoUrlTagChildren {
  background-color: #f39c12;
  border-color: #f39c12;
  padding-left: 5px;
  padding-right: 5px;
  height: 18px;
  line-height: 16px;
  position: absolute;
  top: 20px;
  right: 15px;
}
</style>
