import request from '@/utils/request'

export function getVideoSurveillance(id) {
  return request({
    url: '/video/liveCamera/getVideoUrl/'+id,
    method: 'post',
  })
}

export function getKitToken(id) {
  return request({
    url: '/video/liveCamera/getKitToken/'+id,
    method: 'post',
  })
}

export function getTieTaUrl(id) {
  return request({
    url: '/video/liveCamera/getTieTaUrl/'+id,
    method: 'post',
  })
}

//获取云睿平台flv视频地址
export function getYunRunUrl(id){
  return request({
    url: '/video/liveCamera/getYunRunUrl/'+id,
    method: 'post',
  })
}
