import DictUtils from "@/utils/dictUtils";

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: '',
    pvalue: {//字典的pvalue
      type: String,
      default: '',
    },
    itemName:{
      default: 'name',
    },
    itemValue: {
      default: 'value',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    options: { //选项数组，有初始值时pvalue将无效
      type: Array,
      default: function () {
        return [];
      }
    },
  },
  data: function () {
    return {
      innerValue: '',
      finalOptions: [],

    }
  },
  watch: {
    value: function (nVal) {
      this.innerValue = nVal;
    },
    options: function (nVal) {
      if (nVal && nVal.length > 0) {
        this.finalOptions = nVal;
      } else if (!this.pvalue) {
        this.finalOptions = [];
      }
    },
    pvalue: function (nVal) {
      if (nVal && this.finalOptions.length == 0) {
        DictUtils.cList(nVal)
          .then(r => this.finalOptions = r[nVal]);
      }
    },
  },
  mounted: function () {
    this.innerValue = this.value;
    if (this.pvalue && this.pvalue != '' && this.options.length == 0) {
      DictUtils.cList(this.pvalue)
        .then(r => this.finalOptions = r[this.pvalue]);
    }else {
      this.finalOptions = this.options;
    }
  }
}
