<!-- 封装dialog组件，添加固定表头 -->
<template>
  <el-dialog v-if="open" v-bind="$attrs" v-on="$listeners" :fullscreen="dialogFullscreen" :show-close="false"
             :close-on-click-modal="closeOnClickModal"
             :visible.sync="open" @close="handleClose" :destroy-on-close="destroyOnClose" :close-on-press-escape="false"
             :z-index="zIndex" :style="!dialogFullscreen?'margin-top: '+top+'!important;':''">
    <template #title="{ close, titleId, titleClass }">
      <el-row>
        <el-col :span="18">
          <h3 style="margin:0" :id="titleId" :class="titleClass">{{ title }}</h3>
        </el-col>
        <el-col :span="6" style="text-align: right">
          <a class="a-icon" @click="dialogFullscreen=false">
            <i  v-if="dialogFullscreen" class="el-icon-document-copy"></i>
          </a>
          <a class="a-icon" @click="dialogFullscreen=true">
            <i  v-if="!dialogFullscreen" class="el-icon-full-screen"></i>
          </a>
          <a class="a-icon" @click="handleClose">
            <i class="el-icon-close"></i>
          </a>
        </el-col>
      </el-row>
    </template>
    <template #default>
      <div class="dialog-body" :style="'height:'+height">
        <slot></slot>
      </div>
    </template>
    <template #footer>
      <slot name="footer">
        <el-button @click="handleClose">关 闭</el-button>
      </slot>
    </template>
  </el-dialog>
</template>

<script>

export default {
  emits: ["close", "update:visible","screenChange"],
  name: "MyDialog",
  props: {
    title: '',
    visible: null,
    height: {
      type: String,
      default: 'auto'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: { //关闭后销毁其中的元素
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    top: {
      type: String,
      default: '0vh'
    }
  },
  data() {
    return {
      dialogFullscreen: false,
      open: false,
    }
  },
  watch: {
    open(nval) {
      this.$emit("update:visible", nval);
    },
    dialogFullscreen(nval) {
      this.$emit("screenChange", nval);
    },
    visible(nval) {
      this.open = nval;
    },
  },
  methods: {
    handleClose() {
      this.open = false;
      this.$emit('close')
    },
  },
  mounted() {
    if (this.visible !== null) {
      this.open = this.visible;
    }
  },

}
</script>

<style scoped>
.a-icon:hover {
  color: #00a0e9;
}

.a-icon {
  margin: 0 5px;
}

::v-deep .el-dialog__body {
  margin: 0 -20px!important;
}

.dialog-body {
  /*margin: -30px -20px;*/
  overflow-y: auto;
  padding: 20px 20px;
}
</style>
