<template>
  <div v-if="showPrompt" class="prompt-modal">
    <div class="prompt-content">
      <div class="prompt-header">
        <h3>采运单有{{sum}}条异常记录，请及时处理 </h3>
        <span class="close-btn" @click="closePrompt">×</span>
      </div>
      <div class="prompt-body">
        <p class="cyd" v-for="item in tlist">采运单号{{item.billNo}}异常警告</p>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    getBillData,
  } from "@/api/tablebbtb/bbtb";
export default {
  data() {
    return {
      sum:0,
      showPrompt: false,
      tlist:[],
      queryParams: {
        billNo: '',
        destination: '',
        areaCode: '',
        status: 'abnormal',
        driverName: '',
        carNumber: '',
        name: '',
        areaName: '',
        sectionName: '',
        type: '',
        signStartDateTime: '',
        signEndDateTime: ''
      }
    };
  },
  mounted() {
    // this.checkPromptDate();
  },
   watch: {
      '$route'() {
         this.checkPromptDate();
      }
    },
  methods: {


    // 检查是否需要显示弹窗
    checkPromptDate() {
      // 从本地存储获取关闭记录
      if(this.$store.state.user.deptcode.length > 9){
      const closedDate = localStorage.getItem('cydclose');
      getBillData(this.queryParams).then((res) => {
          let tableData = res.page.list.length;
          this.sum= res.page.list.length;
          this.tlist=res.page.list;
          console.log(res,'这是异常弹窗获取的')
          if(tableData>0){
            if (closedDate != 'false') {
              this.showPrompt = true;
            }
          }
        });
      }else{
        this.showPrompt = false;
      }
    },

    // 关闭弹窗
    closePrompt() {
      this.showPrompt = false;
      // 存储关闭月份到本地存储
      localStorage.setItem('cydclose', 'false');
    }
  }
};
</script>

<style scoped>
  .cyd{
    color: #F56C6C !important;
  }
.prompt-modal {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 999;
}

.prompt-content {
  width: 300px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #ebeef5;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.prompt-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-btn {
  font-size: 20px;
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #409eff;
}

.prompt-body {
  padding: 15px;
  font-size: 14px;
  color: #606266;
}

.prompt-body p {
  margin: 0;
  line-height: 1.5;
}
</style>
