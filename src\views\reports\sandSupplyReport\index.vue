<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item>
        <my-quarter-select style="width: 573px;height: 0"  ref="myQuarterSelect"  v-model="params" @change="handleChange"></my-quarter-select>
      </el-form-item>
      <el-form-item label="所属市" prop="areaCode">
        <my-select
          v-model="queryParams.areaCode"
          placeholder="请选择所属市"
          size="small"
          :options="areaOptions"
          item-name="areaName"
          item-value="areaCode"
        />
      </el-form-item>
      <el-form-item label="填报状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择填报状态"
          size="small"
          pvalue="fillStatus"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          :disabled="!params.quarter"
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-has-permi="['reports:sandSupplyReport:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              show-summary
              :summary-method="getSummaries"
              @query-success="handleQuerySuccess"
              :cell-style="{padding: '0px'}" url="/reports/sandSupplyReport/reportPage" :show-pager="false" :show-radio="false" :multiselect="false"  :fixed="true" :default-query-params="queryParams" ref="sandSupplyReportTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="所属市" min-width="100" fixed="left" prop="areaName" header-align="center" ></el-table-column>
      <el-table-column  label="年度" min-width="100" prop="year" header-align="center" align="center" ></el-table-column>
      <el-table-column  label="季度" min-width="100" prop="quarter" header-align="center" align="center"   >
        <template #default="{ row }">
          <my-view pvalue="quarter" :value="row.quarter" ></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="填报状态" min-width="100" prop="status"  header-align="center" align="center" >
        <template #default="{ row }">
          <my-view pvalue="fillStatus" :value="row.status" ></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="本期开采量（采砂/万吨）" min-width="120" header-align="center" align="right"  prop="quarterSandTotal" >
        <template #default="{row}">
          {{toThousands(row.quarterSandTotal)}}
        </template>
      </el-table-column>
      <el-table-column  label="本年度累计开采量（采砂/万吨）" min-width="120" header-align="center" align="right"  prop="yearSandTotal">
        <template #default="{row}">
          {{toThousands(row.yearSandTotal)}}
        </template>
      </el-table-column>
      <el-table-column  label="本期利用量（弃砂/万吨）" min-width="120" header-align="center" align="right"  prop="quarterAbandonTotal">
        <template #default="{row}">
          {{toThousands(row.quarterAbandonTotal)}}
        </template>
      </el-table-column>
      <el-table-column  label="本年度累计利用量（弃砂/万吨）" min-width="120" header-align="center" align="right" prop="yearAbandonTotal">
        <template #default="{row}">
          {{toThousands(row.yearAbandonTotal)}}
        </template>
      </el-table-column>
      <el-table-column  label="当地河砂出售价格(元/吨)" min-width="110" header-align="center" align="right"  prop="price" >
        <template #default="{row}">
          {{row.price?common.toThousands(row.price, 2, ','):common.toThousands(0, 2, ',')}}
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="success"
            title="填报"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['reports:sandSupplyReport:update']"
          >填报</el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改砂石量统计-砂石供应报表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="sandSupplyReport"  label-width="200px">
        <my-form-item label="区域" ref="areaCode" prop="areaCode" >
          <my-select
            v-model="sandSupplyReport.areaCode"
            placeholder="请选择所属市"
            :disabled="true"
            size="small"
            :options="areaOptions"
            item-name="areaName"
            item-value="areaCode"
          />
        </my-form-item>
        <my-form-item label="当地河砂出售价格（元/吨）" ref="price" prop="price" :rules="[
          {notNull:true,message:'请输入当地河砂出售价格(元/吨)'},
          {regExp: /^(0*[1-9]\d*(\.\d+)?|0+\.\d*[1-9]\d*)$/, message: '必须大于0,且只能为数字',trigger:['blur','change']}
          ]">
          <my-input v-model.trim="sandSupplyReport.price" :maxlength="16" placeholder="请输入当地河砂出售价格(元/吨)" />
        </my-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getSandSupplyReport, delSandSupplyReport, delSandSupplyReportBatch,addSandSupplyReport, updateSandSupplyReport } from "@/api/reports/sandSupplyReport";
import MyQuarterSelect from '@/components/YB/MyQuarterSelect.vue'
import { getSubList } from '@/api/system/area'
import common from '../../../utils/common'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'

export default {
  name: "SandSupplyReport",
  computed: {
    common() {
      return common
    },
    toThousands(){
      return function(data) {
        if(data){
          return  common.toThousands(data/10000, 2, ',')
        }else{
          return  common.toThousands(0, 2, ',')
        }
      }
    },
  },
  components: { MyAreaSelect, MyQuarterSelect },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        areaCode: '',
        status: '',
      },
      // 表单参数
      sandSupplyReport: {},
      params: {},
      areaOptions:[],
      totalList: [],
    };
  },
  mounted() {
      this.getAreaList();
  },
  methods: {
    async getAreaList(){
      const r = await getSubList('000_013')
      this.areaOptions = r.areaList
    },
    /** 查询砂石量统计-砂石供应报表列表 */
    reload(restart) {
      this.$refs.sandSupplyReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.sandSupplyReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        year: '',
        quarter: '',
        price: null ,
        status: '',
        reportUserId: '',
        reportTime: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.myQuarterSelect.resetQuery();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.sandSupplyReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加砂石供应报表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getSandSupplyReport(id).then(r => {
        this.sandSupplyReport = r.sandSupplyReport;
        this.sandSupplyReport.price = String(r.sandSupplyReport.price)
        this.open = true;
        this.title = "修改砂石供应报表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.sandSupplyReport.id) {
            updateSandSupplyReport(this.sandSupplyReport).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addSandSupplyReport(this.sandSupplyReport).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delSandSupplyReport(row.id);
        }else{
          return delSandSupplyReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleChange(value){
      this.queryParams.year = value.year;
      this.queryParams.quarter =  value.quarter;
    },
    handleExport(){
      this.download('/reports/sandSupplyReport/exportReportPage', {
        ...this.queryParams
      }, `砂石统计量_砂石供应_${new Date().getTime()}.xlsx`, 'application/json');
    },
    handleQuerySuccess(data){
      this.totalList = data.total
    },
    getSummaries(params){
      let showTotal = this.totalList;
      const { columns } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计';
            sums[5] = common.toThousands(showTotal.quarterSandTotal/10000, 2, ',')
            sums[6] = common.toThousands(showTotal.yearSandTotal/10000, 2, ',')
            sums[7] = common.toThousands(showTotal.quarterAbandonTotal/10000, 2, ',')
            sums[8] = common.toThousands(showTotal.yearAbandonTotal/10000, 2, ',')
            sums[9] ='（平均值）'+ common.toThousands(showTotal.price, 2, ',')
            return;
        }
      })
      return sums;
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.sandSupplyReportTable.changeTableHeight();
  },
};
</script>
