
<template>
  <div class="radio-group">
    <el-radio-group
      v-bind="$attrs"
      v-on="$listeners"
      v-model="innerValue"
      :disabled="disabled"
      :filterable="filterable"
      :multiple="multiple"
      @change="handleChange"
      style="width: 100%">
      <slot v-bind:options="finalOptions">
        <el-radio
          v-for="(item,index) in finalOptions"
          :key="item[itemValue]"
          :label="item[itemValue]"
          class="custom-radio">
          {{ item[itemName] }}
        </el-radio>
      </slot>
    </el-radio-group>
  </div>
</template>

<script>
import BaseMixin from "@/components/YB/mixins/BaseMixin";

export default {
  name: "MyRadioGroup",
  mixins: [BaseMixin],
  props:{
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  methods: {
      handleChange(val) {
        // this.$emit('input', val)
        this.$emit('mychange', val)
      }
    }
}
</script>

<style scoped>
.radio-group {
  /* padding: 10px; */
}
::v-deep .el-radio{
  line-height: 18px;
}
::v-deep .custom-radio {
  margin: 2px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: 1px solid #dcdfe6;
}

::v-deep .custom-radio:hover {
  background-color: #f5f7fa;
}

::v-deep .custom-radio.is-checked {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #409eff;
  box-shadow: 0 0 5px rgba(64,158,255,0.3);
}

::v-deep .custom-radio .el-radio__input.is-checked + .el-radio__label {
  color: inherit;
  font-weight: bold;
}
</style>
