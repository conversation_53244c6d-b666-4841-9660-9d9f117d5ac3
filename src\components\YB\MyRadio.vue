<template>
 <el-radio-group v-model="innerValue">
   <el-radio v-for="(item,index) in finalOptions" :disabled="disabled" @change="$emit('change',$event)" :label="item[itemValue]">{{item[itemName]}}</el-radio>
 </el-radio-group>
</template>

<script>
import BaseMixin from "@/components/YB/mixins/BaseMixin";

export default {
  name: "MyRadio",
  mixins:[BaseMixin]

}
</script>

<style scoped>

</style>
