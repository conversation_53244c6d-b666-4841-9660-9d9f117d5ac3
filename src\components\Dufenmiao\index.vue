<template>
  <div class="coordinate-container">
    <!-- 经度输入模块 -->
    <div class="coordinate-group">
      <el-tag :type="longSign === 1 ? 'success' : 'danger'"
              @click="toggleDirection('long')">
        经度
      </el-tag>
      <el-input-number
        v-model="longDeg"
        :min="0" :max="180"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled"/>
      <span>°</span>
      <el-input-number
        v-model="longMin"
        :min="0" :max="59"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled"/>
      <span>′</span>
      <el-input-number
        v-model="longSec"
        :min="0" :max="59"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled"/>
      <span>″</span>
    </div>

    <!-- 纬度输入模块 -->
    <div class="coordinate-group">
      <el-tag :type="latSign === 1 ? 'success' : 'danger'"
              @click="toggleDirection('lat')">
        纬度
      </el-tag>
      <el-input-number
        v-model="latDeg"
        :min="0" :max="90"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled" />
      <span>°</span>
      <el-input-number
        v-model="latMin"
        :min="0" :max="59"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled" />
      <span>′</span>
      <el-input-number
        v-model="latSec"
        :min="0" :max="59"
        controls-position="right"
        :controls="false"
        @change="emitUpdate" :disabled="mydisabled" />
      <span>″</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {  // 接收[longitude, latitude]格式
      type: Array,
      default: () => [0, 0],
      validator(val) {
        return val.length === 2 && val.every(v => typeof v === 'number')
      }
    },
    mydisabled:{
      type: Boolean,
      default: false,
      required: true
    }
  },
  data() {
    return {
      // 经度数据
      longSign: 1,
      longDeg: 0,
      longMin: 0,
      longSec: 0,

      // 纬度数据
      latSign: 1,
      latDeg: 0,
      latMin: 0,
      latSec: 0
    }
  },
  computed: {
    // 十进制坐标计算
    decimalCoordinates() {
      console.log(this.longDeg,this.longMin,this.longSec)
      return [
        this.longSign * (this.longDeg + this.longMin/60 + this.longSec/3600),
        this.latSign * (this.latDeg + this.latMin/60 + this.latSec/3600)
      ]
    }
  },
  watch: {
     value: {
          immediate: true,
          deep: true, // 监听数组内部变化‌:ml-citation{ref="1" data="citationList"}
          handler(newVal) {
            if (Array.isArray(newVal) && newVal.length >= 2) {
                    this.parseDecimal(newVal[0], 'long'); // ✅ 正确访问经度（索引0）
                    this.parseDecimal(newVal[1], 'lat');  // ✅ 正确访问纬度（索引1）
                  }
          }
        }
  },
  methods: {
    toggleDirection(type) {
      if(type === 'long') this.longSign *= -1
      else this.latSign *= -1
      this.emitUpdate()
    },
    parseDecimal(value, type) {
      if (typeof value !== 'number') {
          console.error(`Invalid ${type} value:`, value); // 明确报错
          return;
        }
        console.log('走到这里')
      const absVal = Math.abs(value)
      const sign = value >= 0 ? 1 : -1

      // 度分秒分解
      const deg = Math.floor(absVal)
      const min = Math.floor((absVal - deg) * 60)
      const sec = ((absVal - deg - min/60) * 3600).toFixed(2)
console.log('ddddddddddd',value)
      if(type === 'long') {
        this.longSign = sign
        this.longDeg = deg
        this.longMin = min
        this.longSec = sec
      } else {
        this.latSign = sign
        this.latDeg = deg
        this.latMin = min
        this.latSec = sec
      }
    },
    emitUpdate() {
      console.log(this.decimalCoordinates)
      this.$emit('input', this.decimalCoordinates)
    }
  }
}
</script>

<style scoped>
.coordinate-container {
  display: flex;
/*  flex-direction: column;
  gap: 16px; */
}
.coordinate-group {
  margin-left: 10px;
  height: 34px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.el-input-number {
  width: 50px;
}
.el-tag {
  cursor: pointer;
  user-select: none;
}
::v-deep .el-input-number.is-without-controls .el-input__inner{
  padding: 0;
}
</style>
