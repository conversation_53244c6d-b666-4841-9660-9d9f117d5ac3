import request from '@/utils/request'

// 查询采砂监管责任人表详细
export function getResponsiblePersion(id) {
  return request({
    url: '/reports/responsiblePersion/info/' + id,
    method: 'post'
  })
}

//流程记录
export function getProcessList(params) {
  return request({
    url: '/com/audit/queryList?ownerId=' + params,
    method: 'post',
  })
}

// 新增采砂监管责任人表
export function addResponsiblePersion(data) {
  return request({
    url: '/reports/responsiblePersion/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改采砂监管责任人表
export function updateResponsiblePersion(data) {
  return request({
    url: '/reports/responsiblePersion/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除采砂监管责任人表
export function delResponsiblePersion(id) {
  return request({
    url: '/reports/responsiblePersion/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除采砂监管责任人表
export function delResponsiblePersionBatch(ids) {
  return request({
    url: '/reports/responsiblePersion/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//县/区级审核通过
export function checkPass(data) {
  return request({
    url: '/reports/responsiblePersion/xianExamine',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//县/区级批量审核通过
export function checkPassBatch(data) {
  return request({
    url: '/reports/responsiblePersion/xianExamineBatch',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//市级审核通过
export function checkPassCity(data) {
  return request({
    url: '/reports/responsiblePersion/shiExamine',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//市级批量审核通过
export function checkPassBatchCity(data) {
  return request({
    url: '/reports/responsiblePersion/shiExamineBatch',
    data: data,
    method: 'post',
    showLoading: true,
  });
}



