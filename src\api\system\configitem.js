import request from '@/utils/request'

// 查询参数列表
export function getCategoryList() {
  return request({
    url: '/sys/configitem/category',
    method: 'post'
  })
}

// 新增参数配置
export function addConfig(data) {
  return request({
    url: '/sys/configitem/save',
    method: 'post',
    data: data
  })
}

// 查询参数详细
export function getConfig(configId) {
  return request({
    url: '/sys/configitem/info/' + configId,
    method: 'post'
  })
}

// 修改参数配置
export function updateConfig(data) {
  return request({
    url: '/sys/configitem/update',
    method: 'post',
    data: data
  })
}

// 删除配置项
export function delConfig(id) {
  return request({
    url: '/sys/configitem/delete/' + id,
    method: 'post'
  })
}
// 批量删除配置项
export function batchDelConfig(ids) {
  return request({
    url: '/sys/configitem/delete',
    method: 'post',
    data:ids,
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'post'
  })
}
// 校验配置项编码重复
export function checkCode(code,id) {
  return request({
    url: '/sys/configitem/checkCode/'+code+'?id='+id,
    method: 'post',
    headers:{
      allowRepeatSubmit: true,
    }
  })
}
