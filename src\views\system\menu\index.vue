<template>
  <div v-cloak>
    <el-row :gutter="10" style="margin: 10px">
      <el-col :sm="6">
        <el-card>
          <div slot="header">菜单导航</div>
          <div style="overflow:auto;height: 85%">
            <el-tree
              ref="leftTree"
              :data="menuList"
              :props="treeProps"
              node-key="menuId"
              :default-expanded-keys="expandedKeys"
              @node-click="handleLefTreeClick"
            ></el-tree>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="18">
        <el-card>
          <div slot="header">{{ title }}</div>
          <div style="overflow:auto;height: 85%;width: 550px;">
            <el-form ref="form" :model="menu"  label-width="80px">
              <el-form-item label="类型" prop="type">
                <el-radio-group v-model="menu.type">
                  <el-radio :disabled="!editAble" :label="0">目录</el-radio>
                  <el-radio :disabled="!editAble" :label="1">菜单</el-radio>
                  <el-radio :disabled="!editAble" :label="2">{{ menu.moduleType !== 'sysRes' ? '按钮' : '资源' }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <my-form-item
                v-if="menu.type !== 2 && menu.moduleType === 'frontMenu' && menu.parentId !== '-1'"
                label="组件名称"
                prop="componentName"
                :rules="[{notNull: true, message: '组件名称不能为空'}]">
                <my-input :readonly="!editAble" v-model="menu.componentName" placeholder="组件名称"></my-input>
              </my-form-item>
              <my-form-item
                label="菜单名称"
                prop="name"
                :rules="[{ notNull: true, message: '菜单名不能为空' }]">
                <my-input :readonly="!editAble" v-model="menu.name" placeholder="菜单名称或按钮名称"></my-input>
              </my-form-item>
              <my-form-item v-show="menu.parentId!=='-1'" label="上级菜单" prop="parentName">
                <my-input readonly="readonly" @click.native="editAble?menuTree():''" v-model="menu.parentName" placeholder="上级菜单"></my-input>
              </my-form-item>
              <my-form-item v-if="menu.type === 1" label="菜单URL" prop="url"
                :rules="this.menu.type === 1 ? [{ notNull: true, message: 'url不能为空'}] : []">
                <my-input :readonly="!editAble" v-model="menu.url" placeholder="菜单URL"></my-input>
              </my-form-item>
              <el-form-item v-if="menu.type === 1" label="待办任务URL" prop="todoUrl">
                <el-input :readonly="!editAble" v-model="menu.todoUrl" placeholder="待办任务接口URL"></el-input>
              </el-form-item>
              <el-form-item v-if="menu.type === 1 || menu.type === 2" label="授权标识" prop="perms">
                <el-input :readonly="!editAble" v-model="menu.perms" placeholder="多个用逗号分隔，如：user:list,user:create"></el-input>
              </el-form-item>
              <el-form-item v-if="menu.parentId!=='-1'" label="角色列表" prop="roles">
                <el-input
                  readonly="readonly"
                  type="textarea"
                  row="3"
                  :value="menu.roles | roleListStr"
                  @click.native="editAble ? selectRoles():''"
                  placeholder="角色列表"
                  style="word-break:keep-all"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="menu.type === 1 && menu.moduleType === 'frontMenu'" label="路由参数" prop="query">
                <el-input :readonly="!editAble" v-model="menu.query" placeholder="路由参数"></el-input>
              </el-form-item>
              <el-form-item v-if="menu.type === 1 && menu.moduleType === 'frontMenu'" label="是否缓存" prop="cacheable">
                <el-radio-group v-model="menu.cacheable">
                  <el-radio :disabled="!editAble" :label="true">缓存</el-radio>
                  <el-radio :disabled="!editAble" :label="false">不缓存</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="menu.type === 1 && menu.moduleType === 'frontMenu'" label="是否外链" prop="outUrlOrNot">
                <el-radio-group v-model="menu.outUrlOrNot">
                  <el-radio :disabled="!editAble" :label="true">是</el-radio>
                  <el-radio :disabled="!editAble" :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="排序号" prop="orderNum">
                <el-input-number :disabled="!editAble" v-model="menu.orderNum" :min="0"></el-input-number>
              </el-form-item>
              <el-form-item v-if="menu.type !== 2 && menu.moduleType!=='sysRes'" label="图标" prop="icon">
                <el-popover
                  :disabled="!editAble"
                  placement="bottom-start"
                  width="460"
                  trigger="click"
                  @show="$refs['iconSelect'].reset()">
                  <IconSelect ref="iconSelect" @selected="selected"/>
                  <el-input slot="reference" v-model="menu.icon" placeholder="点击选择图标" readonly>
                    <svg-icon
                      v-if="menu.icon"
                      slot="prefix"
                      :icon-class="menu.icon"
                      class="el-input__icon"
                      style="height: 32px;width: 16px;"/>
                    <i v-else slot="prefix" class="el-icon-search el-input__icon"/>
                  </el-input>
                </el-popover>
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  :readonly="!editAble"
                  type="textarea"
                  row="3"
                  v-model="menu.remark"
                  placeholder="备注"
                  style="word-break:keep-all"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="!editAble">
                <el-col :span="1.5">
                  <el-button
                    v-if="menu.type !== 2"
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="add"
                    v-hasPermi="['sys:menu:save']"
                  >新增</el-button>
                </el-col>
                <el-col :span="1.5" v-if="menu.parentId!=='-1'">
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    @click="update"
                    v-hasPermi="['sys:menu:update']"
                  >修改</el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="delOne"
                    v-hasPermi="['sys:menu:delete']"
                  >删除</el-button>
                </el-col>
              </el-form-item>
              <el-form-item v-else>
                <el-button
                  type="primary"
                  icon="el-icon-success"
                  size="mini"
                  @click="saveOrUpdate"
                >保存</el-button>
                <el-button
                  type="default"
                  icon="el-icon-refresh-left"
                  size="mini"
                  @click="reload"
                >返回</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 选择菜单 -->
    <my-dialog title="选择上级菜单" v-el-drag-dialog :visible.sync="openSelectMenu" width="300px" append-to-body>
      <el-tree ref="selectMenuTree" :data="menuList2" :props="treeProps" node-key="menuId"></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMenuSelect">确 定</el-button>
        <el-button @click="cancelMenuSelect">取 消</el-button>
      </div>
    </my-dialog>
    <!-- 选择角色 -->
    <my-dialog title="选择角色" v-el-drag-dialog :visible.sync="openSelectRole" width="300px" append-to-body>
      <el-form ref="form2" label-width="80px">
        <el-form-item label="关键字">
          <el-input placeholder="关键字" v-model="queryRoleName"></el-input>
        </el-form-item>
        <el-checkbox-group v-model="roleIdList">
          <el-checkbox
            v-for="(item,index) in dynamicRoleList"
            :label="item.roleId"
            :key="item.roleId">{{ item.roleName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRoleSelect">确 定</el-button>
        <el-button @click="cancelRoleSelect">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { getMenuTreeData, addMenu, updateMenu, allRoleList, delMenu, updateRoles } from '@/api/system/menu'
import { handleTree } from '@/utils/ruoyi'
import elDragDialog from '@/directive/dialog/drag'
import IconSelect from '@/components/IconSelect'
import MyConfig from '@/components/YB/MyConfig'
export default {
  name: 'Menu',
  components: {
    IconSelect,
    MyConfig
  },
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
    return {
      showInfo: true,
      editAble: false,
      openSelectMenu: false,
      openSelectRole: false,
      title: '菜单信息',
      menu: {
        menuId: '0',
        name: '系统菜单',
        orderNum: 0,
        parentId: '-1',
        type: 0,
        componentName: '',
        cacheable: true,
        outUrlOrNot: false,
        query: '',
      },
      selectMenu: {},
      queryRoleName: '',
      roleList: [],
      roleIdList: [],
      resetRoleIdList: [],
      roleMenuObj: {},
      menuList: [],
      menuList2: [],
      expandedKeys: ['0'],
      treeProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  methods: {
    // 选择图标
    selected(name) {
      this.menu.icon = name
    },
    //左侧数点击
    handleLefTreeClick(data, treeNode, nodeObj) {
      let vm = this
      vm.$refs['form'].clearValidate()
      vm.showInfo = true
      vm.title = '菜单信息'
      vm.menu = JSON.parse(JSON.stringify(data))
      var parentNode = data.parentNode
      if (parentNode != null && typeof (parentNode) != 'undefined') {
        vm.menu.parentName = parentNode.name
      }
      vm.editAble = false
    },
    //加载菜单导航
    getMenuTreeLeft: function() {
      getMenuTreeData().then(r => {
        this.menuList = handleTree(r.menuList, 'menuId', 'parentId')
        this.menuList2 = JSON.parse(JSON.stringify(this.menuList))
        var that = this
        this.$nextTick(() => {
          if (that.menu.menuId) {
            that.$refs.leftTree.setCurrentKey(that.menu.menuId)
            let node = that.$refs.leftTree.getNode(that.menu.menuId)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
            //展开树节点
            that.expandedKeys = [that.menu.menuId]
          }
        })
      })
    },
    add: function() {
      this.showInfo = false
      this.editAble = true
      this.title = '菜单信息-新增'
      this.selectMenu = this.menu
      this.menu = {
        parentName: this.selectMenu.name,
        parentId: this.selectMenu.menuId,
        moduleType: this.selectMenu.moduleType,
        type: this.selectMenu.moduleType !== 'sysRes' ? 1 : 0,
        perms: '',
        orderNum: 100,
        cacheable: true,
        outUrlOrNot: false,
        query: '',
        url: '',
        name: '',
        componentName: '',
        icon: 'table'
      };
    },
    update: function(menuId) {
      let vm = this
      vm.showInfo = false
      vm.editAble = true
      vm.title = '菜单信息-修改'
      vm.selectMenu = vm.menu
    },
    saveOrUpdate: function() {
      let that = this
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (!that.menu.menuId) {
            addMenu(that.menu).then(r => {
              that.selectMenu = r.menu
              that.$modal.msgSuccess('操作成功')
              that.reload()
            })
          } else {
            updateMenu(that.menu).then(r => {
              that.selectMenu = r.menu
              that.$modal.msgSuccess('操作成功')
              that.reload()
            })
          }
        }
      })
    },
    delOne: function() {
      this.$modal.confirm('确定要删除选中的记录及其所有子菜单吗？')
        .then(() => {
          this.selectMenu = JSON.parse(JSON.stringify(this.menu.parentNode));
          delMenu(this.menu.menuId).then(r => {
            this.$modal.msgSuccess('操作成功')
            this.reload()
          })
        }).catch(()=>{})
    },
    menuTree: function() {
      this.openSelectMenu = true
      this.$nextTick(function() {
        this.$refs.selectMenuTree.setCurrentKey(this.menu.parentId)
      })
    },
    submitMenuSelect() {
      let currentNode = this.$refs.selectMenuTree.getCurrentNode()
      if (currentNode.menuId !== this.menu.menuId) {
        this.menu.parentId = currentNode.menuId
        this.menu.parentName = currentNode.name
        this.menu.moduleType = currentNode.moduleType
        this.openSelectMenu = false
      } else {
        this.$modal.alertError('不能选择自己为上级')
      }
    },
    cancelMenuSelect() {
      this.openSelectMenu = false
    },
    reload: function() {
      let vm = this
      vm.showInfo = true
      vm.menu = vm.selectMenu
      vm.getMenuTreeLeft()
      // $(".error-label").remove();
      vm.editAble = false
      vm.title = '菜单信息'
      vm.queryRoleName = ''
    },
    //获取角色列表 处理roleIdList
    getRoleList: function() {
      var that = this
      allRoleList().then(r => {
        that.roleList = r.list
      })
    },
    //选择角色弹窗以及事件处理
    selectRoles: function() {
      if (!this.menu.menuId) {
        return
      }
      var that = this
      if (that.menu.roles) {
        that.roleIdList = that.menu.roles.map(function(item) {
          return item.roleId
        })
      }
      that.queryRoleName = ''
      that.openSelectRole = true
    },
    submitRoleSelect() {
      this.$modal.loading('加载中')
      updateRoles({
        roleIdList: this.roleIdList,
        menuId: this.menu.menuId
      }).then(() => {
        this.openSelectRole = false
        this.$modal.msgSuccess('操作成功')
        this.reload()
        this.$modal.closeLoading()
      }).catch(this.$modal.closeLoading)
    },
    cancelRoleSelect() {
      this.openSelectRole = false
    }
  },
  computed: {
    //动态过滤角色
    dynamicRoleList: function() {
      if (!this.roleList) {
        return []
      }
      var that = this
      return this.roleList.filter(function(e) {
        return e.roleName.indexOf(that.queryRoleName) > -1
      })
    },
  },
  filters: {
    roleListStr: function(v) {
      if (v && v.length) {
        var l = []
        for (var i = 0; i < v.length; i++) {
          l.push(v[i].roleName)
        }
        return l.join(', ')
      }
    }
  },
  mounted: function() {
    this.getRoleList()
    this.getMenuTreeLeft()
  }
}
</script>
