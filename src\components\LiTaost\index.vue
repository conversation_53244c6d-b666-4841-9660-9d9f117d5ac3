<template>
  <div v-if="showPrompt" class="prompt-modal">
    <div class="prompt-content">
      <div class="prompt-header">
        <h3>温馨提示</h3>
        <span class="close-btn" @click="closePrompt">×</span>
      </div>
      <div class="prompt-body">
        <p>您的非法采砂月报尚未填报，请及时填报！</p>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    getTbaleData,
  } from "@/api/tablebbtb/bbtb";
export default {
  data() {
    return {
      showPrompt: false,
      formInline:{
        pageNum: 1,
        pageSize: 10,
        areaName: "", //区域名称
        areaCode: "", //区域编码
        startTime: null, //开始时间
        endTime: null, //结束时间
        status: null
      }
    };
  },
  mounted() {
    // this.checkPromptDate();
  },
   watch: {
      '$route'() {
         this.checkPromptDate();
      }
    },
  methods: {
    currentYearMonth() {
          const date = new Date()
          return [
            date.getFullYear(),
            (date.getMonth() + 1).toString().padStart(2, '0')
          ].join('-')
        },
    // 判断当前是否月底前一周
    isLastWeekOfMonth() {
      // const today = new Date();
      // const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      // const daysLeft = lastDay.getDate() - today.getDate();
      // return daysLeft <= 7;
      const today = new Date();
      console.log(today.getDate(),'日期？')
        return today.getDate() >= 20;
    },

    // 检查是否需要显示弹窗
    checkPromptDate() {
      console.log('走到了这里，是双数')
      // 从本地存储获取关闭记录
      if(this.$store.state.user.token&&this.$store.state.user.rolenames.includes('水行政主管')&&this.$store.state.user.deptcode.length!=4){

      const closedDate = localStorage.getItem('promptClosedDate');
      const currentMonth = new Date().getMonth();
      // console.log(this.currentYearMonth());
      this.formInline.startTime=this.currentYearMonth();
      this.formInline.endTime=this.currentYearMonth();
      getTbaleData(this.formInline).then((res) => {
          let tableData = res.records;
          console.log(tableData,'这个才是我们的心数据')
          if(tableData[0].fillStatus=='未填报'){
            if (this.isLastWeekOfMonth() && closedDate !== String(currentMonth)) {
              this.showPrompt = true;
            }
          }
        });
      }else{
        this.showPrompt = false;
      }
    },

    // 关闭弹窗
    closePrompt() {
      this.showPrompt = false;
      // 存储关闭月份到本地存储
      localStorage.setItem('promptClosedDate', new Date().getMonth());
    }
  }
};
</script>

<style scoped>
.prompt-modal {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 999;
}

.prompt-content {
  width: 300px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #ebeef5;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.prompt-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-btn {
  font-size: 20px;
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #409eff;
}

.prompt-body {
  padding: 15px;
  font-size: 14px;
  color: #606266;
}

.prompt-body p {
  margin: 0;
  line-height: 1.5;
}
</style>
