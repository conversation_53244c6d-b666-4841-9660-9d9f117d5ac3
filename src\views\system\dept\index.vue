<template>
  <div v-cloak>
    <el-row :gutter="10" style="margin: 10px">
      <el-col :sm="6">
        <el-card>
          <div slot="header">机构导航</div>
          <div style="overflow: auto;height: 85%">
            <el-tree
              ref="leftTree"
              :data="deptList"
              :props="deptTreeProps"
              node-key="deptId"
              :default-expanded-keys="deptExpandedKeys"
              @node-click="handleLefTreeClick"
            ></el-tree>
          </div>
        </el-card>
      </el-col>

      <el-col :sm="18">
        <el-card>
          <div slot="header">{{ title }}</div>
          <div style="overflow: auto;height: 85%;width: 550px;">
            <el-form ref="form" :model="dept" label-width="100px">
              <my-form-item
                v-show="dept.parentId!=='-1'"
                label="上级机构"
                prop="componentName"
              >
                <el-input
                  :disabled="!addAble"
                  @click.native="addAble ? deptTree():''"
                  v-model="dept.parentName"
                  placeholder="一级机构"
                ></el-input>
              </my-form-item>
              <my-form-item
                label="机构名称"
                ref="name"
                prop="name"
                :rules="[{notNull:true, message:'请输入机构名称', trigger:['blur','change']}]"
              >
                <el-input
                  maxlength="50"
                  :disabled="!editAble"
                  v-model="dept.name"
                  placeholder="机构名称"
                ></el-input>
              </my-form-item>
              <my-form-item
                label="机构类型"
                ref="type"
                prop="type"
                :rules="[{notNull:true, message:'请选择机构类型', trigger:['blur','change']}]"
              >
                <el-radio-group :disabled="!editAble" v-model="dept.type">
                  <el-radio
                    v-for="item in typeList"
                    :key="item.value"
                    :label="item.value"
                  >{{ item.name }}
                  </el-radio>
                </el-radio-group>
              </my-form-item>
              <my-form-item
                v-if="deptShowArea==='true'"
                label="所属区域"
                prop="areaName"
              >
                <el-input
                  :disabled="!editAble"
                  @click.native="editAble ? areaTree():''"
                  v-model="dept.areaName"
                  placeholder="行政区域"
                ></el-input>
              </my-form-item>
              <my-form-item
                v-show="dept.parentId!=='-1'"
                label="地区代码"
                ref="simplifiedCode"
                prop="simplifiedCode"
              >
                <el-input
                  maxlength="32"
                  :disabled="!editAble"
                  v-model="dept.simplifiedCode"
                  placeholder="地区代码"
                ></el-input>
              </my-form-item>
              <my-form-item
                label="监督举报电话"
                ref="complaintMobile"
                prop="complaintMobile"
                :rules="[{isMobileOrTel:true, message:'监督举报电话格式不正确', trigger:['blur','change']}]"
              >
                <el-input
                  :disabled="!editAble"
                  v-model="dept.complaintMobile"
                  placeholder="监督举报电话"
                ></el-input>
              </my-form-item>
              <my-form-item
                label="查询电话"
                ref="queryMobile"
                prop="queryMobile"
                :rules="[{isMobileOrTel:true, message:'查询电话格式不正确', trigger:['blur','change']}]"
              >
                <el-input
                  :disabled="!editAble"
                  v-model="dept.queryMobile"
                  placeholder="查询电话"
                ></el-input>
              </my-form-item>
              <my-form-item label="排序号" prop="orderNum">
                <el-input-number
                  :disabled="!editAble"
                  v-model="dept.orderNum"
                  :min="0"
                  placeholder="排序号"
                ></el-input-number>
              </my-form-item>
              <my-form-item class="specialItem" label="印章图片" prop="imgList">
                <my-pv-upload
                  :label="'印章图片'"
                  :disabled="!editAble"
                  :multiple="false"
                  :uploadMode="'image'"
                  :fileType="fileType"
                  :file-list="imgList"
                  :pathFieldName="pathFieldName"
                  @getFileList="getFileList"
                />
              </my-form-item>
              <el-form-item v-if="!editAble">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="add"
                    v-hasPermi="['sys:dept:save']"
                  >新增
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    @click="update"
                    v-hasPermi="['sys:dept:update']"
                  >修改
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="delOne"
                    v-hasPermi="['sys:dept:delete']"
                  >删除
                  </el-button>
                </el-col>
              </el-form-item>
              <el-form-item v-else>
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-success"
                    size="mini"
                    @click="saveOrUpdate"
                  >保存
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="default"
                    icon="el-icon-refresh-left"
                    size="mini"
                    @click="reload"
                  >返回
                  </el-button>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 选择机构 -->
    <my-dialog title="选择机构" v-el-drag-dialog :visible.sync="openSelectDept" width="300px" append-to-body>
      <el-tree
        ref="selectDeptTree"
        :data="deptList2"
        :props="deptTreeProps"
        node-key="deptId"
        :default-expanded-keys="deptExpandedKeys"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDeptSelect">确 定</el-button>
        <el-button @click="cancelDeptSelect">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 选择区域 -->
    <my-dialog
      title="选择区域"
      v-el-drag-dialog
      height="70vh"
      :visible.sync="openSelectArea"
      width="300px"
      append-to-body
    >
      <el-tree
        ref="selectAreaTree"
        :data="areaList"
        :props="areaTreeProps"
        node-key="areaCode"
        :default-expanded-keys="areaExpandedKeys"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaSelect">确 定</el-button>
        <el-button @click="cancelAreaSelect">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { getAreaTreeData, getDeptTreeData, submitDeptData, delDeptData } from '@/api/system/dept';
import { handleTree } from '@/utils/ruoyi';
import elDragDialog from '@/directive/dialog/drag';
import configUtils from '@/utils/configUtils';
import dictUtils from '@/utils/dictUtils';
import MyPvUpload from "@/components/YB/MyPvUpload";
import {fileListByOwner} from "@/api/system/common";

export default {
  name: 'Dept',
  directives: {
    elDragDialog  //拖拽弹窗
  },
  components: {
    MyPvUpload,
  },
  data() {
    return {
      addAble: false,
      editAble: false,
      openSelectDept: false,
      openSelectArea: false,
      deptExpandedKeys: [],  //展开机构树
      areaExpandedKeys: [],  //展开区域树
      title: '机构信息',
      dept: {
        deptId: '000_013',  //默认deptId
        areaCode: '',
        areaName: '',
        parentName: '',
        parentId: '-1',
        deptCode: '',
        parentCode: '',
        complaintMobile: '',
        queryMobile: '',
        imgList: [],
        simplifiedCode:'',
      },
      imgList: [],
      pathFieldName: 'dept-image', //业务表字段
      fileType: '.bmp,.gif,.jpg,.jpeg,.png',  //印章图片类型
      selectDept: {},
      typeList: [],  //机构类型字典
      deptShowArea: '',  //是否显示所属区域
      editDeptType: '',  // 编辑中的机构类型
      area: {},
      deptList: [],
      deptList2: [],
      areaList: [],
      originAreaList: [],
      deptTreeProps: {
        children: 'children',
        label: 'name'
      },
      areaTreeProps: {
        children: 'children',
        label: 'areaName'
      },
      rules: {
        name: [
          {required: true, message:'机构名称不能为空', trigger:['blur','change']}
        ],
        type: [
          {required: true, message: '机构名称不能为空', trigger:['blur','change']}
        ],
        complaintMobile: [
          {required: true, message: '监督举报电话不能为空', trigger:['blur','change']}
        ],
        queryMobile: [
          {required: true, message: '查询电话不能为空', trigger:['blur','change']}
        ]
      }
    }
  },
  methods: {
    //机构导航-机构树点击事件
    handleLefTreeClick(data, treeNode, nodeObj) {
      this.editAble = false
      this.title = '机构信息'
      this.$refs['form'].clearValidate()
      this.dept = JSON.parse(JSON.stringify(data))
      //把印章图片做缓存处理
      if (!data.imgList) {
        //根据ownerId和type获取上传的印章图片
        fileListByOwner(this.dept.deptId, this.pathFieldName).then(res=> {
          if (res.fileList && res.fileList.length > 0) {
            this.imgList = res.fileList.map(item => {
              return {
                name: item.uploadFileName,
                url: item.uploadFilePath,
                uploadFile: item,
              }
            });
          } else {
            this.imgList = [];
          }
          data.imgList = this.imgList;
          this.deptByImgList(this.imgList);
        });
      } else {
        this.imgList = data.imgList;
        this.deptByImgList(data.imgList);
      }
      this.editDeptType = data.type
      var parentNode = data.parentNode
      if (parentNode != null && typeof (parentNode) != 'undefined') {
        this.dept.parentName = parentNode.name
      }
      //打开页面默认展开机构树对应的节点
      this.deptExpandedKeys = [this.dept.deptId]
      //根据areaCode匹配区域名称
      if (this.dept.areaCode) {
        let that = this
        let areaList = this.originAreaList.filter(area => {
          return area.areaCode === that.dept.areaCode
        })
        if (areaList && areaList.length > 0) {
          this.dept.areaName = areaList[0].areaName
        }
      }
    },
    //给dept对象赋值图片属性
    deptByImgList(data) {
      this.dept.imgList = data.map(item => {
        return item.uploadFile;
      });
    },
    //加载机构树
    getDeptTreeLeft() {
      getDeptTreeData().then(r => {
        this.deptList = handleTree(r.deptList, 'deptId', 'parentId')
        this.deptList2 = JSON.parse(JSON.stringify(this.deptList))
        var that = this
        this.$nextTick(() => {
          if (that.dept.deptId) {
            that.$refs.leftTree.setCurrentKey(that.dept.deptId)
            let node = that.$refs.leftTree.getNode(that.dept.deptId)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
          }
        })
      })
    },
    //新建操作
    add() {
      this.addAble = true;
      this.editAble = true;
      this.title = '机构信息-新增';
      this.selectDept = JSON.parse(JSON.stringify(this.dept));
      this.dept = {
        parentName: this.selectDept.name,
        parentId: this.selectDept.deptId,
        parentCode: this.selectDept.deptCode,
        parentType: this.selectDept.type,
        areaCode: this.selectDept.areaCode,
        areaName: this.selectDept.areaName,
        type: 'dept',
        orderNum: 100
      }
      this.imgList = [];
    },
    //修改操作
    update() {
      this.editAble = true
      this.title = '机构信息-修改'
      this.selectDept = JSON.parse(JSON.stringify(this.dept))
    },
    //删除操作
    delOne() {
      let node = this.$refs.leftTree.getNode(this.dept.deptId)
      if (node.data.parentId === '-1') {
        return this.$modal.msgWarning('不允许删除最上级机构')
      }
      if (node.data) {
        this.selectDept = this.$refs.leftTree.getNode(this.dept.parentId).data
        this.$modal.confirm('删除操作将会同时删除下级机构并把本机构下的用户移到上级机构，确定删除吗?').then(() => {
          this.$modal.loading('加载中')
          delDeptData(this.dept.deptId).then(() => {
            this.$modal.msgSuccess('操作成功')
            this.reload()
            this.$modal.closeLoading()
          }).catch(this.$modal.closeLoading)
        }).catch(()=>{})  //捕获异常错误
      }
    },
    //保存操作
    saveOrUpdate() {
      let that = this
      that.$refs['form'].validate(valid => {
        if (valid) {
          if (that.editDeptType === 'company' && that.dept.type === 'dept' && that.dept.deptId) {
            that.$modal.confirm('机构类型由公司修改为部门，其下级所有公司都将被修改为部门，是否继续？').then(() => {
              that.submit()
            }).catch(()=>{})  //捕获异常错误
          } else {
            that.submit()
          }
        }
      })
    },
    //获取上传组件返回的值
    getFileList(fileList) {
      this.dept.imgList = fileList;
    },
    //提交后台
    submit() {
      let that = this
      that.$refs['form'].validate(valid => {
        if (valid) {
          that.$modal.loading('加载中')
          submitDeptData(that.dept).then(r => {
            that.selectDept = r.dept
            that.$modal.msgSuccess('操作成功')
            that.reload()
            that.$modal.closeLoading()
          }).catch(that.$modal.closeLoading)
        }
      })
    },
    //返回操作
    reload() {
      this.dept = JSON.parse(JSON.stringify(this.selectDept))
      this.getArea()
      this.$refs['form'].clearValidate()
      this.addAble = false
      this.editAble = false
      this.title = '机构信息'
    },
    //上级机构弹窗
    deptTree() {
      this.openSelectDept = true
      this.$nextTick(function() {
        this.$refs.selectDeptTree.setCurrentKey(this.dept.parentId)
      })
    },
    //上级机构弹窗-确定操作
    submitDeptSelect() {
      let currentNode = this.$refs.selectDeptTree.getCurrentNode()
      if (currentNode.deptId !== this.dept.deptId) {
        this.dept.parentId = currentNode.deptId
        this.dept.parentName = currentNode.name
        this.dept.parentCode = currentNode.deptCode
        this.dept.parentType = currentNode.type
        if (currentNode.type === 'dept') {
          this.dept.type = 'dept'
        }
        this.openSelectDept = false
      } else {
        this.$modal.msgWarning('不能选择自己为上级')
      }
    },
    //上级机构弹窗-取消操作
    cancelDeptSelect() {
      this.openSelectDept = false
    },
    //获取区域树
    getArea() {
      getAreaTreeData().then(r => {
        this.originAreaList = JSON.parse(JSON.stringify(r.areaList))
        this.areaList = handleTree(r.areaList, 'areaCode', 'parentCode')
        this.getDeptTreeLeft()
      })
    },
    //所属区域弹窗
    areaTree() {
      this.openSelectArea = true
      this.$nextTick(function() {
        this.$refs.selectAreaTree.setCurrentKey(this.dept.areaCode)
        if (this.dept.areaCode) {
          let areaNode = this.$refs.selectAreaTree.getNode(this.dept.areaCode).data
          if (areaNode) {
            this.area = areaNode
            this.dept.areaName = areaNode.areaName
          }
          //打开区域树弹窗展开对应的节点
          this.areaExpandedKeys = [this.dept.areaCode]
        }
      })
    },
    //所属区域弹窗-确定操作
    submitAreaSelect() {
      let currentNode = this.$refs.selectAreaTree.getCurrentNode()
      if (currentNode !== null) {
        this.dept.areaCode = currentNode.areaCode
        this.dept.areaName = currentNode.areaName
        this.area = currentNode[0]
        this.openSelectArea = false
      } else {
        this.$modal.msgWarning('请选择区域')
      }
    },
    //所属区域弹窗-取消操作
    cancelAreaSelect() {
      this.openSelectArea = false
    }
  },
  mounted() {
    this.getArea()
    //调用系统配置类
    configUtils.getConfigValue('system.deptShowArea').then(r => {
      this.deptShowArea = r
    })
    //调用字典工具类
    dictUtils.cList('orgType').then(r => {
      this.typeList = r.orgType
    })
  }
}
</script>

<style scoped>
/deep/ .my-dialog__body{
  height: 60vh;
  overflow: auto;
}
</style>

