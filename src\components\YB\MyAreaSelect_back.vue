<template>
  <div class="selecter">
    <!-- 地市级选择 -->
    <el-select :clearable="isClear" filterable class="parent-select" v-model="parentValue"
      :placeholder="componentType ? '请选择地市级' : '地市级'" @change="parentChange" @clear="parentClear"
      :disabled="disabled || readOnlyParent">
      <el-option v-for="item in subList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode" />
    </el-select>
    <!-- 区县级选择 -->
    <el-select clearable filterable v-model="childrenValue"
      :placeholder="!disabled ? componentType ? '请选择区县级' : '区县级' : ''" @change="childrenChange" @clear="childrenClear"
      :disabled="disabled || readOnlyChildren">
      <el-option v-for="item in subChildList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode" />
    </el-select>
  </div>
</template>

<script>
import { getSubList } from "@/api/system/area";
export default {
  name: "MyAreaSelect",
  emits: ["input"],
  props: {
    value: {
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    componentType: {
      type: Boolean,
      default: false
    },
    externalParentValue: String,
    externalChildrenValue: String,
    readOnlyParent: {
      type: Boolean,
      default: false
    },
    readOnlyChildren: {
      type: Boolean,
      default: false
    },
    isClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      subList: [],
      subChildList: [],
      parentValue: '',
      childrenValue: '',
      parentName: '',
      childrenName: '',
    }
  },
  methods: {
    parentChange() {
      this.childrenValue = "";
      this.$emit("input", this.parentValue);
      const areaItem = this.subList.find(item => item.areaCode === this.parentValue);
      this.$emit("change", areaItem ? areaItem.areaName : "");
    },
    parentClear() {
      this.childrenValue = "";
      this.subChildList = [];
      this.$emit("input", "");
      this.$emit("change", "")
    },
    childrenChange() {
      this.$emit("input", this.childrenValue);
      const areaItem1 = this.subList.find(item => item.areaCode === this.parentValue);
      const areaItem2 = this.subChildList.find(item => item.areaCode === this.childrenValue);
      this.$emit("change", (areaItem1 ? areaItem1.areaName : "") + (areaItem2 ? areaItem2.areaName : ""));
    },
    childrenClear() {
      this.childrenValue = "";
      this.$emit("input", this.parentValue);
      const areaItem = this.subList.find(item => item.areaCode === this.parentValue);
      this.$emit("change", areaItem ? areaItem.areaName : "");
    },
  },
  watch: {
    value(nval, oval) {
      if (nval) {
        if (this.parentValue && !this.childrenValue) {
          getSubList(nval).then(res => {
            this.subChildList = res.areaList;
          });
        } else if (!this.parentValue && !this.childrenValue) {
          if (this.value) {
            this.parentValue = this.value.slice(0, 11);
            getSubList(this.parentValue).then(res => {
              this.subChildList = res.areaList;
            });
            this.childrenValue = this.value;
          }
        }
      } else {
        if (this.parentValue) {
          this.childrenValue = "";
          this.parentValue = "";
          this.subChildList = []
        }
      }
    },
    externalParentValue(newVal) {
      if (newVal !== this.parentValue) {
        this.parentValue = newVal;
        getSubList(this.parentValue).then(res => {
          this.subChildList = res.areaList;
        });
      }
    },
    externalChildrenValue(newVal) {
      if (newVal !== this.childrenValue) {
        this.childrenValue = newVal;
      }
    }
  },
  // created() {
  //   console.log("select_created------:" + this.externalParentValue);
  //   this.parentValue = this.externalParentValue || this.parentValue;
  //   this.childrenValue = this.externalChildrenValue || this.childrenValue;
  // },
  mounted() {
    //默认获取市区域列表
    getSubList('000_013').then(res => {
      this.subList = res.areaList;
    });
    //根据默认城市区域代码获取区县列表
    // getSubList(this.parentValue).then(res => {
    //   this.subChildList = res.areaList;
    //   console.log("mounted_subChildList:" + this.subChildList);
    // });
  }
}
</script>

<style scoped>
.selecter {
  /* width: 100%; */
  display: flex;
}

.selecter .el-select {
  max-width: 50%;
  min-width: calc(50% - 2.5px);
}

@media (max-width: 768px) {
  .selecter .el-select {
    max-width: 33%;
    min-width: calc(33% - 2.5px);
  }
}

.parent-select {
  margin-right: 5px;
  width: 120px;
}
</style>
