<template>
  <div class="icon-box" style="margin:150px 30px auto 30px;">
    <el-alert
      v-if="inWeiXin"
      type="warning"
      title="请注意"
      description="点击右上角选择在浏览器打开~"
      :closable="false"
      show-icon
    ></el-alert>
    <el-alert
      v-if="!inWeiXin"
      type="success"
      title="操作成功"
      description="请等待浏览器完成下载。"
      :closable="false"
      show-icon
    ></el-alert>
  </div>
</template>

<script>
import {setToken} from "@/utils/auth";

export default {
  name: "downloadApp",
  data() {
    return {
      inWeiXin: false,
    }
  },
  methods: {
    isWeixin() {
      var ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        console.log('微信浏览器');
        return true;
      } else {
        console.log("不是微信浏览器");
        return false;
      }
    },
  },
  mounted() {
    this.inWeiXin = this.isWeixin();
    if (!this.inWeiXin) {
      //调用app下载接口，只能用同步请求
      window.location = process.env.VUE_APP_BASE_API + "/api/apkDownload/download.apk";
    }
  }
}
</script>

<style scoped>
::v-deep .el-alert .el-alert__title {
  font-size: 24px;
  line-height: 40px;
}

::v-deep .el-alert .el-alert__description {
  font-size: 20px;
  line-height: 40px;
}
</style>
