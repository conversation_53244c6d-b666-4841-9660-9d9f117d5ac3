import configUtils from "@/utils/configUtils";
import {getToken} from '@/utils/auth';
import DictUtils from "@/utils/dictUtils";

let activeClose = false;

let messageHandlers = {
  //token过期，主动退出
  "timeout": function (wsMessage) {
    console.log(wsMessage);
    activeClose = true;
    webSocket.close();
  },
  //修改字典
  "dictChange": function (wsMessage) {
    console.log(wsMessage);
    DictUtils.clearCache(wsMessage.content);
  },
  //配置修改
  "configChange": function (wsMessage) {
    console.log(wsMessage);
    configUtils.clearCache()
  },
};

let webSocket;

/**
 * websocket工具类
 * @type {{registerHandler(*, *): void, newUserMessage(*): void, sendHeartbeat(): void, initWebSocket(*): void, sendMessage(*, *, *, *): void, closeWebSocket(): void}}
 */
const webSocketUtils = {
  /**
   * 初始化
   * @param port
   */
  initWebSocket(userId) {
    if ('WebSocket' in window) {
      //启动websocket
      configUtils.getConfigValue("system.webSocket.open")
        .then(open => {
          if (open==='true') {
            //配置的端口
            configUtils.getConfigValue("system.webSocket.port")
              .then(port => {
                initWebSocket(port,userId);
                //是否发送心跳
                configUtils.getConfigValue("system.webSocket.heartBeat")
                  .then(heartBeat=>{
                    if (heartBeat === 'true') {
                      setInterval(function () {
                        webSocketUtils.sendHeartbeat();
                      }, 25 * 1000);
                    }
                  })
              })
          }
        });
    }else{
      console.log("WebSocket is not support");
    }
  },
  //关闭链接
  closeWebSocket() {
    webSocket.close();
  },
  //注册回调方法
  registerHandler(type, callback) {
    messageHandlers[type] = callback;
  },
  //发送消息
  sendMessage(toUserId, type, toAll, content) {
    if (webSocket && webSocket.readyState === 1) {
      var wsMessage = {
        toUserId: toUserId,
        type: type,
        toAll: toAll,
        content: content
      };
      webSocket.send(JSON.stringify(wsMessage));
    } else {
      console.error("WebSocket is not ready,cannot send message");
    }
  },
  //发送心跳检测
  sendHeartbeat() {
    if (webSocket && webSocket.readyState === 1) {
      var wsMessage = {
        type: "heartbeat",
      };
      webSocket.send(JSON.stringify(wsMessage));
    } else {
      console.error("WebSocket is not ready,cannot send message");
    }
  },
  //当前用户与消息通道建立关联关系
  newUserMessage(fromUserId) {
    if (webSocket && webSocket.readyState === 1) {
      var wsMessage = {
        fromUserId: fromUserId,
        type: "newUser"
      };
      webSocket.send(JSON.stringify(wsMessage));
    } else {
      console.error("WebSocket is not ready,cannot send message");
    }
  },
};

/**
 * 初始化websocket
 * @param port
 */
function initWebSocket(port,userId) {
  let webSocketUrl = process.env.VUE_APP_WS_API + ":" + port + "/webSocket";

  //利用子协议传递token用于鉴权
  webSocket = new WebSocket(webSocketUrl, [getToken()]);
  webSocket.onopen = function () {
    webSocketUtils.newUserMessage(userId);
    console.log("web socket is ready");
  };
  webSocket.onerror = function (errorInfo) {
    console.error(errorInfo);
  };
  webSocket.onmessage = function (event) {
    var wsMessage = JSON.parse(event.data);
    var callback = messageHandlers[wsMessage.type];
    if (typeof (callback) == "function") {
      callback(wsMessage);
    } else {
      console.log("未注册ws消息处理方法")
      console.log(wsMessage)
    }
  };
  webSocket.onclose = function () {
    console.log("web socket is closed");
    if (!activeClose) {
      console.log("websocket abnormal shutdown,restart it");
      //不是主动关闭的，保持重连
      webSocket = null;
      initWebSocket(port, userId);
    }
  };
  //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
  window.onbeforeunload = function () {
    activeClose = true;
    webSocket.close();
  }
}




export default webSocketUtils;
