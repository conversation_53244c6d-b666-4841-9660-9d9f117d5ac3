import configUtils from "@/utils/configUtils";

/**
 * 引入腾讯地图
 */
export default {
  init: function () {
    return new Promise((resolve, reject) => {
      // 如果已加载直接返回
      if (typeof TMap !== "undefined") {
        resolve(TMap);
        return true;
      }

      // 地图异步加载回调处理
      window.onMapCallback = function () {
        resolve(TMap);
      };

      //初始化获取腾讯地图密钥
      configUtils.getConfigValue("tencent.map.key").then(res=>{
        const TMap_URL = "https://map.qq.com/api/gljs?v=1.exp&libraries=tools,service&key=" + res + "&callback=onMapCallback";
        // 插入script脚本
        let scriptNode = document.createElement("script");
        scriptNode.setAttribute("type", "text/javascript");
        scriptNode.setAttribute("src", TMap_URL);
        document.body.appendChild(scriptNode);

      });
    });
  }
}
