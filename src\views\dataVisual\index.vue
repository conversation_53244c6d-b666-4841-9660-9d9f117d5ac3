<template>
  <div :style="'height:' + height"  element-loading-text="正在加载页面，请稍候！">
    <iframe
      style="width: 100%; height: 100%"
      :src="src"
      frameborder="no"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      height: document.documentElement.clientHeight - 84 + "px",
    };
  },
  computed:{
    src() {
      //取最后一个路由
      let split = this.$route.path.split("/");
      let uri = split[split.length - 1];
      if (uri == 'bigScreen') {
        uri = '';
      }
      let src = process.env.VUE_APP_DATA_SERVER_URL + "/" + uri;
      return src
    },
  }
}
</script>

<style scoped>

</style>
