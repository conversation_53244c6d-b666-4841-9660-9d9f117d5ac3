<template>
  <el-dialog :visible.sync="visible"
    width="80%" center title="绘制坐标"
    :close-on-click-modal="false" top="6vh"
    @closed="onClose"
    @opened="onOpen" append-to-body :destroy-on-close="true"
    :show-close="false">
    <div style="margin-bottom:10px;">
      <el-button size="mini" v-show="typeAllowed%2==0" :disabled="isPlotting" @click="onClickPlot(0)">绘制点</el-button>
      <el-button size="mini" v-show="typeAllowed%3==0" :disabled="isPlotting" @click="onClickPlot(1)">绘制线</el-button>
      <el-button size="mini" v-show="typeAllowed%5==0" :disabled="isPlotting" @click="onClickPlot(2)">绘制面</el-button>
      <el-button size="mini" @click="onClickErase">擦除</el-button>
      <el-button size="mini" type="info" @click="onClickSwitchBaseMap">底图切换</el-button>
    </div>
    <div id="map2D_for_plotting" ref="mapContainer" />
    <div slot="footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :disabled="isPlotting" @click="handleConfirm">确定</el-button>
    </div>
    <el-dialog ref="dialogChange" :visible.sync="visibleDialogChange" :close-on-click-modal="false"
      width="500px" title="上图坐标发生改变" append-to-body :show-close="true">
        <span>检测到上图坐标发生了改变</span><br>
        <span>确认对修改进行保存嘛？（本操作不可恢复）</span>
      <div slot="footer">
        <el-button @click="onClickChangeCancel">取消</el-button>
        <el-button type="primary" @click="onClickChangeConfirm">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>

import L from "leaflet";
import "leaflet/dist/leaflet.css";
import K from "./toolkit_leaflet.js";
import { fromGeometry, isPolygonValid, toGeometry } from "./math_leaflet.js";

// var colorMain = '#f7c18a';
var colorMain = '#ffff7f';
var colorError = '#ff0000';

let markerMover = null;
let markerHint = null;
let markerHammer = null;
let layerLine = null;
let layerRegion = null;
let listLayerNail = [];

let layerImg = null;
let layerVec = null;

export default {

  components:{
    L, K,
  },
  data() {
    return {
      visible: false,
      visibleDialogChange: false,

      geojson: undefined,
      msgFromCaller: "",
      typeAllowed: 30,

			isPlotting: false,
			typePlotting: 0,  // 0-point, 1-line, 2-region

			msgInvalid: "当前多边形不合法，请确认是否存在交叉线",
			isSufficient: false,

      isLayerImgShowing: true,
      envWordCurrent: process.env.NODE_ENV,

      preloadReady: false,
    }
  },
  methods: {
		onOpen() {
      if (K.viewer) {
        K.viewer.remove()
      }
      this.init()
      this.listen()
			this.preloadReady = false;
			this.erase();
      if (this.msgFromCaller != "") {
        this.$modal.msgSuccess(this.msgFromCaller);
      }
      if (this.geojson) {
        let obj = fromGeometry(this.geojson);
        this.typePlotting = obj.type;
        let N = obj.points.length;
        for (let i = 0; i < N; i++) {
          this.addNail(obj.points[i]);
        }
        this.finish();
        if (N == 1) {
          K.viewer.setView(obj.points[0], 15);
        } else {
          let bounds = L.latLngBounds(obj.points);
          K.setViewToBounds(bounds.pad(0.2));
        }
      }
      this.preloadReady = true;
      // this.check();
		},
    onClose(){

    },
    init() {
			K.initMap("map2D_for_plotting");
			K.initWatermark();
      layerImg = K.createLayerWmtsTdt('img', 1)
      layerVec = K.createLayerWmtsTdt('vec', 1)
      let layerNote = K.createLayerWmtsTdt('cva', 2)
      // if (this.envWordCurrent == "development") {
      // 	// 天地图临时转发
      // 	layerImg = K.createLayerWmtsTdt('img', 1)
      // 	layerVec = K.createLayerWmtsTdt("vec", 1)
      //   layerNote = K.createLayerWmtsTdt('cva', 2);
      // }
      // K.switchLayer(layerVec, true)
      K.switchLayer(layerImg, true)
      K.switchLayer(layerNote, true)

			markerMover = L.marker([0, 0], {icon: L.icon({iconUrl: require("@/assets/plotter/nail.png"),iconAnchor: [4, 4],}), interactive: false,});
			K.switchLayer(markerMover, true);
			markerHint = L.marker([0, 0], {icon: L.icon({iconUrl: require("@/assets/plotter/hint.png"),iconAnchor: [-12, 18],}),interactive: false,});
			K.switchLayer(markerHint, true);
			markerHammer = L.marker([0, 0], {icon: L.icon({iconUrl: require("@/assets/plotter/hammer.png"),iconAnchor: [8, 8],}),interactive: false,});
			K.switchLayer(markerHammer, true);
			layerLine = L.polyline([], {color: colorMain,weight: 1,interactive: false,});
			K.switchLayer(layerLine, true);
			layerRegion = L.polygon([], {color: colorMain,fill: true,fillColor: colorMain,weight: 1,interactive: false,});
			K.switchLayer(layerRegion, true);
    },
    listen(){
      let that = this;
      K.viewer.on("move", function(e) {
      });
      K.viewer.on("mousemove", function(e) {
        if (!that.isPlotting) {
          return;
        }
        markerMover.setLatLng(e.latlng);
				if (that.typePlotting > 0) {
					markerHint.setLatLng(e.latlng);
				}
        that.refresh();
      });
      K.viewer.on("click", function(e) {
        if (!that.isPlotting) {
          return;
        }
        // console.log(4444, e.latlng);
        that.addNail(e.latlng);
        if (that.typePlotting == 0) {
          that.finish();
        }
      });
      K.viewer.on("contextmenu", function(e){
      	if (that.isPlotting) {
      		that.finish();
      	}
      });
    },
    refresh(){
      let lls = this.getNailsCurrent();
      if (this.typePlotting == 1) {
        layerLine.setLatLngs(lls);
      }
      if (this.typePlotting == 2) {
        layerRegion.setLatLngs(lls);
				if (isPolygonValid(lls)){
					layerRegion.setStyle({
						color: colorMain,
						fillColor: colorMain,
					});
				} else {
					layerRegion.setStyle({
						color: colorError,
						fillColor: colorError,
					});
				}
      }
    },
		check(){
			let lls = this.getNailsCurrent();
			if (this.typePlotting == 2 && !isPolygonValid(lls)){
				this.$message.error(this.msgInvalid);
				return false;
			} else {
				return true;
			}
		},
		getNailsCurrent(){
			let lls = [];
			for (let i = 0; i < listLayerNail.length; i++) {
			  lls.push(listLayerNail[i].getLatLng());
			}
			if (this.isPlotting) {
			  lls.push(markerMover.getLatLng());
			}
			return lls;
		},
    finish() {
      // console.log(4444, "finish")
      this.isPlotting = false;
      markerMover.setLatLng([0, 0]);
			markerHint.setLatLng([0, 0]);
			if (listLayerNail.length - this.typePlotting < 1) {
				this.erase();
			} else {
				this.isSufficient = true;
			}
      this.refresh();
      this.$refs.mapContainer.style.cursor = "pointer";
    },
    erase(){
      for (let i = 0; i < listLayerNail.length; i++) {
        K.switchLayer(listLayerNail[i], false);
      }
      listLayerNail = [];
			this.isSufficient = false;
      layerLine.setLatLngs([]);
      layerRegion.setLatLngs([]);
    },
    registerLocation() {
      let ll = marker.getLatLng();
      this.lng = ll.lng.toFixed(6);
      this.lat = ll.lat.toFixed(6);
    },
    resetView(){
      console.log(4444, "resetView");
    },

    addNail(latlng){
    	// console.log(1111, "addNail")
    	var nail = L.marker(latlng, {
    		icon: L.icon({
    			iconUrl: require("@/assets/plotter/nail.png"),
    			iconAnchor: [4, 4],
    			// popupAnchor: [0, -32],
    		}),
    		draggable: true,
    	});
      K.switchLayer(nail, true);
    	var that = this;
    	nail.on("move", function(e){
    		markerHammer.setLatLng(e.latlng);
    		that.refresh();
    	});
    	nail.on("moveend", function(e){
    		markerHammer.setLatLng([0, 0]);
				that.refresh();
				that.check();
    		// that.showSplittedResult();
    	});
    	// nail.on("movestart", function(e){
    	// 	that.registerBladeCurrent();
    	// });
    	nail.on("mouseover", function(e) {
    		if (that.isPlotting) {
    			return;
    		}
    		markerHammer.setLatLng(e.latlng);
    	});
    	nail.on("mouseout", function(e) {
    		markerHammer.setLatLng([0, 0]);
    	});
    	listLayerNail.push(nail);
      this.refresh();
      if (this.preloadReady) {
        this.check();
      }
    },
    onClickPlot(k) {
      this.erase();
      this.isPlotting = true;
      this.typePlotting = k;
      this.$refs.mapContainer.style.cursor = "none";
    },
    onClickErase(){
			this.finish();
      this.erase();
    },
    handleCancel() {
			this.onClickErase();
      this.visible = false;
    },
    handleConfirm() {
      let s = toGeometry(this.typePlotting, this.getNailsCurrent());
      if (this.geojson != s) {
        this.visibleDialogChange = true;
      } else {
        this.handleCancel();
      }
    },
    onClickChangeCancel(){
      this.projectToBeDeleted = {};
      this.visibleDialogChange = false;
    },
    onClickChangeConfirm(){
      // let s = toGeometry(this.typePlotting, this.getNailsCurrent());
      let lls = this.getNailsCurrent();
      let s = "";
      for (let i = 0; i < lls.length; i++) {
        s += lls[i].lng + "," + lls[i].lat + ";";
      }
      this.visibleDialogChange = false;
      this.$emit("plotted", s);
      this.handleCancel();

    },
    onClickSwitchBaseMap(){
      this.isLayerImgShowing = !this.isLayerImgShowing;
      K.switchLayer(layerImg, this.isLayerImgShowing);
      K.switchLayer(layerVec, !this.isLayerImgShowing);
    },
  }
}
</script>

<style scoped>
  #map2D_for_plotting {
  	width: 100%;
  	height: 67vh;
  	background-color: #cacdd0;
    cursor: pointer;
  }
</style>
