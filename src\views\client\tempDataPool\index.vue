<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          clearable
          v-model.trim="queryParams.projectName"
          placeholder="请输入项目名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="磅站名称" prop="stationName">
        <el-input
          clearable
          v-model.trim="queryParams.stationName"
          placeholder="请输入磅站名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="carNumber">
        <el-input
          clearable
          v-model.trim="queryParams.carNumber"
          placeholder="请输入车牌号"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--          v-hasPermi="['client:tempDataPool:save']"-->
      <!--        >新增</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['client:tempDataPool:update']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['client:tempDataPool:delete']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/client/tempDataPool/page" ref="tempDataPoolTable" row-key="id"
              @my-selection-change="handleSelectionChange"
    >
      <el-table-column header-align="center" align="left" label="项目名称" min-width="180" prop="projectName"
                       sortable="custom" column-key="project.NAME"
      ></el-table-column>
      <el-table-column header-align="center" align="left" label="标段名称" min-width="60" prop="sectionName"
                       sortable="custom" column-key="project.SECTION_NAME"
      ></el-table-column>
      <el-table-column header-align="center" align="left" label="磅站名称" min-width="80" prop="stationName"
                       sortable="custom" column-key="station.NAME"
      ></el-table-column>
      <el-table-column header-align="center" align="center" label="车牌号" min-width="60" prop="carNumber"
                       sortable="custom" column-key="CAR_NUMBER"
      ></el-table-column>
      <el-table-column header-align="center" align="center" label="重量" min-width="40" prop="weight" sortable="custom"
                       column-key="WEIGHT"
      >
        <template #default="scope">
          {{common.toThousands(scope.row.weight,2,',')}}
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="数据类型" min-width="50" prop="dataType"
                       sortable="custom" column-key="DATA_TYPE"
      >
        <template #default="scope">
          <my-view pvalue="dataType" :value="scope.row.dataType"></my-view>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="创建时间" min-width="52" prop="createTime"
                       sortable="custom" column-key="CREATE_TIME"
      ></el-table-column>
      <el-table-column label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-document-copy"
            title="查看详情"
            @click="handleView(scope.row)"
            v-hasPermi="['client:tempDataPool:info']"
          ></el-button>
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['client:tempDataPool:update']"
          ></el-button>
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['client:tempDataPool:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改客户端临时数据池对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="tempDataPool" label-width="120px">
        <el-row :span="24" v-if="mode === 'update'">
          <el-col :span="12">
            <my-form-item
              label="项目名称"
              ref="projectId"
              prop="projectId"
              :rules="[{notNull:true,message:'请选择项目', trigger:['blur','change']}]"
            >
              <my-select
                v-model="tempDataPool.projectId"
                :options="projectList"
                placeholder="请选择项目"
                @change="projectSelectChange"
              >
                <el-option v-for="item in projectList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item
              label="磅站名称"
              ref="station"
              prop="stationId"
              :rules="[{notNull:true,message:'请选择磅站', trigger:['blur','change'] }]"
            >
              <my-select
                v-model="tempDataPool.stationId"
                :options="stationList"
                placeholder="请选择磅站"
                @change="stationSelectChange"
              >
              </my-select>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row :span="24" v-if="mode==='detail'">
          <el-col :span="12">
            <my-form-item label="项目名称" ref="projectName" prop="projectName">
              <my-input disabled v-model="tempDataPool.projectName" placeholder="请输入客户端"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="磅站名称" ref="stationName" prop="stationName">
              <my-input disabled v-model="tempDataPool.stationName" placeholder="请输入磅站"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <my-form-item v-if="tempDataPool.sectionName" label="标段名称" ref="sectionName" prop="sectionName">
              <my-input disabled v-model="tempDataPool.sectionName" placeholder="请输入标段"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="车牌号" ref="carNumber" prop="carNumber">
              <my-input :disabled="mode==='detail'" v-model="tempDataPool.carNumber" placeholder="请输入车牌号"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="重量" ref="weight" prop="weight">
              <my-input :disabled="mode==='detail'" v-model="tempDataPool.weight" placeholder="请输入重量"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="数据类型" ref="dataType" prop="dataType">
              <my-select :disabled="mode==='detail'" pvalue="dataType" v-model="tempDataPool.dataType" placeholder="请选择状态"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="创建时间" ref="createTime" prop="createTime">
              <my-input disabled v-model="tempDataPool.createTime" placeholder="请输入创建时间"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <my-form-item label="现场照片" style="margin-bottom: 0" ref="imgFileIds" prop="imgFileIds">
            <el-image
              v-for="(item,index) in tempDataPool.thumbnailPathImgUrls"
              :key="index"
              title="查看照片"
              class="currentPicture"
              :src="item"
              fit="cover"
              @click="handleViewPicture(tempDataPool.imgUrls,index)"
            />
          </my-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="mode!=='detail'" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 预览图片遮罩层 -->
    <el-image-viewer
      style="z-index: 2050"
      v-if="imageVisible"
      :url-list="imageUrl"
      :on-close="() => {imageVisible = false}"
    />
  </div>
</template>

<script>
import {
  getTempDataPool,
  delTempDataPool,
  delTempDataPoolBatch,
  addTempDataPool,
  updateTempDataPool,
  updateTempDataPoolAndCache,
  listProjectNamesByUseClient,
  listStationByProjectId
} from '@/api/client/tempDataPool'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import common from '../../../utils/common'

export default {
  name: "TempDataPool",
  computed: {
    common() {
      return common
    }
  },
  components: {
    MyAreaSelect,
    'el-image-viewer': () => import("element-ui/packages/image/src/image-viewer"),
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        projectName: '',
        stationName: '',
        carNumber: ''
      },
      // 表单参数
      tempDataPool: {},
      mode: '',
      // 是否显示预览图片弹出层
      imageVisible: false,
      // 图片路径
      imageUrl: [],
      // 项目列表
      projectList: [],
      // 磅站列表
      stationList: [],
    };
  },
  mounted() {
    listProjectNamesByUseClient().then(res => {
      if (res.list && res.list.length > 0) {
        this.projectList = res.list.map(item => {
          if(item.sectionName){
            return { 'name': item.name, 'value': item.deptId, 'sectionName':item.sectionName}
          }else {
            return { 'name': item.name, 'value': item.deptId }
          }
        });
      }
    })
  },
  methods: {
    /** 查询客户端临时数据池列表 */
    reload(restart) {
      this.$refs.tempDataPoolTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.tempDataPool = {
        projectId:'',
        projectName:'',
        id: '',
        createTime: '',
        clientId: '',
        stationId: '',
        carNumber: '',
        weight: null,
        imgFileIds: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = this.$refs.tempDataPoolTable.getSelectRowKeys()
      this.single = this.ids.length != 1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户端临时数据";
    },
    /** 查询按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getTempDataPool(id).then(r => {
        this.tempDataPool = r.tempDataPool;
        this.open = true;
        this.title = "临时数据详情";
        this.mode = 'detail'
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getTempDataPool(row.id).then(r => {
        this.tempDataPool = r.tempDataPool;
        this.open = true;
        this.title = "修改临时数据";
        this.mode = 'update'
        listStationByProjectId(this.tempDataPool.projectId).then(res => {
          if (res.list && res.list.length > 0) {
            this.stationList = res.list.map(item => {
              return { 'name': item.name, 'value': item.id }
            });
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.tempDataPool.id) {
            updateTempDataPoolAndCache(this.tempDataPool).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addTempDataPool(this.tempDataPool).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        } else {
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(() => {
        if (row.id) {
          return delTempDataPool(row.id);
        } else {
          return delTempDataPoolBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 预览图片操作 */
    handleViewPicture(imgUrlList, index) {
      this.imageVisible = true;
      if (index > 0) {
        this.imageUrl = JSON.parse(JSON.stringify(imgUrlList));
        this.imageUrl.unshift(imgUrlList[index])
        this.imageUrl.splice(index + 1, 1)
      } else {
        this.imageUrl = JSON.parse(JSON.stringify(imgUrlList));
      }
    },
    /** 项目选择change事件 */
    projectSelectChange(val) {
      this.stationList = [];
      this.tempDataPool.stationId = '';
      this.tempDataPool.stationName = '';
      if (val) {
        const obj = this.projectList.find(item => {
          return item.value === val;
        });
        console.log(obj)
        this.tempDataPool.projectName = obj.name ? obj.name : '';
        this.tempDataPool.sectionName = obj.sectionName? obj.sectionName : '';
        listStationByProjectId(val).then(res => {
          if (res.list && res.list.length > 0) {
            this.stationList = res.list.map(item => {
              return {'name': item.name, 'value': item.id}
            });
          }
        });
      } else {
        this.tempDataPool.projectName = '';
      }
    },
    /** 磅站选择change事件 */
    stationSelectChange(val) {
      if (val) {
        const obj = this.stationList.find(item => {
          return item.value === val;
        });
        this.tempDataPool.stationName = obj.name ? obj.name : '';
      } else {
        this.tempDataPool.stationName = '';
      }
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.tempDataPoolTable.changeTableHeight();
  },
};
</script>

<style scoped>
.currentPicture {
  width: 100px;
  height: 100px;
  cursor: pointer;
  margin-right: 10px
}
</style>
