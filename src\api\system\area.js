import request from '@/utils/request'

//获取区域树
export function getAreaTreeData() {
  return request({
    url: '/sys/area/select',
    method: 'post',
  })
}

// 新增区域
export function addArea(data) {
  return request({
    url: '/sys/area/save',
    method: 'post',
    data: data
  })
}

// 修改区域
export function updateArea(data) {
  return request({
    url: '/sys/area/update',
    method: 'post',
    data: data
  })
}

// 删除区域
export function delArea(Id) {
  return request({
    url: '/sys/area/delete/' + Id,
    method: 'post'
  })
}

export function checkAreaCode(area) {
  return  request({
    url: '/sys/area/checkAreaCode',
    method: 'post',
    params: {
      areaCode: area.parentCode + "_" + area.selfCode,
      id: area.id
    },
    headers: {
      allowRepeatSubmit: true,
    }
  })
}

// 根据areaCode获取子集区域
export function getSubList(areaCode) {
  return  request({
    url: '/sys/area/subList',
    method: 'post',
    params: {
      areaCode: areaCode,
    },
    headers: {
      allowRepeatSubmit: true,
    }
  })
}


