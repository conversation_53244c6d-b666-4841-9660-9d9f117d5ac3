<template>
  <div class="app-container">
    <div v-if="!showViewSysLog">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete(null)"
            v-hasPermi="['sys:syslog:del']"
          >删除</el-button>
        </el-col>
        <right-toolbar
          class="rightToolbar"
          @queryTable="getList">
        </right-toolbar>
      </el-row>
      <el-table
          border
          ref="sysLoginLogTable"
          v-loading="loading"
          :data="page.list"
          datatype="lazy"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column type="index" width="80" label="序号" align="center" :index="page.pageSize * (page.currPage - 1) + 1" fixed="left" />
          <el-table-column min-width="200" label="日期" align="center" prop="date" column-key="DATE" />
          <el-table-column min-width="400" label="文件名" align="center" prop="name" column-key="NAME" />
          <el-table-column min-width="150" label="操作" align="center" column-key="caozuo" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="primary"
                class="btn-table-operate"
                title="查看"
                size="mini"
                icon="el-icon-view"
                @click="handleView(scope.row.name)"
                v-hasPermi="['sys:syslog:view']"
              ></el-button>
              <el-button
                type="success"
                class="btn-table-operate"
                title="下载"
                size="mini"
                icon="el-icon-download"
                @click="handleDownload(scope.row.name)"
                v-hasPermi="['sys:syslog:download']"
              ></el-button>
              <el-button
                type="danger"
                class="btn-table-operate"
                title="删除"
                size="mini"
                icon="el-icon-delete"
                v-hasPermi="['sys:syslog:del']"
                @click="handleDelete(scope.row.name)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
    </div>
    <!-- 查看系统日志 -->
    <el-card v-else>
      <div slot="header">
        查看系统日志-{{ fileName }}
          <el-button
            size="mini"
            type="text"
            icon="el-icon-back"
            style="float:right;padding: 3px;"
            @click="goBack"
          >返回</el-button>
      </div>
      <div class="sysLogBody" :style="{ height: windowHeight-205 + 'px' }">
        <p style="text-align: center" v-show="sysLogList.length === 0">
          <i class="el-icon-loading"></i>文件读取中...
        </p>
        <p v-for="item in sysLogList">{{ item }}</p>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getListData, batchDelSysLog, delOneSysLog, viewSysLog, downLoadSysLog } from '@/api/system/syslog'
import elDragDialog from '@/directive/dialog/drag'
export default {
  name: 'Syslog',
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
    return {
      loading: true,
      multiple: true,
      names: [],
      page: {
        currPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPage: 0,
        list: []
      },
      fileName: '',
      showViewSysLog: false,
      sysLogList: [],
      windowHeight: document.documentElement.clientHeight ,
    }
  },
  methods: {
    //查询操作日志列表
    getList() {
      this.loading = true
      getListData(this.queryParams).then(r => {
          this.page.totalCount = r.list.length
          this.page.list = r.list
          this.loading = false
        })
    },
    //多选框选中数据
    handleSelectionChange(selection) {
      this.names = selection.map(item => item.name)
      this.multiple = !selection.length
    },
    //删除操作
    handleDelete(name) {
      let tip = name === null ? '确定要删除选中的 ' + this.names.length + ' 条记录？' : '确定要删除选中的记录？'
      this.$modal.confirm(tip).then(() => {
        this.$modal.loading('加载中')
        if (name) {
          return delOneSysLog(name)
        } else {
          return batchDelSysLog(this.names)
        }
      }).then(() => {
        this.$modal.closeLoading()
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch((error) => {
        console.log(error)
        this.$modal.closeLoading();
        })//捕获异常错误
    },
    //查看操作
    handleView(name) {
      this.showViewSysLog = true
      this.fileName = name
      viewSysLog(name).then(r => {
        this.sysLogList = r.list
      })
    },
    //返回操作
    goBack() {
      this.sysLogList = []
      this.showViewSysLog = false
    },
    //下载操作
    handleDownload(name) {
      downLoadSysLog(name)
    },
  },
  mounted() {
    this.getList()
  }
}
</script>
<style scoped>
.sysLogBody {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>

