
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px" @submit.native.prevent>
      <el-form-item label="文件名" prop="uploadFileName">
        <el-input
          clearable
          v-model.trim="queryParams.uploadFileName"
          placeholder="请输入姓名"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['help:doc:upload']"
        >上传文件</el-button>
        <input
          ref="uploadFileInput"
          type="file"
          @change="uploadChange"
          style="display: none"
          accept=".pdf,.mp4,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
        >
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>
    <my-table url="/help/doc/page" ref="docTable" row-key="id" :multiselect="false">
      <el-table-column label="文件名称" header-align="center" align="left" min-width="200" prop="uploadFileName" sortable="custom"
                       column-key="upload_File_Name">
        <template v-slot="scope">
          <el-button type="text" @click="handleDownload(scope.row)">{{scope.row.uploadFileName}}</el-button>
        </template>
      </el-table-column>
      <el-table-column  label="创建时间" width="150" prop="createTime" align="center" header-align="center" sortable="custom" column-key="CREATE_TIME"></el-table-column>

      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center" width="150">
        <template v-slot="scope">
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            title="下载"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"
          ></el-button>
            <el-button
              size="mini"
              type="danger"
              class="btn-table-operate"
              icon="el-icon-delete"
              title="删除"
              @click="handleDelete(scope.row)"
              v-hasPermi="['com:uploadFile:delete']"
            ></el-button>
        </template>
      </el-table-column>
    </my-table>

  </div>
</template>

<script>
import {uploadDoc,delDoc} from "@/api/help/doc"
import {download} from "@/utils/request";
export default {
  name:'Doc',
  data(){
    return{
      queryParams:{
        uploadFileName: '',
      },
      // 显示搜索条件
      showSearch: true,
    }
  },
  methods:{
    /** 查询运砂申请列表 */
    reload(restart) {
      this.$refs.docTable.search(this.queryParams, restart);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /**
     * 上传文件
     */
    handleAdd() {
      this.$refs.uploadFileInput.click();
    },
    uploadChange() {
        if(this.$refs.uploadFileInput.files.length>0){
          var file=this.$refs.uploadFileInput.files[0]
          var formData = new FormData();

          formData.append("file", file);
          uploadDoc(formData).then(r=>{
            this.$modal.msgSuccess("上传成功");
            this.$refs.uploadFileInput.value = '';
            this.reload();
          }).catch((err) => {
            console.log('err', err)
            this.$refs.uploadFileInput.value = '';
          });
        }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        return delDoc(row.id);
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**
     * 下载文件
     */
    handleDownload(row){
        this.download("/com/uploadfile/download/"+row.id,null,row.uploadFileName)
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.docTable.changeTableHeight();
  },

}
</script>


<style scoped lang="scss">

</style>
