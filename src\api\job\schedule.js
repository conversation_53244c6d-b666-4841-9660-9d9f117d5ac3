import request from "@/utils/request";

export function listTasks(query) {
  return request({
    url: 'sys/schedule/list',
    method: 'post',
    params: query
  })
}

// 查询定时任务详细
export function getTasks(jobId) {
  return request({
    url: '/sys/schedule/info/' + jobId,
    method: 'post'
  })
}

// 新增定时任务
export function addTasks(data) {
  return request({
    url: '/sys/schedule/save',
    method: 'post',
    data: data
  })
}

// 修改定时任务
export function updateTasks(data) {
  return request({
    url: '/sys/schedule/update',
    method: 'post',
    data: data
  })
}

// 删除定时任务
export function delTasks(jobIds) {
  return request({
    url: '/sys/schedule/delete/' ,
    method: 'post',
    data: jobIds
  })
}
// 立即执行任务
export function runTasks(jobIds) {
  return request({
    url: '/sys/schedule/run',
    method: 'post',
    data: jobIds
  })
}
// 暂停定时任务
export function pauseTasks(jobIds) {
  return request({
    url: '/sys/schedule/pause',
    method: 'post',
    data: jobIds
  })
}
// 恢复定时任务
export function resumeTasks(jobIds) {
  return request({
    url: '/sys/schedule/resume',
    method: 'post',
    data: jobIds
  })
}
