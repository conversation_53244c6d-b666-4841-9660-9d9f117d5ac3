<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"
      @submit.native.prevent>
      <el-form-item label="项目名称" prop="name">
        <my-input style="width: 205px" v-model.trim="queryParams.name" placeholder="项目名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input style="width: 205px" v-model.trim="queryParams.sectionName" placeholder="标段名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select ref="areaSelect" v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <my-select id="type" pvalue="projectType" v-model="queryParams.type" placeholder="项目类型" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <div class="shenhe">
      <el-form-item label="审核状态" prop="auditStatus">
        <!-- <my-select id="auditStatus" pvalue="auditStatus" v-model="queryParams.auditStatus" placeholder="审核状态" /> -->
         <MyRadioGroup id="auditStatus" pvalue="auditStatus" v-model="queryParams.auditStatus" @mychange="handleRadioChange" placeholder="审核状态"></MyRadioGroup>
      </el-form-item>
      </div>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['project:projectDeclare:save']">新增</el-button>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['project:projectDeclare:update']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="danger"-->
      <!--          icon="el-icon-delete"-->
      <!--          size="mini"-->
      <!--          :disabled="multiple"-->
      <!--          @click="handleDelete"-->
      <!--          v-hasPermi="['project:projectDeclare:delete']"-->
      <!--        >删除</el-button>-->
      <!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/projectDeclare/page" :fixed="true" ref="projectDeclareTable" row-key="deptId"
      @my-selection-change="handleSelectionChange">
      <el-table-column label="项目名称" min-width="250" fixed="left" header-align="center" align="left" prop="name"
        sortable="custom" column-key="NAME">
        <template #default="{ row }">
         <!-- <el-tooltip :content="row.name" placement="top" effect="light">
            <div>{{ row.name }}</div>
          </el-tooltip> -->
          <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="标段名称" min-width="160" prop="sectionName" header-align="center" align="left"
        sortable="custom" column-key="SECTION_NAME"></el-table-column>
      <el-table-column label="项目位置" min-width="160" prop="areaName" header-align="center" align="left" sortable="custom"
        column-key="AREA_NAME"></el-table-column>
      <el-table-column label="项目类型" min-width="150" prop="type" header-align="center" align="center" sortable="custom"
        column-key="TYPE">
        <template #default="{ row }">
          <my-view pvalue="projectType" :value="row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" min-width="150" prop="auditStatus" header-align="center" align="center"
        sortable="custom" column-key="AUDIT_STATUS">
        <template #default="{ row }">
          <my-view pvalue="auditStatus" :value="row.auditStatus"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="采砂人（供砂人）" min-width="150" prop="leaderName" header-align="center" align="center"
        sortable="custom" column-key="LEADER_NAME"></el-table-column>
      <el-table-column label="联系电话" min-width="150" prop="contact" header-align="center" align="center"
        sortable="custom" column-key="CONTACT"></el-table-column>
      <el-table-column label="监管部门" align="left" header-align="center" min-width="150" prop="deptName" sortable="custom"
        column-key="dept.NAME"> </el-table-column>
        <el-table-column label="审批时间" align="left" header-align="center" min-width="150" prop="departmentAuditTime" sortable="custom"
          column-key="DEPTMARTMENT_AUDITTIME"> </el-table-column>
      <el-table-column label="控制总量（万吨）" min-width="150" header-align="center" align="right" prop="totalYield"
        sortable="custom" column-key="TOTAL_YIELD">
        <template #default="scope">
          {{ common.toThousands(scope.row.totalYield, 2, ',') }}
        </template>
      </el-table-column>
      <el-table-column label="采砂许可证/弃砂审批文号" min-width="200" header-align="center" align="center" prop="licenseNo"
        sortable="custom" column-key="LICENSE_NO"></el-table-column>
      <el-table-column label="操作" column-key="caozuo" min-width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.auditStatus == 'reject' && deptcode == scope.row.deptCode" size="mini" type="success"
            class="btn-table-operate" icon="el-icon-edit" title="修改" @click="handleUpdate(scope.row)"
            v-hasPermi="['project:projectDeclare:update']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-document-copy" title="查看"
            @click="handleView(scope.row)" v-has-permi="['project:projectDeclare:info']"></el-button>
          <el-button v-if="scope.row.auditStatus == 'reject' && deptcode == scope.row.deptCode" size="mini" type="danger"
            class="btn-table-operate" icon="el-icon-delete" title="删除" @click="handleDelete(scope.row)"
            v-hasPermi="['project:projectDeclare:delete']"></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改项目/沙场信息表对话框 -->
    <my-dialog :zIndex="1500" :title="title" :visible.sync="open" width="900px" height="70vh" append-to-body
      @close="cancel">
      <el-tabs type="border-card" v-model="activeName">
        <el-tab-pane label="项目信息" name="first">
          <el-form ref="form" :model="projectDeclare" label-width="180px" :disabled="mode === 'view'">
            <my-form-item label="项目名称" ref="name" prop="name" :rules="[{ notNull: true, message: '请输入项目名称' }]">
              <my-input v-model.trim="projectDeclare.name" maxlength="50" placeholder="请输入项目名称" />
            </my-form-item>
            <my-form-item label="标段名称" ref="sectionName" prop="sectionName">
              <my-input v-model.trim="projectDeclare.sectionName" maxlength="30" placeholder="请输入标段名称" />
            </my-form-item>
            <my-form-item label="项目位置" ref="areaCode" prop="areaCode"
              :rules="[{ notNull: true, message: '请选择项目位置', trigger: 'change' }]">
              <my-area-select :children-disabled="childrenDisabled" :parent-disabled="parentDisabled"
                v-model="projectDeclare.areaCode" :component-type="true" :disabled="mode == 'view' || mode == 'update'"
                @change="getAreaName" />
            </my-form-item>
            <my-form-item label="项目类型" ref="type" prop="type"
              :rules="[{ notNull: true, message: '请选择项目类型', trigger: 'change' }]">
              <my-select pvalue="projectType" v-model="projectDeclare.type" placeholder="请选择项目类型" />
            </my-form-item>
            <my-form-item v-if="mode === 'view'" label="监管部门" ref="deptName" prop="deptName">
              <my-input v-model.trim="projectDeclare.deptName" disabled="disabled" />
            </my-form-item>
            <my-form-item v-else label="监管部门" ref="parentId" prop="parentId"
              :rules="[{ notNull: true, message: '请选择监管部门', trigger: 'change' }]">
              <my-cascader ref="deptCascader" :disabled="mode === 'update'" v-model="projectDeclare.parentId"
                placeholder="请选择监管部门" />
            </my-form-item>

            <my-form-item label="控制总量(万吨)" ref="totalYield" prop="totalYield" :rules="[{ notNull: true, message: '请输入控制总量(万吨)' },
            { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
              <my-input v-model="projectDeclare.totalYield" maxlength="20" placeholder="请输入控制总量(万吨)" />
            </my-form-item>
            <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="25年1月1日前利用量（万吨）" ref="historyYield"
              prop="historyYield" :rules="[{ notNull: true, message: '请输入25年1月1日前利用量（万吨）' },
              { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
              <my-input v-model="projectDeclare.historyYield" maxlength="20" placeholder="请输入25年1月1日前利用量（万吨）" />
            </my-form-item>
            <my-form-item :label="projectDeclare.type == 'abandonSand' ? '弃砂方案是否报备' : '治河采砂方案是否报备'" ref="reportedFlag"
              prop="reportedFlag" :rules="[{ notNull: true, message: projectDeclare.type == 'abandonSand' ? '弃砂方案是否报备' : '治河采砂方案是否报备' }]">
              <my-radio :options="[{ name: '是', value: true }, { name: '否', value: false }]"
                v-model="projectDeclare.reportedFlag"></my-radio>
            </my-form-item>
            <my-form-item :label="projectDeclare.type == 'abandonSand' ? '弃砂方案批文及文号' : '采砂许可证号'" ref="licenseNo"
              prop="licenseNo"
              :rules="[{ notNull: true, message: projectDeclare.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号' }]">
              <my-input v-model="projectDeclare.licenseNo" maxlength="128"
                :placeholder="projectDeclare.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号'" />
            </my-form-item>
            <my-form-item :label="projectDeclare.type == 'abandonSand' ? '供砂人' : '采砂人'" ref="leaderName"
              prop="leaderName"
              :rules="[{ notNull: true, message: projectDeclare.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人' }]">
              <my-input v-model="projectDeclare.leaderName" maxlength="128"
                :placeholder="projectDeclare.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人'" />
            </my-form-item>
            <my-form-item label="联系电话" ref="contact" prop="contact" :rules="[{ notNull: true, message: '请输入联系电话' },
            { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
              <my-input v-model="projectDeclare.contact" placeholder="请输入联系电话" />
            </my-form-item>
            <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="是否国债项目" ref="nationalDebt"
              prop="nationalDebt" :rules="[{ notNull: true, message: '请选择是否国债项目' }]">
              <my-radio :options="[{ name: '是', value: true }, { name: '否', value: false }]"
                v-model="projectDeclare.nationalDebt"></my-radio>
            </my-form-item>
            <my-form-item v-if="projectDeclare.nationalDebt == true && projectDeclare.type == 'abandonSand'"
              label="国债类型" ref="nationalDebtType" prop="nationalDebtType"
              :rules="[{ notNull: true, message: '请选择国债类型' }]">
              <my-select pvalue="nationalDebtType" v-model="projectDeclare.nationalDebtType" placeholder="请选择国债类型" />
            </my-form-item>
            <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="处置方式" ref="disposalMethodJson"
              prop="disposalMethodJson"
              :rules="[{ validator: validate, trigger: ['blur', 'change'] }, { notNull: true, message: '请选择处置方式' }]">
              <div v-for="item in projectDeclare.disposalMethodJson">
                <el-row style="margin-bottom: 10px">
                  <el-col :span="12">
                    <el-checkbox v-model="item.selected" @change="changeCheckbox(item)">
                      <template>
                        <my-view pvalue="disposalMethod" :value="item.method"></my-view>
                      </template>
                    </el-checkbox>
                    <my-input v-if="item.selected && item.method == 'qiTa'" style="margin-left: 20px;width: 200px"
                      v-model.trim="item.remark" :maxlength="16" placeholder="请输入其他处置方式 " />
                  </el-col>
                  <el-col :span="12">
                    <div>
                      <my-form-item label="效益（万元）" v-if="item.selected" label-width="120px" class="asterisk">
                        <my-input @input="inputAmount" v-model.trim="item.amount" :maxlength="16"
                          placeholder="请输入效益（万元）" />
                      </my-form-item>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div style="display: flex">
                <div>总效益（万元）</div>
                <div>{{ common.toThousands(investmentTotal, '4', ',') }}</div>
              </div>
            </my-form-item>
            <my-form-item v-if="projectDeclare.type != 'abandonSand'" label="项目总投资（万元）" ref="investment"
              prop="investment" :rules="[{ notNull: true, message: '请输入项目总投资（万元）' }]">
              <my-input v-model.trim="projectDeclare.investment" :maxlength="16" placeholder="请输入项目总投资（万元）" />
            </my-form-item>
            <my-form-item v-if="projectDeclare.type == 'riverSand' || projectDeclare.type == ''" label="治理河长（km）"
              ref="reverLength" prop="reverLength" :rules="[{ notNull: true, message: '请输入治理河长（km）' }]">
              <my-input v-model.trim="projectDeclare.reverLength" :maxlength="16" placeholder="请输入治理河长（km）" />
            </my-form-item>
            <my-form-item label="监督举报电话" ref="complaintMobile" prop="complaintMobile"
              :rules="[{ notNull: true, message: '请输入监督举报电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
              <my-input v-model.trim="projectDeclare.complaintMobile" placeholder="请输入监督举报电话" :maxlength="32" />
            </my-form-item>
            <my-form-item label="查询电话" ref="queryMobile" prop="queryMobile"
              :rules="[{ notNull: true, message: '请输入查询电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
              <my-input v-model.trim="projectDeclare.queryMobile" placeholder="请输入查询电话" :maxlength="32" />
            </my-form-item>
            <el-row>
              <el-col :span="12">
                <my-form-item :label="projectDeclare.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" prop="imgList"
                  :rules="[{ required: true, validator: validatorImg, trigger: ['blur', 'change'] }]">
                  <my-pv-upload :label="projectDeclare.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" :multiple="false"
                    :uploadMode="'image'" :file-list="imgList" :pathFieldName="pathFieldName1" :fileType="fileType1"
                    :disabled="mode == 'view'" @getFileList="getFileList1">
                  </my-pv-upload>
                  <span v-if="mode == 'add'">请上传透明背景png格式图片</span>
                </my-form-item>
              </el-col>
              <el-col :span="12">
                <my-form-item label="监管部门印章" prop="imgList2"
                  :rules="[{ required: true, validator: validatorImg2, trigger: ['blur', 'change'] }]">
                  <my-pv-upload label="监管部门印章" :multiple="false" :uploadMode="'image'" :file-list="parentImgList"
                    pathFieldName="parent-dept-img" :fileType="fileType1" :disabled="mode == 'view'"
                    @getFileList="getFileList2">
                  </my-pv-upload>
                  <span v-if="mode == 'add'">请上传透明背景png格式图片</span>
                </my-form-item>
              </el-col>
            </el-row>

            <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="uploadFileList" prop="uploadFileList"
              :rules="[{ required: true, validator: validatorImg3, trigger: ['blur', 'change'] }]">
              <my-file-upload :file-list="projectDeclare.uploadFileList" :pathFieldName="pathFieldName"
                :fileType="fileType" @getFileList="getFileList" :disabled="mode == 'view'" />
              <template #label>
                <span>{{ projectDeclare.type == 'abandonSand' ? '附件（方案批文）' : '附件（采砂许可证）' }}</span>
              </template>
            </my-form-item>
            <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="paiMaiFileList" prop="paiMaiFileList"
              :rules="[{ required: true, validator: validatorImg4, trigger: ['blur', 'change'] }]"
              v-show="projectDeclare.type == 'abandonSand'">
              <my-file-upload :file-list="projectDeclare.paiMaiFileList" :pathFieldName="pathFieldName8"
                :fileType="fileType2" @getFileList="getFileList8" :disabled="mode == 'view'" />
              <template #label>
                <span>{{ projectDeclare.type == 'abandonSand' ? '附件（中拍文件）' : '' }}</span>
              </template>
            </my-form-item>
            <my-form-item label="上述信息填报人联系电话" ref="createUserMobile" prop="createUserMobile" :rules="[{ notNull: true, message: '请输入上述信息填报人联系电话' },
            { isMobile: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
              <my-input v-model="projectDeclare.createUserMobile" placeholder="请输入上述信息填报人联系电话" />
            </my-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="second" v-if="mode !== 'add'">
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item, index) in processList" :key="item.id">
              <td style="width: 30%">{{ item.createTime }}</td>
              <td>{{ item.createUser }}</td>
              <td>
                <my-view pvalue="auditPassed" :value="item.passed + ''"></my-view>
              </td>
              <td>{{ item.opinion }}</td>
            </tr>
          </table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="mode != 'view'" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </my-dialog>



  </div>
</template>

<script>
import {
  getProjectDeclare,
  delProjectDeclare,
  delProjectDeclareBatch,
  addProjectDeclare,
  updateProjectDeclare
} from "@/api/project/projectDeclare";
import Template from '@/views/sms/template/index.vue'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import GeoPlotter from '@/views/components/plotter/index.vue'
import InputCoords from '@/views/components/input-coords/index.vue'
import MyPvUpload from '@/components/YB/MyPvUpload.vue'
import MyCascader from '@/components/YB/MyCascader.vue'
import MyFileUpload from '@/components/YB/MyFileUpload.vue'
import MyRadioGroup from '@/components/YB/MyRadioGroup.vue'
import {
  delProjectFile,
  getProject
} from '@/api/project/project'
import common from '../../../utils/common'
import {
  getUser
} from '@/api/statistics/projectStatistics'
import {
  getDeptTreeData
} from '@/api/system/dept'
import {
  getProcessList
} from '@/api/reports/responsiblePersion'

export default {
  name: "ProjectDeclare",
  computed: {
    common() {
      return common
    },
    investmentTotal() {
      return this.projectDeclare.disposalMethodJson.reduce((total, item) => {
        if (this.projectDeclare.type == 'abandonSand') {
          if (item.selected) {
            total += Number(item.amount)
          }
          return total
        }
      }, 0)
    }
  },
  components: {
    MyFileUpload,
    MyCascader,
    MyPvUpload,
    InputCoords,
    GeoPlotter,
    MyAreaSelect,
    Template,
    MyRadioGroup
  },
  data() {
    return {
      activeName: 'first',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: '',
        sectionName: '',
        type: '',
        auditStatus: '',
        areaCode: '',
      },

      // 表单参数
      projectDeclare: {
        disposalMethodJson: [{
          method: 'paiMai',
          amount: '',
          selected: false
        },
        {
          method: 'shiZheng',
          amount: '',
          selected: false
        },
        {
          method: 'qiTa',
          amount: '',
          selected: false,
          remark: ''
        }
        ],
      },
      mode: '',
      // 文件类型  常用文件类型：.txt,.doc,.docx,.pdf,.xls,.xlsx,.bmp,.gif,.jpeg,.png,.jpg,.zip,.rar,.7z,.tar
      fileType: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
      fileType1: '.png', //印章图片类型
      fileType2: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
      //印章图片
      imgList: [],
      parentImgList: [],
      dialogVisible: false,
      dialogImageUrl: "",
      // 上传类型
      pathFieldName: 'project-file',
      pathFieldName1: 'seal-image-img',
      pathFieldName8: 'projectPaiMai-file',
      // 是否显示附件列表弹出层
      openFileList: false,
      version: '',
      parentDisabled: false,
      childrenDisabled: false,
      processList: [],
      deptId: '',
      state: '',
      deptcode:''
    };
  },
  created() {
    if (this.$store.state.user.deptcode.length == 9) {
      this.state = 1;//市级
    } else if (this.$store.state.user.deptcode.length > 9) {
      this.state = 0;//县级
    } else if (this.$store.state.user.deptcode.length < 9) {
      this.state = 2;//省级
    }
    console.log(this.state, '用户角色');
    this.deptcode = this.$store.state.user.deptcode;
  },
  mounted() { },
  methods: {
    handleRadioChange(val) {
          console.log('子组件传递的值:', val)
          this.reload(true,val);
        },
    inputAmount() {
      this.$refs['form'].validateField('disposalMethodJson')
    },
    changeCheckbox(val) {
      this.$refs['form'].validateField('disposalMethodJson')
      if (!val.selected) {
        this.projectDeclare.disposalMethodJson.map(i => {
          if (val.method == i.method) {
            i.amount = ''
          }
          if (val.method == 'qiTa') {
            i.remark = ''
          }
          return i
        })
      }
      console.log(this.projectDeclare.disposalMethodJson)
    },
    validate(rule, value, callback) {
      const selectArray = []
      value.map(i => {
        selectArray.push(i.selected)
      })
      if (selectArray.includes(true)) {
        for (let index = 0; index < value.length; index++) {
          let item = value[index];
          let method = ''
          if (value[index].method == 'paiMai') {
            method = '拍卖'
          } else if (value[index].method == 'shiZheng') {
            method = '用于市政民生工程'
          } else if (value[index].method == 'qiTa') {
            method = '其他'
          }
          if (item.selected) {
            if (!value[index].amount) {
              callback(new Error('请输入' + method + '效益（万元）'))
              return
            }
            if (value[index].amount) {
              if (!/^[1-9]\d*(\.\d{1,4})?$|^0(\.\d{1,4})?$/.test(value[index].amount)) {
                callback(new Error(method + '效益（万元）必须大于0且必须为数字'))
                return
              }
            }
            if (value[index].method == 'qiTa' && !value[index].remark) {
              callback(new Error('请输入其他处置方式'))
              return
            }
          }
        }
        callback()
      } else {
        callback(new Error('请选择处置方式'))
      }

    },
    handleClick() {
      this.processList = []
      getProcessList(this.deptId).then(res => {
        this.processList = res.list
      })
    },
    validatorImg(rule, value, callback) {
      if (this.projectDeclare.fileList == null || this.projectDeclare.fileList.length <= 0) {
        callback(new Error("请上传印章图片"));
      } else {
        callback();
      }
    },
    validatorImg2(rule, value, callback) {
      if (this.projectDeclare.parentImgList == null || this.projectDeclare.parentImgList.length <= 0) {
        callback(new Error("请上传印章图片"));
      } else {
        callback();
      }
    },
    validatorImg3(rule, value, callback) {
      if (this.projectDeclare.uploadFileList == null || this.projectDeclare.uploadFileList.length <= 0) {
        callback(new Error("请上传附件"));
      } else {
        callback();
      }
    },
    validatorImg4(rule, value, callback) {
      if (this.projectDeclare.type == 'abandonSand' && this.projectDeclare.disposalMethodJson[0].selected == true) {
        if (this.projectDeclare.paiMaiFileList == null || this.projectDeclare.paiMaiFileList.length <= 0) {
          callback(new Error("请上传附件"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    /** 查询项目信息申报表列表 */
    reload(restart,val) {
      console.log(val)
      if(val){
        this.queryParams.auditStatus=val;
      }
      this.$refs.projectDeclareTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.activeName = 'first'
    },
    // 表单重置
    reset() {
      this.projectDeclare = {
        deptId: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptCode: '',
        name: '',
        sectionName: '',
        areaName: '',
        areaCode: '',
        type: '',
        parentId: '',
        totalYield: null,
        licenseNo: '',
        leaderName: '',
        contact: '',
        deleted: null,
        investment: null,
        reverLength: null,
        complaintMobile: '',
        queryMobile: '',
        auditStatus: '',
        version: null,
        createUserMobile: '',
        reportedFlag:false,
        disposalMethodJson: [{
          method: 'paiMai',
          amount: '',
          selected: false
        },
        {
          method: 'shiZheng',
          amount: '',
          selected: false
        },
        {
          method: 'qiTa',
          amount: '',
          selected: false,
          remark: ''
        }
        ],
        nationalDebt: false,
        nationalDebtType: '',
        historyYield: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.projectDeclareTable.getSelectRowKeys()
      this.single = this.ids.length != 1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.childrenDisabled = false
      this.parentDisabled = false
      this.reset();
      this.open = true;
      this.mode = 'add';
      this.imgList = [];
      this.parentImgList = [];
      this.title = "添加项目信息申报";
      getUser().then(r => {
        console.log(r)
        if (r.districtAreaCode) {
          this.projectDeclare.areaCode = r.districtAreaCode
          this.childrenDisabled = true
          this.parentDisabled = true
          this.getAreaName(r.cityName + r.districtName)
        } else if (r.cityAreaCode && !r.districtAreaCode) {
          this.projectDeclare.areaCode = r.cityAreaCode
          this.parentDisabled = true
          this.getAreaName(r.cityName)
        } else {
          this.projectDeclare.areaCode = ''
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.version = row.version
      const deptId = row.deptId || this.ids[0];
      this.deptId = deptId;
      getProjectDeclare(deptId).then(r => {
        this.projectDeclare = r.projectDeclare;
        if (this.projectDeclare.disposalMethodJson == '' || this.projectDeclare.disposalMethodJson == null) {
          this.projectDeclare.disposalMethodJson = [{
            method: 'paiMai',
            amount: '',
            selected: false
          },
          {
            method: 'shiZheng',
            amount: '',
            selected: false
          },
          {
            method: 'qiTa',
            amount: '',
            selected: false,
            remark: ''
          }
          ]
        }
        this.projectDeclare.reverLength = this.projectDeclare.reverLength.toFixed(3)
        this.open = true;
        this.mode = 'update';
        this.title = "修改项目信息申报";
        this.handelFileList(r)
        this.handleClick()
      });
    },
    /** 预览附件操作 */
    handleView(row) {
      const deptId = row.deptId || this.deptIds[0];
      this.deptId = deptId;
      this.title = "查看项目详情";
      getProjectDeclare(deptId).then(r => {
        this.projectDeclare = r.projectDeclare;
        this.projectDeclare.reportedFlag=r.projectDeclare.reportedFlag=='1'?true:false;
        if (!this.projectDeclare.disposalMethodJson) {
          this.projectDeclare.disposalMethodJson = [{
            method: 'paiMai',
            amount: '',
            selected: false
          },
          {
            method: 'shiZheng',
            amount: '',
            selected: false
          },
          {
            method: 'qiTa',
            amount: '',
            selected: false,
            remark: ''
          }
          ]
        }
        this.projectDeclare.reverLength = this.projectDeclare.reverLength.toFixed(3)
        this.open = true;
        this.mode = 'view';
        this.handelFileList(r)
        this.handleClick()
      });
    },
    handelFileList(r) {
      if (r.projectDeclare.fileList && r.projectDeclare.fileList.length > 0) {
        this.imgList = r.projectDeclare.fileList.map(item => {
          return {
            name: item.uploadFileName,
            url: item.uploadFilePath,
            uploadFile: item,
          }
        });
      } else {
        this.imgList = [];
      }
      if (r.projectDeclare.parentImgList && r.projectDeclare.parentImgList.length > 0) {
        this.parentImgList = r.projectDeclare.parentImgList.map(item => {
          return {
            name: item.uploadFileName,
            url: item.uploadFilePath,
            uploadFile: item,
          }
        });
      } else {
        this.parentImgList = [];
      }
    },
    /** 提交按钮 */
    submitForm() {
      console.log(22222)
      console.log(this.projectDeclare)
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          this.projectDeclare.reportedFlag=this.projectDeclare.reportedFlag==false?'0':'1';
          this.projectDeclare.version = this.version
          if (this.projectDeclare.type == 'abandonSand') {
            this.projectDeclare.investment = this.investmentTotal
          }
          if (this.projectDeclare.deptId) {
            updateProjectDeclare(this.projectDeclare).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addProjectDeclare(this.projectDeclare).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        } else {
          console.log(errorObj)
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that = this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function () {
        if (row.deptId) {
          return delProjectDeclare(row.deptId);
        } else {
          return delProjectDeclareBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 项目位置change事件 获取areaName */
    async getAreaName(areaName) {
      console.log(areaName)
      const res = await getDeptTreeData()
      this.projectDeclare.areaName = areaName;
      if (this.mode === 'add') {
        if (this.projectDeclare.areaCode.length >= 15) {
          const deptObj = res.deptList.find(item => {
            return item.areaCode === this.projectDeclare.areaCode;
          });
          console.log(deptObj)
          this.projectDeclare.parentId = deptObj ? deptObj.deptId : '';
        } else {
          this.projectDeclare.parentId = '';
        }
      }
    },
    /** 获取fileList */
    getFileList(fileList) {
      this.projectDeclare.uploadFileList = fileList;
      this.$nextTick(() => {
        this.$refs['form'].validateField("uploadFileList")
      })
    },
    getFileList1(fileList) {
      this.projectDeclare.fileList = fileList
      this.$nextTick(() => {
        this.$refs['form'].validateField("imgList")
      })
    },
    getFileList2(fileList) {
      this.projectDeclare.parentImgList = fileList
      this.$nextTick(() => {
        this.$refs['form'].validateField("imgList2")
      })
    },
    getFileList8(fileList) {
      this.projectDeclare.paiMaiFileList = fileList;
      this.$nextTick(() => {
        this.$refs['form'].validateField("paiMaiFileList")
      })
    },
    /** 查看附件列表 */
    handleFileList(row) {
      this.deptId = row.deptId;
      this.openFileList = true;
    },

    /** 下载附件操作 */
    handleDownloadFile(row) {
      this.download("/com/uploadfile/download/" + row.id, {}, row.uploadFileName);
    },

    /** 删除附件操作 */
    handleDeleteFile(row) {
      this.$modal.confirm('是否确认删除此附件？').then(() => {
        return delProjectFile(row.id);
      }).then(() => {
        this.$refs.fileTable.search();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },


  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.projectDeclareTable.changeTableHeight();
  },
};
</script>

<style lang="scss" scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,
td {
  border: 1px solid rgb(238, 231, 237);
  padding: 5px;
}

::v-deep .asterisk .el-form-item__label:before {
  content: "*" !important;
  color: #ff4949 !important;
  margin-right: 4px !important;
}
</style>
