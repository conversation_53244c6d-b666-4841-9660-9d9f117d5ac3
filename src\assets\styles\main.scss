/* 菜单 */
.el-submenu .el-menu-item {
  height: 56px;
  line-height: 56px;
  padding: 0 10px;
}
//.nest-menu .el-menu-item {
//  padding-left: 30px!important;
//}
.el-submenu__icon-arrow {
  margin-top: -5px;
}

/* 右侧工具栏 */
.rightToolbar {
  margin-right: 5px;
}

/* 表格-操作栏 */
.btn-table-operate {
  padding: 5px 6px;
  font-size: 12px;
  border-radius: 3px;
}

/* 表格-状态 */
.table-label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
.label-success {
  background-color:#468847
}
.label-danger {
  background-color: #d9534f;
}

/* 输入框-禁用 */
.el-input.is-disabled .el-input__inner {
  background-color: #ffffff;
  color: #606266;
}

/* 加减号-禁用 */
.el-input-number__increase, .el-input-number__decrease {
  background: #ffffff;
}

/* 单选框-禁用 */
.el-radio__input.is-disabled.is-checked .el-radio__inner {
  border-color: #1890ff;
  background: #1890ff;
}
.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #ffffff;
}
.el-radio__input.is-disabled + span.el-radio__label {
  color: #606266;
}
.el-radio__input.is-checked + .el-radio__label {
  color: #606266;
}

/* 弹窗-dialog */
.el-dialog__header {
  padding-bottom: 0;
}
.el-dialog__body {
  padding: 20px 20px;
}
.el-dialog__footer {
  padding-top: 0;
}

/* 弹窗-消息提示 */
.el-message-box__message p {
  margin: 0;
  line-height: 24px;
  word-break: break-all;
  white-space: normal;
  word-wrap: break-word;
}

/* Treeselect 单选 */
.myTreeSelect.el-form-item--small .el-form-item__content {
  line-height: 30px;
}
.myTreeSelect .vue-treeselect__control {
  height: 30px;
}
.myTreeSelect .vue-treeselect__placeholder, .vue-treeselect__single-value {
  line-height: 31px;
}
.myTreeSelect .vue-treeselect__placeholder {
  color: #C0C4CC;
  font-size: 13px;
}
.myTreeSelect .vue-treeselect__single-value {
  color: #606266!important;
  font-size: 12.5px;
}
.vue-treeselect__control {
  border-radius: 4px!important;
  padding-left: 10px!important;
  border: 1px solid #DCDFE6!important;
}
.vue-treeselect__single-value {
  color: #606266!important;
}
.vue-treeselect--open .vue-treeselect__control {
  border-color: #1890ff!important;
}
.vue-treeselect--focused:not(.vue-treeselect--open) .vue-treeselect__control {
  border-color: #1890ff!important;
  box-shadow: none!important;
}
.vue-treeselect__x-container:hover {
  color: #1890ff!important;
}
.vue-treeselect--has-value .vue-treeselect__input {
  vertical-align: middle!important;
}

/* Treeselect 多选 */
.myTreeSelectMulti.vue-treeselect--multi .vue-treeselect__input {
  padding-top: 0;
  padding-bottom: 5px;
}
.myTreeSelectMulti .vue-treeselect__placeholder, .vue-treeselect__single-value {
  line-height: 36px;
}
.myTreeSelectMulti.el-form-item--medium .el-form-item__content {
  line-height: 18px;
}

.text-green{
  color:#00a65a !important
}

.text-bold{
  font-weight: 700
}

.el-table-max-cell2 .cell{
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;/*省略几行写几*/
  -webkit-box-orient: vertical;
  max-height: 68px;
}

.el-table-max-cell2 .cell .error-label{
  font-size: 12px;
}

.el-table-max-cell3 .cell{
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;/*省略几行写几*/
  -webkit-box-orient: vertical;
  max-height: 68px;
}

.el-table-max-cell3 .cell .error-label{
  font-size: 12px;
}
