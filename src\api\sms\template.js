import request from '@/utils/request'

// 查询短信模板表 详细
export function getTemplate(id) {
  return request({
    url: '/sms/template/info/' + id,
    method: 'post'
  })
}

// 新增短信模板表
export function addTemplate(data) {
  return request({
    url: '/sms/template/save',
    method: 'post',
    data: data
  })
}

// 修改短信模板表
export function updateTemplate(data) {
  return request({
    url: '/sms/template/update',
    method: 'post',
    data: data
  })
}

// 删除短信模板表
export function delTemplate(id) {
  return request({
    url: '/sms/template/delete/' + id,
    method: 'post'
  })
}

// 批量删除短信模板表
export function delTemplateBatch(ids) {
  return request({
    url: '/sms/template/delete',
    data: ids,
    method: 'post'
  });
}


