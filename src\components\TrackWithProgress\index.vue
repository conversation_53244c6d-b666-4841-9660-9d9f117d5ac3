<template>
  <Map-dialog :visible.sync="visible" top="10vh" :title="this.carPlateNum +'-行车轨迹'"
              width="70%"
    @opened="onOpen" append-to-body
    @close="resetSlider">
    <div id="map2D_for_track" ref="mapContainer" />
		<div class="progress">
			<el-slider v-model="percentage" :format-tooltip="formatTooltip"
			@input="onChange"></el-slider>
		</div>
    <div slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </div>
  </Map-dialog>
</template>

<script>

import L from "leaflet";
import "leaflet/dist/leaflet.css";
import K from "./toolkit_leaflet.js";
import { parseTime } from "./ruoyi.js";

let colorAhead = '#00aeff';
let colorBehind = '#32bd3c';
let layerLineAhead = null;
let layerLineBehind = null;
let markerTruck = null;
let myList = [
  {time: 0, lng: 116.3, lat: 39.9, dir: 0},
];

export default {

  components:{
    L, K,
  },
  data() {
    return {
      carPlateNum:'',
      visible: false,
			percentage: 0,
			infoCar: {},
			start: 0,
			resList: [],
    }
  },
  methods: {
		formatTooltip(val) {
			let now = this.start + 24*36 * 1000 * val;
			return parseTime(new Date(now), "{y}-{m}-{d} {h}:{i}:{s}");
		},
		onChange(val) {
			this.updateMap(val / 100);
		},
		onOpen() {
			if (K.viewer) {
			  K.viewer.remove()
			}
			// this.start = new Date().getTime() - 3600 * 1000;
			this.start = new Date(Date.now() - 24*3600 * 1000).getTime(); //开始时间 当前时刻减去一小时---1小时前
			this.init();
			this.listen();
			this.rearrangeData();
			this.updateMap(0);
      if (this.resList.length == 0) {
        return;
      }
			this.resetView();
		},
    resetSlider(){
      this.percentage = 0;
    },
    init() {
			K.initMap("map2D_for_track");
			K.initWatermark();
			// let layerImg = K.createLayerWmtsTdt("img", 1);
			//layerImg.setOpacity(0.5);
			// K.switchLayer(layerImg, true);
			let layerVec = K.createLayerWmtsTdt("vec", 0);
			// layerVec.setOpacity(0.5);
			K.switchLayer(layerVec, true);
			let layerCia = K.createLayerWmtsTdt("cia", 2);
			K.switchLayer(layerCia, true);
			layerLineAhead = L.polyline([], {color: colorAhead, weight: 8, interactive: false,});
			K.switchLayer(layerLineAhead, true);
			layerLineBehind = L.polyline([], {color: colorBehind, weight: 8, interactive: false,});
			K.switchLayer(layerLineBehind, true);
			markerTruck = L.marker([0, 0], {icon: L.icon({iconUrl: require("@/assets/onemap/images/car5.png"), iconAnchor: [16, 39],}), interactive: false,});
			K.switchLayer(markerTruck, true);
    },
    listen(){
			let that = this;
			K.viewer.on("zoomend", function() {
				console.log(3333)
				that.rotateMarker(markerTruck, markerTruck.deg);
			});
    },
		rearrangeData() {
			myList = [];
			let isHeadAdded = false;
      console.log("this.resList:*************", this.resList)
      if(this.resList.length == 0){
        return;
      }
			for (let i = 0; i < this.resList.length; i++) {
				let info = this.resList[i];
				let t = new Date(info.tGpsStr).getTime();
				if (t < this.start) {
					continue;
				}
				if (t > this.start + 24*3600 * 1000) {
					break;
				}
				if (!isHeadAdded) {
					myList.push({
						time: this.start,
						lng: info.lgtd,
						lat: info.lttd,
						dir: info.direction,
					});
					isHeadAdded = true;
				}
				myList.push({
					time: t,
					lng: info.lgtd,
					lat: info.lttd,
					dir: info.direction,
				});
			}
			myList.push({
        time: this.start + 24 * 3600 * 1000 + 1000,
				lng: this.resList[this.resList.length - 1].lgtd,
				lat: this.resList[this.resList.length - 1].lttd,
				dir: this.resList[this.resList.length - 1].direction,
			});
		},
		rotateMarker(marker, deg) {
			let iconElement = marker.getElement();
      if (iconElement) {
        let str = iconElement.style.transform;
        let ss = str.split(' rotate(');
        iconElement.style.transform = ss[0] + ' rotate(' + deg + 'deg)'; // 旋转标记
        iconElement.style.transformOrigin = '50% 50%';
      }
		},
		updateMap(val){
			if (!markerTruck) {
				return;
			}
      console.log("myList.length__________",myList.length)
      if(myList.length == 0){
        return;
      }
			let llMarker = [];
			let i = 0;
			let now = this.start + 24*3600 * 1000 * val;
			for (i = 0; i < myList.length - 1; i++) {
				let tCurrent = myList[i].time;
				let tNext = myList[i + 1].time;
				if (now >= tCurrent && now < tNext) {
					let f = (now - tCurrent) / (tNext - tCurrent);
					let dx = myList[i + 1].lng - myList[i].lng;
					let dy = myList[i + 1].lat - myList[i].lat;
					llMarker = [
						f * dy + myList[i].lat,
						f * dx + myList[i].lng
					];
					markerTruck.setLatLng(llMarker);
					markerTruck.deg = myList[i].dir;
					this.rotateMarker(markerTruck, markerTruck.deg);
					break;
				}
			}
      if (llMarker.length == 0 && myList.length == 1) {
        console.log("myList.length == 1",myList[0]);
        //车辆离线的情况，后台构造了一个点的数据。
        llMarker = [myList[0].lat, myList[0].lng];
        // markerTruck.setLatLng(llMarker);
        let markerTruckTemp=L.marker(llMarker, {icon: L.icon({iconUrl: require("@/assets/onemap/images/car5_ontline.png"), iconAnchor: [16, 39],}), interactive: false,});
        markerTruckTemp.deg = myList[0].dir;
        K.switchLayer(markerTruckTemp, true);
        this.rotateMarker(markerTruckTemp, markerTruckTemp.deg);
      }
			console.log(3333, val, i, myList[i].dir)
      let llsAhead = [llMarker];
			let llsBehind = [];
			for (let j = 0; j < myList.length; j++) {
				if (j <= i) {
					llsBehind.push([myList[j].lat, myList[j].lng])
				} else {
					llsAhead.push([myList[j].lat, myList[j].lng])
				}
			}
			llsBehind.push(llMarker);
			layerLineAhead.setLatLngs(llsAhead);
			layerLineBehind.setLatLngs(llsBehind);
		},
    resetView(){
      if(Object.keys(layerLineAhead.getBounds()).length!=0){
        K.viewer.flyToBounds(layerLineAhead.getBounds().pad(0.5), {
          animate: true,
          duration: 1.5,
        });
      }else{
        K.viewer.flyToBounds(layerLineBehind.getBounds().pad(0.5), {
          animate: true,
          duration: 1.5,
        });
      }

    },
  }
}
</script>

<style scoped>
  #map2D_for_track {
  	width: 100%;
  	height: 60vh;
  	background-color: #cacdd0;
    cursor: pointer;
  }
	.progress{
		width: 60%;
		height: 44px;
		position: absolute;
		bottom: 60px;
		left: 20%;
		z-index: 1000;
		background: #020F2280;
		border-radius: 10px 10px 10px 10px;
		border: 2px solid #028DD6;
		text-align: center;
	}
</style>
