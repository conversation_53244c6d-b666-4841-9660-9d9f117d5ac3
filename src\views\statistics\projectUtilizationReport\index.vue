
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="月份" prop="month">
        <el-date-picker
          v-model="queryParams.month"
          type="month"
          :clearable="false"
          value-format="yyyy-MM"
          placeholder="选择月"
          @change="handleChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>

        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>

        <el-button  v-if="((queryParams.userDeptCodeLength=='14')&&userDeptCode.length!='9'&&userDeptCode.length!='14')&&cityName!='市本级'" icon="el-icon-back" size="mini" @click="toBack">返回</el-button>

        <el-button v-if="queryParams.userDeptCodeLength=='19'&&userDeptCode.length!='14'||cityName=='市本级'"  icon="el-icon-back" size="mini" @click="toBackCity">返回</el-button>

      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload()"></right-toolbar>
    </el-row>

    <my-table
      v-show="showTable"
      v-loading="loading"
      url="/project/statistics/getProjectPageByCity"
      ref="projectTable"
      row-key="deptCode"
      :fixed="true"
      :multiselect="false"
      :show-radio="false"
      cell-class-name="el-table-max-cell2"
      :auto-request="false"
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column
        :label="queryParams.userDeptCodeLength=='14'?'区县名称':'地市名称'"
        align="center"
        header-align="center"
        min-width="160"
        prop="cityName"
        sortable="custom"
        column-key="deptCode">
        <template #default="scope">
          <el-link type="primary" title="查看详情" @click="openDetails(scope.row)">
            {{ scope.row.cityName }}
          </el-link>
<!--           <span title="查看详情" style="color: #409EFF;cursor: pointer" @click="openDetails(scope.row)">-->
<!--            {{ scope.row.cityName }}-->
<!--          </span>-->
        </template>
      </el-table-column>
      <el-table-column
        label="控制总量(吨)"
        align="right"
        header-align="center"
        min-width="130"
        sortable="custom"
        prop="totalYield"
        column-key="totalYield">
        <template #default="scope">
          {{common.toThousands(scope.row.totalYield, 2, ',')}}
        </template>
      </el-table-column>
      <el-table-column
        label="已采总量(吨)"
        align="right"
        header-align="center"
        min-width="130"
        prop="mined"
        sortable="custom"
        column-key="mined">
        <template #default="scope">
          {{common.toThousands(scope.row.mined, 2, ',')}}
        </template>
      </el-table-column>
    </my-table>

    <my-table
      v-show="!showTable"
      url="/project/statistics/getProjectDetailsPage"
      ref="projectTable1"
      row-key="deptCode"
      :fixed="true"
      :multiselect="false"
      :show-radio="false"
      cell-class-name="el-table-max-cell2"
      :auto-request="false"
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column
        label="项目名称"
        align="left"
        header-align="center"
        min-width="80"
        prop="name"
        sortable="custom"
        column-key="deptCode"
      >
      </el-table-column>
      <el-table-column
        label="项目类型"
        align="center"
        header-align="center"
        min-width="80"
        prop="type"
        sortable="custom"
        column-key="type"
      >
        <template slot-scope="scope">
          <my-view pvalue="projectType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column
        label="控制总量(吨)"
        align="right"
        header-align="center"
        min-width="130"
        sortable="custom"
        prop="totalYield"
        column-key="totalYield">
        <template #default="scope">
          {{common.toThousands(scope.row.totalYield, 2, ',')}}
        </template>
      </el-table-column>
      <el-table-column
        label="已采总量(吨)"
        align="right"
        header-align="center"
        min-width="130"
        prop="mined"
        sortable="custom"
        column-key="mined">
        <template #default="scope">
          {{common.toThousands(scope.row.mined, 2, ',')}}
        </template>
      </el-table-column>
    </my-table>
  </div>
</template>

<script>
import Template from '@/views/sms/template/index.vue'
import { getProjectTotalList, getUser } from '@/api/statistics/projectStatistics'
import common from '@/utils/common'

export default {
  name: 'ProjectUtilizationReport',
  computed: {
    common() {
      return common
    }
  },
  components: { Template },
  data() {
    return {
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      queryParams: {
        month: common.formatDate(new Date(), "yyyy-MM"),
        userDeptCodeLength:9,
        userDeptCode:'',
        isMunicipalityLevel:false,

      },
      project:{},
      userDeptCode:'',
      showTable:true,
      totalList:[],
      cityName:''
    }
  },
  created() {
    this.getUserDeptCode()
  },
  methods: {
    handleChange(){
      if(this.showTable && this.$refs.projectTable){
        this.$refs.projectTable.search(this.queryParams);
      }
      if(!this.showTable && this.$refs.projectTable1){
        this.$refs.projectTable1.search(this.queryParams);
      }
      this.getProjectTotal(this.queryParams)
    },
    //获取项目汇总信息
    async getProjectTotal(queryParams){
      const r = await getProjectTotalList(queryParams)
      this.totalList = r.list
    },
    // 计算合计
    getSummaries(params) {
      let totalYieldTotal;
      let minedTotal;
      let showTotal = this.showTable;
      if(this.totalList[0]){
         totalYieldTotal = this.totalList[0].totalYieldTotal.toFixed(2)
         minedTotal = this.totalList[0].minedTotal.toFixed(2)
      }
      const { columns } = params;
      const sums = [];
      columns.forEach((column, index) => {
          if (index === 0) {
            if(showTotal){
              sums[0] = '总计';
              sums[2] = common.toThousands(totalYieldTotal, 2, ',')
              sums[3] =  common.toThousands(minedTotal, 2, ',')
            }else {
              sums[0] = '总计';
              sums[3] = common.toThousands(totalYieldTotal, 2, ',')
              sums[4] = common.toThousands(minedTotal, 2, ',')
            }
            return;
          }

      });
      return sums;
    },
    //获取用户deptCode
    async getUserDeptCode(){
      const r = await getUser()
      this.queryParams.userDeptCode = r.userDeptCode
      this.userDeptCode = r.userDeptCode
      if(this.queryParams.userDeptCode.length<9){
        this.queryParams.userDeptCodeLength = 9
        this.$refs.projectTable.search(this.queryParams);
      }else if (this.userDeptCode.length == 9){
        this.queryParams.userDeptCodeLength = 14
        this.$refs.projectTable.search(this.queryParams);
      }else if(this.userDeptCode.length == 14){
        this.queryParams.userDeptCodeLength = 14
        this.showTable = false
        this.$refs.projectTable1.search(this.queryParams);
      }else {
        this.showTable = false
        this.$refs.projectTable1.search(this.queryParams);
      }
      await this.getProjectTotal(this.queryParams)
    },
    //从县区列表返回上一级
    toBack(){
      this.queryParams.userDeptCode = this.userDeptCode
      this.queryParams.sidx = ''
      this.queryParams.order = ''
      this.queryParams.userDeptCodeLength = 9
      this.getProjectTotal(this.queryParams)
      this.$refs.projectTable.search(this.queryParams);
    },
    //从项目列表返回上一级
    toBackCity(){
      if(this.userDeptCode.length<=9){
        this.queryParams.userDeptCode = this.queryParams.userDeptCode.substring(0,9)
      }else {
        this.queryParams.userDeptCode = this.userDeptCode
      }
      this.queryParams.sidx = ''
      this.queryParams.order = ''
      this.queryParams.userDeptCodeLength = 14
      this.showTable = true
      this.queryParams.isMunicipalityLevel = false
      this.getProjectTotal(this.queryParams)
      this.$refs.projectTable.search(this.queryParams);
      this.cityName = ''
    },
    //点击城市名称
    openDetails(row){
      if(row.cityName=='市本级'){
        this.queryParams.isMunicipalityLevel = true
      }else {
        this.queryParams.isMunicipalityLevel = false
      }
      this.cityName = row.cityName
      this.queryParams.userDeptCode = row.deptCode
      if(row.deptCode.length == 9&&row.cityName!='市本级'){
        this.queryParams.userDeptCodeLength = 14
        this.$refs.projectTable.search(this.queryParams);
      }
      if(row.deptCode.length == 14||row.cityName=='市本级'){
        this.queryParams.userDeptCodeLength = row.deptCode.length+5
        this.showTable = false
        this.loading = true;
        this.$refs.projectTable1.search(this.queryParams);
        this.loading = false;
      }
      this.getProjectTotal(this.queryParams)
    },
    // 表单重置
    reset() {
      this.project = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
      };
      this.resetForm("form");
    },
    reload(restart) {
      this.getProjectTotal(this.queryParams)
      if(this.showTable){
        this.$refs.projectTable.search(this.queryParams, restart);
      }else {
        this.$refs.projectTable1.search(this.queryParams, restart);
      }
      this.single=true;
      this.multiple = true;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.projectTable &&this.$refs.projectTable.changeTableHeight();
    this.$refs.projectTable1 &&this.$refs.projectTable1.changeTableHeight();
  },
}
</script>

<style scoped>

</style>
