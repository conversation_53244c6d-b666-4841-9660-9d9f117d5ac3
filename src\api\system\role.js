import request from '@/utils/request'

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/sys/role/list',
    method: 'post',
    params: query
  })
}
//查询角色列表不分页
export function getRoleListData() {
  return request({
    url: '/sys/role/select',
    method: 'post',
  })
}
// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/sys/role/info/' + roleId,
    method: 'post'
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/sys/role/save',
    method: 'post',
    data: data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/sys/role/update',
    method: 'post',
    data: data
  })
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/sys/role/delete/' + roleId,
    method: 'post'
  })
}
// 批量删除角色
export function batchDelRole(roleIds) {
  return request({
    url: '/sys/role/delete',
    method: 'post',
    data:roleIds
  })
}

// 选择用户弹窗选中角色回显
export function saveUserIds(roleId,data){
  return request({
    url:'/sys/role/saveUserIds/' + roleId,
    method: 'post',
    data:data
  })
}
// 选择用户弹窗 确定操作
export function userIdsByRole(roleId){
  return request({
    url:'/sys/role/userIdsByRole/' + roleId,
    method: 'post',
  })
}
// 查询部门列表
export function listDept(query) {
  return request({
    url: '/sys/dept/list',
    method: 'post',
    params: query
  })
}
// 根据部门id获取角色列表
export function listRoleByDeptId(deptId){
  return request({
    url:'/sys/role/getRoleListByDeptId/' + deptId,
    method: 'post',
  })
}
// 获取当前用户有权查看的角色列表
export function getAuthViewRoleList() {
  return request({
    url:'/sys/role/authViewList',
    method: 'post',
  })
}


