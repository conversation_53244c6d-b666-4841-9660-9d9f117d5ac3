import request from '@/utils/request'

// 查询项目信息申报表详细
export function getProjectDeclare(deptId) {
  return request({
    url: '/project/projectDeclare/info/' + deptId,
    method: 'post'
  })
}

// 新增项目信息申报表
export function addProjectDeclare(data) {
  return request({
    url: '/project/projectDeclare/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改项目信息申报表
export function updateProjectDeclare(data) {
  return request({
    url: '/project/projectDeclare/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除项目信息申报表
export function delProjectDeclare(deptId) {
  return request({
    url: '/project/projectDeclare/delete/' + deptId,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除项目信息申报表
export function delProjectDeclareBatch(deptIds) {
  return request({
    url: '/project/projectDeclare/delete',
    data: deptIds,
    method: 'post',
    showLoading: true,
  });
}

//审核
export function checkProjectDeclare(data) {
  return request({
    url: '/project/projectDeclare/audit',
    method: 'post',
    data: data,
    showLoading: true,
  })
}


