<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <my-input style="width: 205px" v-model.trim="queryParams.projectName" placeholder="项目名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input style="width: 205px" v-model.trim="queryParams.sectionName" placeholder="标段名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="磅站名称" prop="stationName">
        <my-input style="width: 205px" v-model.trim="queryParams.stationName" placeholder="磅站名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="在线状态" prop="heartbeatTime">
        <my-select v-model="queryParams.heartbeatTime" placeholder="请选择在线状态">
          <el-option v-for="(item,index) of heartbeatTimeList" :key="index" :label="item.value"
            :value="item.key">{{item.value}}</el-option>
        </my-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['client:client:save']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['client:client:update']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['client:client:delete']">删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/client/client/waterPage" ref="clientTable" row-key="id" :fixed="true"
      @my-selection-change="handleSelectionChange">
      <el-table-column header-align="center" fixed="left" label="项目名称" min-width="250" align="left" prop="projectName"
        sortable="custom" column-key="client.PROJECT_NAME">
      </el-table-column>
      <el-table-column label="标段名称" align="left" header-align="center" min-width="160" prop="sectionName"
        sortable="custom" column-key="client.SECTION_NAME">
      </el-table-column>
      <el-table-column header-align="center" label="磅站名称" align="left" min-width="150" prop="stationName"
        sortable="custom" column-key="client.STATION_NAME">
      </el-table-column>
      <el-table-column header-align="center" label="在线状态" align="center" min-width="100" prop="heartbeatTime"
        sortable="custom" column-key="client.HEARTBEAT_TIME">
        <template #default="scope">
          <el-tag v-if="getTime(scope.row.heartbeatTime)==1" effect="dark" type="success">在线</el-tag>
          <el-tag v-else-if="getTime(scope.row.heartbeatTime)==2" effect="dark" type="danger">离线</el-tag>
          <el-tag v-else effect="dark" type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="客户端编号" align="center" min-width="150" prop="sn" sortable="custom"
        column-key="client.SN">
      </el-table-column>
      <!--      <el-table-column-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="客户端密码"-->
      <!--        min-width="150"-->
      <!--        prop="pwd"-->
      <!--        sortable="custom"-->
      <!--        column-key="client.PWD">-->
      <!--      </el-table-column>-->
      <el-table-column header-align="center" align="center" label="mac地址" min-width="120" prop="mac" sortable="custom"
        column-key="client.MAC">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="ip地址" min-width="120" prop="ip" sortable="custom"
        column-key="client.IP">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="串口号" min-width="120" prop="com" sortable="custom"
        column-key="client.COM">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="波特率" min-width="120" prop="baudRate"
        sortable="custom" column-key="client.BAUD_RATE">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="数据位" min-width="120" prop="dataBit" sortable="custom"
        column-key="client.DATA_BIT">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="校验位" min-width="120" prop="verifyBit"
        sortable="custom" column-key="client.VERIFY_BIT">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="停止位" min-width="120" prop="stopBit" sortable="custom"
        column-key="client.STOP_BIT">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="单位" min-width="120" prop="unit" sortable="custom"
        column-key="client.UNIT">
        <template #default="scope">
          <my-view pvalue="unit" :value="scope.row.unit"></my-view>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="是否流控" min-width="120" prop="flowControl"
        sortable="custom" column-key="client.FLOW_CONTROL">
        <template #default="scope">
          <span v-if="scope.row.flowControl">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="终端类型" min-width="120" prop="type" sortable="custom"
        column-key="client.TYPE">
        <template #default="scope">
          <my-view pvalue="terminalType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="版本号" min-width="120" prop="programVersion"
        sortable="custom" column-key="client.PROGRAM_VERSION">
      </el-table-column>
      <!--      <el-table-column
        header-align="center"
        align="center"
        label="是否严格验证车牌号"
        min-width="120"
        prop="applyBeforeSubmit"
        sortable="custom"
        column-key="client.APPLY_BEFORE_SUBMIT">
        <template #default="scope">
          <span v-if="scope.row.applyBeforeSubmit">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>-->
      <el-table-column header-align="center" align="center" label="有效期（起）" min-width="150" prop="startTime"
        sortable="custom" column-key="client.START_TIME">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="有效期（止）" min-width="150" prop="endTime"
        sortable="custom" column-key="client.END_TIME">
      </el-table-column>
      <el-table-column header-align="center" align="center" label="状态" min-width="120" prop="status" sortable="custom"
        column-key="client.STATUS">
        <template #default="scope">
          <my-view pvalue="terminalStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" header-align="center" min-width="150" prop="createTime"
        sortable="custom" column-key="client.CREATE_TIME">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" header-align="center" label="修改时间" min-width="150" prop="updateTime"
        sortable="custom" column-key="client.UPDATE_TIME">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column width="180" label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button size="small" type="danger" class="btn-table-operate"
            @click="handleToexamine(scope.row)">审核</el-button>
          <el-button size="small" type="success" class="btn-table-operate"
            @click="handleStart(scope.row)">抬杆</el-button>
          <el-button size="small" type="warning" class="btn-table-operate"
            @click="downAut(scope.row.id,'Drop')">落杆</el-button>
        </template>
      </el-table-column>
    </my-table>
    <el-dialog title="现场抬杆" :visible.sync="dialogVisible1" width="300px">
      <el-form :model="appealForm1" label-width="100px">
        <!-- <el-form-item label="抬杆时间">
          <el-input v-model="inputValue" placeholder="请输入分钟数(0-1440)" @input="handleInput"
            @blur="handleBlur"></el-input>
        </el-form-item> -->

        <!-- <el-form-item label="抬杆原因">
          <el-input type="textarea" :rows="4" v-model="appealForm1.reason" placeholder="请输入详细申诉原因">
          </el-input>
        </el-form-item> -->
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleApprove1(null,'Raise')" type="primary">确认抬杆</el-button>
        <el-button @click="closeTai">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog title="申诉处理" :visible.sync="dialogVisible" width="800px">
        <el-table :data="tableData" border style="width: 100%" height="450">
          <el-table-column fixed prop="carNumber" align="center" label="车牌号" width="150">
          </el-table-column>
          <el-table-column fixed prop="causeAppeal" align="center" label="申诉原因" width="200">
          </el-table-column>
          <el-table-column prop="createTime" align="center" label="创建时间" width="180">
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="confirmAction(scope.row.id, 'auditPass')" type="primary"
                v-if="scope.row.status!='auditPass'&&scope.row.status!='auditReject'">通过</el-button>
              <el-button @click="confirmAction(scope.row.id,'auditReject')" type="danger"
                v-if="scope.row.status!='auditPass'&&scope.row.status!='auditReject'">驳回</el-button>
            </template>
          </el-table-column>
        </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <!-- 添加或修改客户端信息表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" height="70vh" append-to-body>
      <el-form ref="form" :model="client" label-width="120px">
        <el-row>
          <el-col :span="12">
            <my-form-item label="项目名称" ref="projectId" prop="projectId"
              :rules="[{notNull:true,message:'请选择项目', trigger:['blur','change']}]">
              <my-select v-model="client.projectId" :options="projectList" placeholder="请选择项目"
                @change="projectSelectChange">
                <el-option v-for="item in projectList" :key="item.value" :value="item.value"
                  :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"></el-option>
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="磅站名称" ref="stationId" prop="stationId"
              :rules="[{notNull:true,message:'请选择磅站', trigger:['blur','change'] }]">
              <my-select v-model="client.stationId" :options="stationList" placeholder="请选择磅站"
                @change="stationSelectChange"></my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="mac地址" ref="mac" prop="mac"
              :rules="[{notNull:true,message:'请输入mac地址', trigger:['blur','change']}]">
              <my-input v-model="client.mac" placeholder="请输入mac地址" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="ip地址" ref="ip" prop="ip">
              <my-input v-model="client.ip" placeholder="请输入ip地址" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="串口号" ref="com" prop="com">
              <my-input v-model="client.com" placeholder="请输入串口号" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="波特率" ref="baudRate" prop="baudRate"
              :rules="[{isNumber:true, message:'只能是数字', trigger:['blur','change']}]">
              <my-input v-model="client.baudRate" placeholder="请输入波特率" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="数据位" ref="dataBit" prop="dataBit">
              <my-select pvalue="dataBit" v-model="client.dataBit" placeholder="请选择数据位" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="校验位" ref="verifyBit" prop="verifyBit">
              <my-select pvalue="checkBit" v-model="client.verifyBit" placeholder="请选择校验位" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="停止位" ref="stopBit" prop="stopBit">
              <my-select pvalue="stopBit" v-model="client.stopBit" placeholder="请选择停止位" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="单位" ref="unit" prop="unit"
              :rules="[{notNull:true,message:'请选择单位', trigger:['blur','change']}]">
              <my-select pvalue="unit" v-model="client.unit" placeholder="请选择单位" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="是否流控" ref="flowControl" prop="flowControl"
              :rules="[{notNull:true,message:'请选择是否流控', trigger:['blur','change']}]">
              <el-radio-group v-model="client.flowControl">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="终端类型" ref="type" prop="type"
              :rules="[{notNull:true,message:'请选择终端类型', trigger:['blur','change']}]">
              <my-select pvalue="terminalType" v-model="client.type" placeholder="请选择终端类型" />
            </my-form-item>
          </el-col>
          <template v-if="client.type==='portable'">
            <el-col :span="12">
              <my-form-item label="数据起始地址" ref="startAddr" prop="startAddr"
                :rules="[{notNull:true,message:'请输入数据起始地址', trigger:['blur','change']},{isNumber:true, message:'只能是数字'}]">
                <my-input v-model="client.startAddr" placeholder="请输入数据起始地址" />
              </my-form-item>
            </el-col>

            <el-col :span="12">
              <my-form-item label="读取寄存器数" ref="readBits" prop="readBits"
                :rules="[{notNull:true,message:'请输入读取寄存器数', trigger:['blur','change']},{isNumber:true, message:'只能是数字'}]">
                <my-input v-model="client.readBits" placeholder="请输入读取寄存器数" />
              </my-form-item>
            </el-col>

          </template>
          <el-col :span="12">
            <my-form-item label="最小提交值（吨）" ref="minWeight" prop="minWeight"
              :rules="[{notNull:true,message:'请输入最小允许提交值', trigger:['blur','change']},{isNumber:true, message:'只能是数字'}]">
              <my-input v-model="client.minWeight" placeholder="请输入最小允许提交值（单位：吨）" />
            </my-form-item>
          </el-col>

          <el-col :span="12">
            <my-form-item label="数据类型阈值（吨）" ref="dataTypeThreshold" prop="dataTypeThreshold"
              :rules="[{notNull:true,message:'请输入数据类型阈值', trigger:['blur','change']},{isNumber:true, message:'只能是数字'}]">
              <my-input v-model="client.dataTypeThreshold" placeholder="请输入数据类型阈值（单位：吨）" />
            </my-form-item>
          </el-col>

          <el-col :span="12">
            <my-form-item label="有效期（起）" ref="startTime" prop="startTime"
              :rules="[{notNull:true,message:'请选择有效期（起）', trigger:['blur','change']}]">
              <el-date-picker v-model="client.startTime" type="datetime" style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss" placeholder="请输入有效期（起）">
              </el-date-picker>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="有效期（止）" ref="endTime" prop="endTime"
              :rules="[{notNull:true,message:'请选择有效期（止）', trigger:['blur','change']}]">
              <el-date-picker v-model="client.endTime" type="datetime" style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择有效期（止）">
              </el-date-picker>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="状态" ref="status" prop="status"
              :rules="[{notNull:true,message:'请选择状态', trigger:['blur','change']}]">
              <my-select pvalue="terminalStatus" v-model="client.status" placeholder="请选择状态" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="版本号" ref="programVersion" prop="programVersion"
              :rules="[{notNull:true,message:'请输入版本号', trigger:['blur','change']}]">
              <my-input v-model="client.programVersion" placeholder="请输入版本号" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="是否严格验证" ref="applyBeforeSubmit" prop="applyBeforeSubmit"
              :rules="[{notNull:true,message:'是否严格验证车牌号', trigger:['blur','change']}]">
              <el-radio-group v-model="client.applyBeforeSubmit">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
  import {
    getClient,
    delClient,
    delClientBatch,
    addClient,
    updateClient,
    listProjectNames,
    listStationByProjectId,
    goStart,
    getCar,
    clientAppeal
  } from "@/api/client/client";
  import common from '@/utils/common'
  import Template from '@/views/sms/template/index.vue'

  export default {
    name: "Client",
    components: {
      Template
    },
    data() {
      return {
        tableData: [],
        inputValue: '',
        dialogVisible: false,
        appealForm: {
          type: '',
          reason: ''
        },
        dialogVisible1: false,
        appealForm1: {
          type: '',
          reason: ''
        },
        sessionEdit: {},
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          projectName: '',
          stationName: '',
          heartbeatTime: ''
        },
        // 表单参数
        client: {},
        stationId: '',
        // 项目列表
        projectList: [],
        // 磅站列表
        stationList: [],
        dateTime: '', //系统时间
        //在线状态下拉列表
        heartbeatTimeList: [{
            key: "online",
            value: '在线'
          },
          {
            key: "offline",
            value: '离线'
          },
          {
            key: "unknown",
            value: '未知'
          }
        ]
      };
    },
    mounted() {
      this.dateTime = common.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss")
      listProjectNames().then(res => {
        if (res.list && res.list.length > 0) {
          this.projectList = res.list.map(item => {
            if (item.sectionName) {
              return {
                'name': item.name,
                'value': item.deptId,
                'sectionName': item.sectionName
              }
            } else {
              return {
                'name': item.name,
                'value': item.deptId
              }
            }
          });
        }
      })
    },
    methods: {
      confirmAction(id, actionType) {
        const actionText = actionType === 'auditPass' ? '通过' : '驳回';
        this.$confirm(`确定要${actionText}该申诉吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleApprove(id, actionType);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      },
      getCarNum(stationId) {
        getCar({
          stationId: stationId,
          pageNumber: 1,
          pageSize: 100
        }).then(res => {
          console.log(res, '获取的申诉列表')
          if (res.msg == 'success') {
            this.tableData = res.page.records;
          } else {
            this.$message.error('操作失败！');
          }
        });
      },
      closeTai() {
        this.dialogVisible1 = false;
        this.sessionEdit = {};
      },
      handleInput() {
        // 只允许输入数字，过滤其他字符
        this.inputValue = this.inputValue.replace(/[^\d]/g, '')
      },
      handleBlur() {
        // 限制最大值1440
        if (this.inputValue > 1440) {
          this.inputValue = 1440
        }
      },
      handleStart(row) {
        this.dialogVisible1 = true;
        this.sessionEdit = row;
        this.inputValue = '';
      },
      downAut(id, actionType) {
        const actionText1 = actionType === 'Drop' ? '落杆' : '抬杆';
        this.$confirm(`确定要${actionText1}吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleApprove1(id, actionType);
          // handleApprove1(scope.row.id,'Drop')
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      },
      handleApprove1(clientId, status) {
        goStart({
          clientId: clientId ? clientId : this.sessionEdit.id,
          status: status,
          time: this.inputValue
        }).then(res => {
          console.log(res)
          if (res.msg == 'success') {
            if (clientId) {
              this.$modal.msgSuccess("落杆成功！");
            } else {
              this.sessionEdit = {};
              this.$modal.msgSuccess("抬杆成功！");
              this.dialogVisible1 = false;
            }
          } else {
            this.$message.error('操作失败！');
          }
        });
      },
      handleToexamine(row) {
        console.log(row, '获取')
        this.getCarNum(row.stationId);
        this.stationId = row.stationId;
        this.dialogVisible = true;
      },
      handleApprove(id, status) {
        clientAppeal({
          id: id,
          status: status,
          rejectCause: ''
        }).then(res => {
          console.log(res)
          if (res.msg == 'success') {
            if (status == 'auditPass') {
              this.$modal.msgSuccess("已通过！");
              this.getCarNum(this.stationId);
            } else {
              this.sessionEdit = {};
              this.$modal.msgSuccess("已驳回！");
              // this.dialogVisible1 = false;
              this.getCarNum(this.stationId);
            }
          } else {
            this.$message.error('操作失败！');
          }
        });
      },
      handleReject() {
        this.dialogVisible = false
      },
      getTime(rowTime) {
        if (rowTime) {
          if ((Date.parse(this.dateTime) - Date.parse(rowTime)) / 1000 <= 70) {
            return 1
          } else {
            return 2
          }
        } else {
          return 0
        }

      },
      /** 查询客户端信息表列表 */
      reload(restart) {
        this.$refs.clientTable.search(this.queryParams, restart);
        this.single = true;
        this.multiple = true;
        this.dateTime = common.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss")
      },

      /** 取消按钮 */
      cancel() {
        this.open = false;
        this.reset();
      },

      /** 表单重置 */
      reset() {
        this.client = {
          id: '',
          projectId: '',
          projectName: '',
          stationId: '',
          stationName: '',
          sn: '',
          pwd: '',
          mac: '',
          ip: '',
          com: '',
          baudRate: null,
          dataBit: '',
          verifyBit: '',
          stopBit: '',
          flowControl: false,
          unit: '',
          type: '',
          startTime: '',
          endTime: '',
          status: 'use',
          minWeight: 1, //最小允许提交吨数
          startAddr: '', //数据起始地址
          readBits: '', //读取寄存器数
          dataTypeThreshold: 30, //进出场数据阈值
          programVersion: '', //程序版本
          applyBeforeSubmit: true //强制运砂申请
        };
        this.stationList = [];
        this.resetForm("form");
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.reload(true);
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /** 多选框选中数据 */
      handleSelectionChange() {
        this.ids = this.$refs.clientTable.getSelectRowKeys()
        this.single = this.ids.length !== 1
        this.multiple = !this.ids.length
      },

      /** 项目选择change事件 */
      projectSelectChange(val) {
        this.stationList = [];
        this.client.stationId = '';
        this.client.stationName = '';
        if (val) {
          const obj = this.projectList.find(item => {
            return item.value === val;
          });
          console.log(obj)
          this.client.projectName = obj.name ? obj.name : '';
          this.client.sectionName = obj.sectionName ? obj.sectionName : '';
          listStationByProjectId(val).then(res => {
            if (res.list && res.list.length > 0) {
              this.stationList = res.list.map(item => {
                return {
                  'name': item.name,
                  'value': item.id
                }
              });
            }
          });
        } else {
          this.client.projectName = '';
        }
      },

      /** 磅站选择change事件 */
      stationSelectChange(val) {
        if (val) {
          const obj = this.stationList.find(item => {
            return item.value === val;
          });
          this.client.stationName = obj.name ? obj.name : '';
        } else {
          this.client.stationName = '';
        }
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加客户端信息表";
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const id = row.id || this.ids[0];
        getClient(id).then(r => {
          this.client = r.client;
          this.open = true;
          this.title = "修改客户端信息表";
          listStationByProjectId(this.client.projectId).then(res => {
            if (res.list && res.list.length > 0) {
              this.stationList = res.list.map(item => {
                return {
                  'name': item.name,
                  'value': item.id
                }
              });
            }
          });
        });
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate((valid, errorObj) => {
          if (valid) {
            console.log(this.client)
            if (this.client.id) {
              updateClient(this.client).then(r => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.reload();
              });
            } else {
              addClient(this.client).then(r => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.reload();
              });
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        this.$modal.confirm('是否确认删除选中的数据吗？').then(() => {
          if (row.id) {
            return delClient(row.id);
          } else {
            return delClientBatch(this.ids);
          }
        }).then(() => {
          this.reload();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
    },
    activated() {
      //组件被激活时重绘表格
      this.$refs.clientTable.changeTableHeight();
    },
  };
</script>
<style scoped>
  .myscroll{
    max-width: 500px;

  }
</style>
