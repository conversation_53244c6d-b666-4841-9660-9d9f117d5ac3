<template>
  <div v-cloak>
    <el-row :gutter="10" style="margin: 10px">
      <el-col :sm="6">
        <el-card>
          <div slot="header">字典导航</div>
          <div style="overflow: auto;height: 85%">
            <el-tree
              ref="dictTree"
              :data="dictList"
              :props="dictTreeProps"
              node-key="id"
              :default-expanded-keys="dictExpandedKeys"
              @node-click="handleLefTreeClick"
            ></el-tree>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="18">
        <el-card>
          <div slot="header">{{ title }}</div>
          <div style="overflow: auto;height: 85%;width: 550px;">
            <el-form ref="form" :model="dict" :rules="rules" label-width="95px">
              <el-form-item label="中文参数名" prop="name">
                <el-input
                  :disabled="!editAble"
                  v-model="dict.name"
                  placeholder="中文参数名"
                ></el-input>
              </el-form-item>
              <el-form-item label="英文参数名" prop="enName">
                <el-input
                  :disabled="!editAble"
                  v-model="dict.enName"
                  placeholder="英文参数名"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="dict.parentId!==-1" label="上级参数" prop="parentName">
                <el-input
                  :disabled="!addAble"
                  @click.native="addAble ? dictTree():''"
                  v-model="dict.parentName"
                  placeholder="字典维护"
                ></el-input>
              </el-form-item>
              <el-form-item label="参数值" prop="value">
                <el-input
                  :disabled="!editAble"
                  v-model="dict.value"
                  placeholder="参数值"
                ></el-input>
              </el-form-item>
              <el-form-item label="回显样式" prop="className">
                <my-select
                  :disabled="!editAble"
                  v-model="dict.className"
                  pvalue="echoStyle"
                  placeholder="回显样式"
                ></my-select>
              </el-form-item>
              <el-form-item label="排序号" prop="orderNum">
                <el-input-number
                  :disabled="!editAble"
                  v-model="dict.orderNum"
                  :min="0"
                  placeholder="排序号"
                ></el-input-number>
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input
                  :disabled="!editAble"
                  v-model="dict.remark"
                  placeholder="备注"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="!editAble">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="add"
                    v-hasPermi="['sys:dict:save']"
                  >新增
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    @click="update"
                    v-if="dict.parentId!==-1"
                    v-hasPermi="['sys:dict:update']"
                  >修改
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="delOne"
                    v-if="dict.parentId!==-1"
                    v-hasPermi="['sys:dict:delete']"
                  >删除
                  </el-button>
                </el-col>
              </el-form-item>
              <el-form-item v-else>
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-success"
                    size="mini"
                    @click="saveOrUpdate"
                  >保存
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="default"
                    icon="el-icon-refresh-left"
                    size="mini"
                    @click="reload"
                  >返回
                  </el-button>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 选择父类字典 -->
    <my-dialog title="选择父类字典" v-el-drag-dialog :visible.sync="openSelectDict" width="300px" append-to-body>
      <el-tree
        ref="selectDictTree"
        :data="dictList2"
        :props="dictTreeProps"
        node-key="id"
        :default-expanded-keys="dictExpandedKeys"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDictSelect">确 定</el-button>
        <el-button @click="cancelDictSelect">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { getDictTreeData, deleteDictData, submitDictData } from '@/api/system/dict'
import { handleTree } from '@/utils/ruoyi'
import elDragDialog from '@/directive/dialog/drag'
export default {
  name: 'Dict',
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
    return {
      addAble: false,
      editAble: false,
      dictList: [],
      dictList2: [],
      openSelectDict: false,
      dictExpandedKeys: [],
      title: '字典信息',
      dict: {
        name: '',
        enName: '',
        className: '',
        orderNum: 100,
        parentId: -1
      },
      selectDict: {},
      dictTreeProps: {
        children: 'children',
        label: 'name'
      },
      type: '',
      rules: {
        name: [{required: true, message:'中文参数名不能为空',trigger:'blur'}],
        value: [{required: true, message:'参数值不能为空',trigger:'blur'}],
      }
    }
  },
  methods: {
    //字典导航-字典树点击事件
    handleLefTreeClick(data, treeNode, nodeObj) {
      this.editAble = false
      this.title = '字典信息'
      this.$refs['form'].clearValidate()
      this.dict = JSON.parse(JSON.stringify(data))
      var parentNode = data.parentNode
      if (parentNode != null && typeof (parentNode) != 'undefined') {
        this.dict.parentName = parentNode.name;
        this.dict.parentValue = parentNode.value;
      }
      //打开页面默认展开字典树对应的节点
      this.dictExpandedKeys = [this.dict.id]
    },
    //加载菜单树
    getDictTreeLeft() {
      getDictTreeData(this.type).then(r => {
        console.log('我加载了')
        if (r.dictList.length > 0) {
          var map = new Map()
          r.dictList.forEach(item => {
            map.set(item.id, item)
          })
          for (var i = 0; i < r.dictList.length; i++) {
            if (map.get(r.dictList[i].parentId) == null) {
              r.dictList[i].parentId = -1
              r.dictList[i].open = true
              if (typeof (this.selectDict.name) == 'undefined') {
                this.dict = r.dictList[i]
              }
              break
            }
          }
        }
        this.dictList = handleTree(r.dictList, 'id', 'parentId')
        this.dictList2 = JSON.parse(JSON.stringify(this.dictList))
        var that = this
        this.$nextTick(() => {
          if (that.dict.id) {
            that.$refs.dictTree.setCurrentKey(that.dict.id)
            let node = that.$refs.dictTree.getNode(that.dict.id)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
          }
        })
      })
    },
    //新增操作
    add() {
      this.addAble = true
      this.editAble = true
      this.title = '字典信息-新增'
      this.selectDict = JSON.parse(JSON.stringify(this.dict))
      this.dict = {
        parentName: this.selectDict.name,
        parentId: this.selectDict.id,
        parentValue: this.selectDict.value,
        orderNum: 100,
        type: this.type
      }
    },
    //修改操作
    update() {
      this.editAble = true
      this.title = '字典信息-修改'
      this.selectDict = JSON.parse(JSON.stringify(this.dict))
    },
    //删除操作
    delOne() {
      let node = this.$refs.dictTree.getNode(this.dict.id)
      if (node.data.parentId === -1) {
        return this.$modal.msgWarning('不允许删除根节点字典')
      }
      if (node.data) {
        this.selectDict = this.$refs.dictTree.getNode(this.dict.parentId).data
        var tip = this.dict.hasChild ? '确定要删除选中节点及其下级节点？' : '确定要删除选中的记录？'
        this.$modal.confirm(tip).then(() => {
          this.$modal.loading('加载中')
          deleteDictData(this.dict.id, this.dict.parentValue).then(() => {
            this.$modal.msgSuccess('操作成功')
            this.reload()
            this.$modal.closeLoading()
          }).catch(this.$modal.closeLoading);
        }).catch(() => {})  //捕获异常错误
      }
    },
    //保存操作
    saveOrUpdate() {
      let that = this
      that.$refs['form'].validate(valid => {
        if (valid) {
          //提交后台
          that.$modal.loading('加载中')
          submitDictData(that.dict).then(r => {
            that.selectDict = r.dict
            that.$modal.msgSuccess('操作成功')
            that.reload()
            that.$modal.closeLoading()
          })
        }
      })
    },
    //返回操作
    reload() {
      this.dict = JSON.parse(JSON.stringify(this.selectDict))
      this.$refs['form'].clearValidate()
      this.getDictTreeLeft()
      this.addAble = false
      this.editAble = false
      this.title = '字典信息'
    },
    //上级参数弹窗
    dictTree() {
      this.openSelectDict = true
      this.$nextTick(function() {
        this.$refs.selectDictTree.setCurrentKey(this.dict.id)
      })
    },
    //选择父类字典弹窗-确定操作
    submitDictSelect() {
      let currentNode = this.$refs.selectDictTree.getCurrentNode()
      if (currentNode.id !== this.dict.id) {
        this.dict.parentId = currentNode.id
        this.dict.parentName = currentNode.name
        this.openSelectDict = false
      } else {
        this.$modal.msgWarning('不能选择自己为上级')
      }
    },
    //选择父类字典弹窗-取消操作
    cancelDictSelect() {
      this.openSelectDict = false
    }
  },
  mounted() {
    this.getDictTreeLeft()
  }
}
</script>



