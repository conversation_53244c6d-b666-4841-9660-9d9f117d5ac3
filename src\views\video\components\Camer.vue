<template>
  <div>
    <div v-if="cameraType == 'yingShi'" style="overflow: auto;width: 100%;" ref="myDiv">
      <iframe ref="myIframe" id="inlineFrameExample" :src="src" width="100%" :height="height"
        @onbeforeonload="handleLoad">
      </iframe>
    </div>
    <div v-if="cameraType === 'leChengYun'" id="root"></div>
    <video v-if="cameraType === 'tieTa'" id="HLS_Player" autoplay="autoplay" loop muted controls="controls"
      width="100%"></video>
    <video v-if="cameraType === 'yunRun'" id="flvPlayer" autoplay="autoplay" loop muted controls="controls"
      width="100%"></video>
    <div v-if="cameraType == 'xiaodu'" style="overflow: auto;width: 100%;" ref="myDiv">
      <iframe ref="myIframe" :src="appUrl" width="100%" :height="height">
      </iframe>
    </div>
  </div>
</template>

<script>
/*import '@/plugins/imou-player/imou-player.css'*/
import '@/plugins/imou-player/imou-player'
import {
  getKitToken,
  getTieTaUrl,
  getVideoSurveillance,
  getYunRunUrl
} from '@/api/video/videoSurveillance'
import common from '@/utils/common'

export default {
  name: "Camer",
  props: {
    camera: {
      type: Object,
      default: function () {
        return {};
      },
    },
    height: {
      type: String,
      default: 'auto'
    },
  },
  data() {
    return {
      src: '',
      cameraType: '',
      appUrl: ''
    }
  },
  watch: {
    camera(nval) {
      console.log("nval:", nval)
      if (nval != null && nval.platform) {
        this.cameraType = nval.platform
        this.onloadView(nval)
      }
    },
  },
  methods: {
    /** 加载视频 */
    onloadView(row) {
      const id = row.id;
      this.videoType = row.platform
      if (row.platform === 'yingShi') {
        getVideoSurveillance(id).then(r => {
          this.src = r.url;
        });
      } else if (row.platform === 'leChengYun') {
        // this.$nextTick(() => {
        //   this.handleLeCheng("11111","222","1",this.height.substring(0, this.height.length - 2));
        // });
        getKitToken(id).then(r => {
          console.log(id, r)
          this.$nextTick(() => {
            this.handleLeCheng(r.kitToken, row.platformSn, row.channel);
          });
        })
      } else if (row.platform === 'tieTa') {
        getTieTaUrl(id).then(r => {
          this.$nextTick(() => {
            this.handleTT(r.url);
          })
        })
      } else if (row.platform == 'yunRun') {
        if (typeof (flvjs) != 'undefined') {
          this.loadYunRun();
        } else {
          common.loadJsResource(["/lib/flv.js"]).then(() => {
            this.loadYunRun();
          });
        }

      } else if (row.platform === 'xiaodu') {
        this.appUrl = this.camera.appUrl;
        console.log(this.appUrl);
      }
    },
    //加载云睿平台flv直播地址
    loadYunRun() {
      if (flvjs.isSupported()) {
        getYunRunUrl(this.camera.id).then(r => {
          var flvPlayer = flvjs.createPlayer({
            type: 'flv',
            isLive: true,
            hasAudio: false,
            hasVideo: true,
            url: r.url,
          });
          var video2 = document.getElementById("flvPlayer")
          flvPlayer.attachMediaElement(video2);
          flvPlayer.load(); //加载
        })

      } else {
        console.error("flv not support");
      }

    },
    handleLeCheng(accessToken, platformSn, channel, height) {
      console.log(height)
      // 播放器初始化
      const player = new imouPlayer({
        id: 'root',
        width: '100%',
        height: height,
        // 设备序列号
        deviceId: platformSn,
        token: accessToken,
        channelId: channel,
        // 1 直播  2 录播
        type: 1,
        // 直播 0 高清  1 标清  默认
        //streamId: 0,
        // 录播 云录像 1 本地录像 localRecord 默认 云录像
        //recordType: 'cloud',
        // 如果设备设置了自定义音视频加密秘钥，则输入此秘钥；
        // 如果设备只设置了设备密码，则输入设备密码；其他情况默认设备序列号。
        //code: 'xxxxxx',
      });
      // // 播放
      player.play();
    },
    handleTT(url) {
      const video = document.getElementById('HLS_Player');
      if (Hls.isSupported()) {
        const hls = new Hls();
        hls.loadSource(url);
        hls.attachMedia(video);
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          video.play();
        });
      }
    },
    handleLoad() { },
  },
  mounted() {
    if (this.camera && this.camera.platform) {
      this.cameraType = this.camera.platform;
      this.onloadView(this.camera)
    }
  },
}
</script>
<style scoped lang="scss"></style>
