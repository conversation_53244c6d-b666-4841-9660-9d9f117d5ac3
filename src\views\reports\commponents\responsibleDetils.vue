<template>
  <div>
    <my-dialog @screenChange="screenChange" :title="title" v-bind="$attrs" :visible.sync="detailsOpen" width="75vw" append-to-body @close="cancel">
       <el-form :model="queryParamsView" ref="queryFormView" size="small" :inline="true" v-show="showSearchView" label-width="68px" @submit.native.prevent>
         <el-form-item label="河道名称" prop="reverName">
           <my-input v-model="queryParamsView.reverName" label="河道名称" @input="change($event)" placeholder="请输入河道名称" />
         </el-form-item>
         <el-form-item label="负责人" prop="name">
           <my-input v-model="queryParamsView.name" label="负责人" @input="change($event)" placeholder="请输入负责人" />
         </el-form-item>
         <el-form-item label="手机号" prop="mobile">
           <my-input v-model="queryParamsView.mobile" label="手机号" @input="change($event)" placeholder="请输入手机号" />
         </el-form-item>
         <el-form-item>
           <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryView">搜索</el-button>
           <el-button icon="el-icon-refresh" size="mini" @click="resetQueryView">重置</el-button>
         </el-form-item>
       </el-form>
       <el-row :gutter="10" class="mb8">
         <el-col :span="1.5" v-if="status=='daiXiuGai'||status=='daiXianShenHe'||status=='weiTianBao'">
           <el-button
             type="primary"
             icon="el-icon-document-checked"
             size="mini"
             @click="handleDownload"
             v-has-permi="['reports:responsiblePersion:import']"
           >下载模板</el-button>
         </el-col>
         <el-col :span="1.5" v-if="status=='daiXiuGai'||status=='daiXianShenHe'||status=='weiTianBao'">
           <el-button
             type="warning"
             icon="el-icon-upload"
             size="mini"
             @click="handleUpload"
             v-has-permi="['reports:responsiblePersion:import']"
           >导入</el-button>
           <input
             ref="excel"
             style="display: none;"
             type="file"
             @change="fileImport"
             accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
           >
         </el-col>
         <right-toolbar :showSearch.sync="showSearchView" @queryTable="reloadView"></right-toolbar>
       </el-row>
       <el-tabs type="border-card" v-model="activeName" >
         <el-tab-pane name="1"  label="责任人信息">
           <my-table  :show-radio="false" :multiselect=false  :url="'/reports/responsiblePersion/page/'+reportId" :fixed="true" ref="responsiblePersionTable"
                      :offset="dialogOffset" row-key="id" >
             <el-table-column  label="河道名称" min-width="110" fixed="left" prop="reverName" sortable="custom" header-align="center" align="center" column-key="REVER_NAME"></el-table-column>
             <el-table-column  label="所在市" min-width="120" prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
             <el-table-column  label="所在县/区" min-width="120" prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
             <el-table-column  label="起始位置" min-width="200" prop="startAddr" sortable="custom" header-align="center" align="center" column-key="START_ADDR"></el-table-column>
             <el-table-column  label="终止位置" min-width="200" prop="endAddr" sortable="custom" header-align="center" align="center" column-key="END_ADDR"></el-table-column>
             <el-table-column  label="县级责任人" min-width="350" prop="responsibleXian" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIAN">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleXian}} , {{scope.row.positionXian}} , {{scope.row.mobileXian}}
                 </div>
                 <div>
                   {{scope.row.startAddrXian}} - {{scope.row.endAddrXian}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="乡级责任人" min-width="350" prop="responsibleXiang" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIANG">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleXiang}} , {{scope.row.positionXiang}} , {{scope.row.mobileXiang}}
                 </div>
                 <div>
                   {{scope.row.startAddrXiang}} - {{scope.row.endAddrXiang}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="村级责任人" min-width="350" prop="responsibleCun" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_CUN">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleCun}} , {{scope.row.positionCun}} , {{scope.row.mobileCun}}
                 </div>
                 <div>
                   {{scope.row.startAddrCun}} - {{scope.row.endAddrCun}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="县级水行政主管部门责任人" min-width="350" prop="responsibleCompetentDept" header-align="center" align="center" sortable="custom" column-key="RESPONSIBLE_COMPETENT_DEPT">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleCompetentDept}} , {{scope.row.positionCompetentDept}} , {{scope.row.mobileCompetentDept}}
                 </div>
                 <div>
                   {{scope.row.startAddrCompetentDept}} - {{scope.row.endAddrCompetentDept}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="现场监管责任人" min-width="350" prop="responsibleSupervise" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_SUPERVISE">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleSupervise}} , {{scope.row.positionSupervise}} , {{scope.row.mobileSupervise}}
                 </div>
                 <div>
                   {{scope.row.startAddrSupervise}} - {{scope.row.endAddrSupervise}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="行政执法责任人" min-width="350" prop="responsibleEnforce" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_ENFORCE">
               <template  #default="scope">
                 <div>
                   {{scope.row.responsibleEnforce}} , {{scope.row.positionEnforce}} , {{scope.row.mobileEnforce}}
                 </div>
                 <div>
                   {{scope.row.startAddrEnforce}} - {{scope.row.endAddrEnforce}}
                 </div>
               </template>
             </el-table-column>
             <el-table-column  label="备注" min-width="150" prop="remark" sortable="custom" header-align="center" align="center" column-key="REMARK"></el-table-column>
           </my-table>
         </el-tab-pane>
         <el-tab-pane name="2"  label="操作记录" style="max-height: 42vh;overflow: auto">
           <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
             <th style="width: 30%">操作时间</th>
             <th>操作人</th>
             <th>操作状态</th>
             <th>操作备注</th>
             <tr align="center" v-for="(item,index) in processList1" :key="item.id">
               <td style="width: 30%">{{item.createTime}}</td>
               <td >{{item.createUser}}</td>
               <td >
                 <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>
               </td>
               <td >{{item.opinion}}</td>
             </tr>
           </table>

         </el-tab-pane>
       </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="isAudit" type="primary" @click="pass">审核通过</el-button>
        <el-button v-if="isAudit" type="warning" @click="reject">审核不通过</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </my-dialog>
  </div>

</template>


<script>
import Template from '@/views/sms/template/index.vue'
import { getProcessList } from '@/api/reports/responsiblePersion'
import { checkPassShi, checkPassXian, importResponsiblePersionReport } from '@/api/reports/responsiblePersionReport'

export default {
  name: 'responsibleDetils',
  components: { Template },
  emits: ["close"],
  props: {
    detailsOpen: {
      type: Boolean,
      default: false
    },
    reportId: {
      type: String,
      default: ''
    },
    version: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: ''
    },
    isAudit: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '详情'
    },
    isCityExamine: {
      type: Boolean,
      default: false
    },
    processList:{
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data(){
    return {
      queryParamsView:{},
      showSearchView:true,
      activeName:'1',
      dialogOffset:600,
      processList1:[],
      rowVersion: ''
    }
  },
  watch:{
    detailsOpen(nvl){
      if(nvl){
        this.activeName = '1'
      }
    },
    processList(nvl){
      if(nvl){
        this.processList1 = nvl
      }else {
        this.processList1 = []
      }
    },
    version(nvl){
      if(nvl){
        this.rowVersion = nvl
      }else {
        this.rowVersion = ''
      }
    }
  },
  methods:{
    screenChange(val){
      if(val){
        this.dialogOffset = 380
        this.$nextTick(() => {
          this.$refs.responsiblePersionTable.changeTableHeight()
        })
      }else{
        this.dialogOffset = 550
        this.$nextTick(() => {
          this.$refs.responsiblePersionTable.changeTableHeight()
        })
      }
    },
    cancel(){
      this.$emit('close')
    },
    reloadView(restart) {
      this.$refs.responsiblePersionTable.search(this.queryParamsView, restart);
      this.single=true;
      this.multiple = true;
    },
    handleQueryView() {
      this.reloadView(true);
    },
    resetQueryView() {
      this.resetForm("queryFormView");
      this.queryParamsView = {}
      this.handleQueryView();
    },
    //下载模板
    handleDownload(){
      this.download("/reports/responsiblePersion/downloadTemplate", {},"采砂管理责任人导入模板.xlsx")
    },
    handleUpload(){
      this.$refs.excel.click();
    },
    //导入
    fileImport(e){
      if (!this.$refs.excel.files.length) {
        return;
      }
      console.log(this.$refs.excel.files[0])
      var formData = new FormData();
      formData.append("file",this.$refs.excel.files[0]);
      importResponsiblePersionReport(formData,this.reportId,this.rowVersion).then(res=>{
        if (res.code == 0){
          this.rowVersion = res.version
          this.$modal.msgSuccess("导入成功");
          this.$refs.responsiblePersionTable.search(this.queryParams);
          e.target.value = null
          getProcessList(this.reportId).then(res=>{
            this.processList1 = res.list
          })
        }
      }).catch((err) => {
        console.log('err', err)
        e.target.value = null
      });
    },
    change(e){
      this.$forceUpdate()
    },
    // 导出
    handleExport(){
      this.download('/reports/responsiblePersion/export', {
        ...this.queryParams
      }, `采砂管理责任人_${new Date().getTime()}.xlsx`, 'application/json');
    },
    pass(){
      this.$confirm('是否确认审核通过当前数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if(this.isCityExamine){
          checkPassShi({id:this.reportId,examineStatus:"pass",opinion:'',version:this.rowVersion}).then(r => {
            this.$message({
              type: 'success',
              message: '审核通过成功!'
            });
            this.cancel()
          })
        }else {
          checkPassXian({id:this.reportId,examineStatus:"pass",opinion:'',version:this.rowVersion}).then(r => {
            this.$message({
              type: 'success',
              message: '审核通过成功!'
            });
            this.cancel()
          })
        }
      })
    },
    // 取消按钮
    reject() {
      this.$prompt('请填写不通过原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType:'textarea',
        inputValidator: (value) => {
          if (value) {
            return true;
          } else {
            return '请输入不通过原因';
          }
        },
      }).then(({ value }) => {
        if(this.isCityExamine){
          checkPassShi({id:this.reportId,examineStatus:"reject",opinion:value,version:this.rowVersion}).then(r => {
            this.$message({
              type: 'success',
              message: '审核不通过成功!'
            });
            this.cancel()
          })
        }else {
          checkPassXian({id:this.reportId,examineStatus:"reject",opinion:value,version:this.rowVersion}).then(r => {
            this.$message({
              type: 'success',
              message: '审核不通过成功!'
            });
            this.cancel()
          })
        }
      })

    },
  }
}
</script>

<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
