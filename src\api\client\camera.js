import request from '@/utils/request'

// 查询摄像头信息表详细
export function getCamera(id) {
  return request({
    url: '/client/camera/info/' + id,
    method: 'post'
  })
}

// 新增摄像头信息表
export function addCamera(data) {
  return request({
    url: '/client/camera/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改摄像头信息表
export function updateCamera(data) {
  return request({
    url: '/client/camera/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除摄像头信息表
export function delCamera(id) {
  return request({
    url: '/client/camera/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除摄像头信息表
export function delCameraBatch(ids) {
  return request({
    url: '/client/camera/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


