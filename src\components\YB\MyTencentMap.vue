<template>
  <el-row :gutter="10" style="border: 1px solid #dcdfe6; border-radius: 4px">
    <!-- 关键字搜索 -->
    <el-col :span="12" v-if="!disabled">
      <el-form-item
        label="关键字"
        label-width="80px"
        style="margin-bottom: 0; width: 100%; margin-top: 22px"
      >
        <el-autocomplete
          :clearable="true"
          style="width: 100%"
          v-model="searchValue"
          :fetch-suggestions="querySearch"
          placeholder="请输入关键字"
          :trigger-on-focus="false"
          @select="handleSelect"
          @clear="clearSearch"
        >
          <template slot-scope="data">
            <div>
              <div>
                {{ data.item.value }}
              </div>
              <div style="color: #8c8c8c; font-size: 12px">
                {{ data.item.address }}
              </div>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
    </el-col>

    <!-- 切换地图类型 -->
    <el-col v-if="switchType" :span="6">
      <el-form-item style="margin-bottom: 0; width: 100%; margin-top: 22px">
        <el-switch
          v-model="viewType"
          active-text="卫星图"
          inactive-text="矢量图"
          @change="changeViewType"
        >
        </el-switch>
      </el-form-item>
    </el-col>

    <!-- 地图容器 -->
    <div id="mapContainer" class="mapContainer"></div>
  </el-row>
</template>

<script>
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import {transformFromGCJToWGS,transformFromWGSToGCJ} from "@/utils/transformFromGCJToWGS";

export default {
  name: "MyTianDiTuMap",
  emits: ["change"],
  props: {
    //是否可编辑
    disabled: {
      type: Boolean,
      default: false,
    },
    // 地图中心点
    center: {
      type: Object,
      default: function () {
        return { lat: 38.041323, lng: 114.514686 };
      },
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10,
    },
    // 是否移除指南针
    removeCompass: {
      type: Boolean,
      default: false,
    },
    // 是否移除缩放
    removeZoom: {
      type: Boolean,
      default: false,
    },
    // 是否开启类型切换
    switchType: {
      type: Boolean,
      default: false,
    },
    // 以城市为中心搜索 默认石家庄市
    region: {
      type: String,
      default: "石家庄",
    },
    // 经度
    lng: {
      type: Number,
      default: null,
    },
    // 纬度
    lat: {
      type: Number,
      default: null,
    },
    isClickPopup:{
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      // 地图实例
      map: null,
      // 矢量图层
      vecLayer: null,
      // 矢量注记图层
      cvaLayer: null,
      // 影像图层
      imgLayer: null,
      // 影像注记图层
      ciaLayer: null,
      // 当前激活的图层
      activeLayer: null,
      activeText: null,
      // 标记点集合
      markers: [],
      // 信息窗口实例
      infoWindow: null,
      // 搜索值
      searchValue: "",
      // 当前视图模式
      viewType: false, // false:矢量图, true:影像图
      // 标记点数据
      positionData: {
        latitude: "",
        longitude: "",
        address: "",
      },
      // 地图是否初始化完成
      mapReady: false,
      // 天地图密钥
      tiandituToken: "041111aee14d7b1dd8362aba20e799ad",
    };
  },
  mounted() {
    // 初始化地图
    this.initMap();

    // 获取天地图密钥
    if (!this.tiandituToken) {
      console.error("天地图密钥未配置");
      return;
    }

    // 添加地图控件和图层
    this.$nextTick(() => {
      this.addMapLayers();

      // 监听父组件传入的经纬度变化
      if (this.lng && this.lat) {
        this.mapMarkByParent();
      }
    });
  },
  beforeDestroy() {
    // 清理地图实例和事件监听
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  },
  /** 监听父层经纬度输入框变化 */
  watch: {
    lng(nval, oval) {
      if (this.lng && this.lat) {
        if (nval !== oval || this.lat !== oval) {
          this.mapMarkByParent();
        }
      } else {
        this.searchValue = "";
        this.clearMarkers();
      }
    },
    lat(nval, oval) {
      if (this.lng && this.lat) {
        if (nval !== oval || this.lng !== oval) {
          this.mapMarkByParent();
        }
      } else {
        this.searchValue = "";
        this.clearMarkers();
      }
    },
  },
  methods: {
    /** 初始化地图 */
    initMap() {
      // 初始化地图实例
      this.map = L.map("mapContainer", {
        center: [this.center.lat, this.center.lng],
        zoom: this.zoom,
        zoomControl: !this.removeZoom,
        attributionControl: false,
      });

      // 地图初始化完成后设置图层
      this.map.whenReady(() => {
        this.mapReady = true;
        this.addMapLayers();
      });

      // 添加点击事件
      this.map.on("click", this.onMapClick);
      // this.openInfoWindow(this.map.getCenter(), "点击位置");
    },

    /** 添加地图图层√ */
    addMapLayers() {
      // 添加矢量图层
      this.vecLayer = L.tileLayer(
        "http://t{s}.tianditu.gov.cn/vec_w/wmts?layer=vec&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=" +
          this.tiandituToken,
        {
          subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
          maxZoom: 18,
          attribution: "天地图矢量",
        }
      );

      // 添加矢量注记图层
      this.cvaLayer = L.tileLayer(
        "http://t{s}.tianditu.gov.cn/cva_w/wmts?layer=cva&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=" +
          this.tiandituToken,
        {
          subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
          maxZoom: 18,
          attribution: "天地图矢量注记",
        }
      );

      // 添加影像图层
      this.imgLayer = L.tileLayer(
        "http://t{s}.tianditu.gov.cn/img_w/wmts?layer=img&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=" +
          this.tiandituToken,
        {
          subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
          maxZoom: 18,
          attribution: "天地图影像",

        }
      );

      // 添加影像注记图层
      this.ciaLayer = L.tileLayer(
        "http://t{s}.tianditu.gov.cn/cia_w/wmts?layer=cia&style=default&tilematrixset=w&Service=WMTS&Request=GetTile&Version=1.0.0&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&tk=" +
          this.tiandituToken,
        {
          subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
          maxZoom: 18,
          attribution: "天地图影像注记",
        }
      );

      // 默认添加矢量图层和注记
      this.activeLayer = this.vecLayer;
      this.activeText = this.cvaLayer;
      this.activeLayer.addTo(this.map);
      this.activeText.addTo(this.map);
    },

    /** 切换地图类型√ */
    changeViewType(flag) {
      // 移除当前图层
      this.map.removeLayer(this.activeLayer);
      this.map.removeLayer(this.activeText);

      // 根据选择添加新的图层
      if (flag) {
        // 卫星图(影像图)
        this.activeLayer = this.imgLayer;
        this.activeText = this.ciaLayer;
      } else {
        // 矢量图
        this.activeLayer = this.vecLayer;
        this.activeText = this.cvaLayer;
      }

      this.activeLayer.addTo(this.map);
      this.activeText.addTo(this.map);
    },

    /** 点击地图获取坐标和地址 */
    onMapClick(e) {
      if (this.disabled) return;

      const latlng = e.latlng;
      this.searchValue = "";
      this.clearMarkers();
      let location = transformFromWGSToGCJ(latlng.lat, latlng.lng);
      this.positionData = {
        latitude: location.latitude.toFixed(8),
        longitude: location.longitude.toFixed(8),
        address: '',
      };
      this.afterThrowData();
    },
    /** 添加标记到地图 */
    addMarker(position) {
      if (!position || !this.map) return null;

      const marker = L.marker(position, {
        icon: this.createCustomIcon(),
      }).addTo(this.map);
      if(this.isClickPopup){
        marker.on('click', () => {
          this.getAddressByLocation(position[0], position[1]);
        });
      }

      this.markers.push(marker);

      return marker;
    },
    /** 关键字搜索 */
    async querySearch(queryString, cb) {
      try {
        const url = `https://api.tianditu.gov.cn/v2/search?postStr=${encodeURIComponent(
          JSON.stringify({
            keyWord: queryString, // 关键词
            level: "18", // 地图缩放级别（1-20）
            mapBound: "113.46,36.05,119.85,42.62", // 河北省范围
            queryType: "2", // 建议词搜索类型
            count: "10", // 返回结果数量（相当于page_size）
            start: "0", // 分页起始（相当于page_index=1时设为0）
          })
        )}&type=query&tk=${this.tiandituToken}`;

        const response = await fetch(url).then((res) => res.json());
        if (response && response.pois && response.pois.length > 0) {
          const results = response.pois.map((item) => {
            return {
              value: item.name,
              address: item.address || "",
              lonlat: item.lonlat,
              location: [item.lonlat.split(",")[1], item.lonlat.split(",")[0]],
            };
          });
          cb(results);
        } else {
          cb([]);
        }
      } catch (e) {
        console.error("搜索失败:", e);
        cb([]);
      }
    },

    /** 选择搜索结果 */
    handleSelect(item) {
      if (!item.location) return;
      const [lat, lng] = item.location;
      let position = transformFromWGSToGCJ(Number(lat), Number(lng));
      this.positionData = {
        latitude: position.latitude.toFixed(8),
        longitude: position.longitude.toFixed(8),
        address: item.address || item.name,
      };
      this.afterThrowData();
    },

    /** 根据坐标获取地址 */
    getAddressByLocation(lat, lng) {
      // 天地图地理编码API
      const url = `https://api.tianditu.gov.cn/geocoder?postStr=${encodeURIComponent(
        JSON.stringify({
          lon: lng,
          lat: lat,
          ver: 1,
        })
      )}&type=geocode&tk=${this.tiandituToken}`;

      fetch(url)
        .then((res) => res.json())
        .then((res) => {
          if (res.status === "0" && res.result) {
            // 天地图成功状态码为0
            const addressInfo = res.result;

            // 天地图地址组件结构：addressComponent包含省/市/区/街道等信息
            this.markerGeometries = [
              {
                id: "1",
                styleId: "myStyle",
                position: [lat, lng], // Leaflet使用[lat, lng]顺序
                properties: {
                  address: addressInfo.formatted_address || "未知地址", // 完整地址
                },
              },
            ];
            this.positionData = {
              latitude: this.lat,
              longitude: this.lng,
              address: addressInfo.formatted_address || "未知地址", // 完整地址
            }
            this.afterThrowData();
            this.openInfoWindow(
              [lat, lng],
              addressInfo.formatted_address,
            );
          } else {
            console.error("天地图逆地址解析失败:", res.message);
            // 可添加失败提示或降级处理
          }
        })
        .catch((error) => {
          console.error("天地图请求异常:", error);
        });
    },
    /** 创建自定义图标 */
    createCustomIcon() {
      return L.icon({
        iconUrl: require("@/assets/icons/map/marker.png"),
        iconSize: [32, 32], // 设置图标大小
        iconAnchor: [16, 40], // 设置图标锚点，使标记底部中心对准坐标点
      });
    },

    /** 清除所有标记 */
    clearMarkers() {
      this.markers.forEach((marker) => {
        this.map.removeLayer(marker);
      });
      this.markers = [];
    },

    /** 打开信息窗口 */
    openInfoWindow(position, address) {
      this.closeInfoWindow();
      // 转换为GCJ02坐标

      const content = `
        <div style="padding: 5px;">
         <div><strong>地址：</strong>${address}</div>
         <div><strong>经度：</strong>${this.lat}</div>
         <div><strong>纬度：</strong>${this.lng}</div>
        </div>
      `;

      this.infoWindow = L.popup({
        offset: [0, -35],
        maxWidth: 400,
      })
        .setLatLng(position)
        .setContent(content)
        .openOn(this.map);
    },

    /** 关闭信息窗口 */
    closeInfoWindow() {
      if (this.infoWindow) {
        this.map.closePopup(this.infoWindow);
        this.infoWindow = null;
      }
    },

    /** 平移动画 */
    mapTranslate(time, center) {
      this.map.setView(center, 16, {
        animate: true,
        duration: time / 1000,
      });
    },

    /** 在地图上标记父组件传入的经纬度 */
    mapMarkByParent() {
      if (this.lat && this.lng) {
        let location = transformFromGCJToWGS(this.lat, this.lng);
        console.log(location);
        const position = [location.latitude, location.longitude];
        //清除标点
        this.clearMarkers();
        this.addMarker(position);
        this.map.setView(position, this.map.getZoom());
        this.getAddressByLocation(position[0],position[1]);
      }
    },
    /** 抛出数据变更事件 */
    afterThrowData() {
      this.$emit("change", this.positionData);
    },

    /** 清除搜索 */
    clearSearch() {
      this.restore();
      this.afterThrowData();
    },

    /** 还原地图状态 */
    restore() {
      this.clearMarkers();
      if (this.mapReady) {
        this.map.setView([this.center.lat, this.center.lng], this.zoom);
      }
      this.positionData = {
        latitude: "",
        longitude: "",
        address: "",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.mapContainer {
  margin-top: 80px;
  width: 100%;
  height: 420px;
  z-index: 1;
}

/* 确保leaflet样式正确加载 */
:deep(.leaflet-container) {
  width: 100%;
  height: 100%;
}

/* 自定义图标样式 */
:deep(.leaflet-marker-icon) {
  width: 25px !important;
  height: 34px !important;
}
</style>
