import request from '@/utils/request'

// 查询合作伙伴信息表详细
export function getPartner(id) {
  return request({
    url: '/open/partner/info/' + id,
    method: 'post'
  })
}

// 新增合作伙伴信息表
export function addPartner(data) {
  return request({
    url: '/open/partner/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改合作伙伴信息表
export function updatePartner(data) {
  return request({
    url: '/open/partner/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除合作伙伴信息表
export function delPartner(id) {
  return request({
    url: '/open/partner/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除合作伙伴信息表
export function delPartnerBatch(ids) {
  return request({
    url: '/open/partner/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//获取全部数据
export function getPartnerAllList() {
  return request({
    url: '/open/partner/allList',
    method: 'post',
    showLoading: true,
  });
}



