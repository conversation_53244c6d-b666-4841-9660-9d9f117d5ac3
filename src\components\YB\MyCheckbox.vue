<template>
  <el-checkbox-group v-model="innerValue">
    <el-checkbox v-for="(item,index) in finalOptions" :disabled="disabled" @change="$emit('change',innerValue)" :label="item[itemValue]">{{item[itemName]}}</el-checkbox>
  </el-checkbox-group>
</template>

<script>
import BaseMixin from "@/components/YB/mixins/BaseMixin";

export default {
  name: "MyCheckbox",
  mixins: [BaseMixin],
  props: {
    value: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  data: function () {
    return {
      innerValue: [],
    }
  },
}
</script>

<style scoped>

</style>
