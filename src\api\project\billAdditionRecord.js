import request from '@/utils/request'

// 查询项目开单数量追加记录详细
export function getBillAdditionRecord(id) {
  return request({
    url: '/project/billAdditionRecord/info/' + id,
    method: 'post'
  })
}

// 新增项目开单数量追加记录
export function addBillAdditionRecord(data) {
  return request({
    url: '/project/billAdditionRecord/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改项目开单数量追加记录
export function updateBillAdditionRecord(data) {
  return request({
    url: '/project/billAdditionRecord/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除项目开单数量追加记录
export function delBillAdditionRecord(id) {
  return request({
    url: '/project/billAdditionRecord/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除项目开单数量追加记录
export function delBillAdditionRecordBatch(ids) {
  return request({
    url: '/project/billAdditionRecord/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//修改开单授权的数量
export function setCount(data) {
  return request({
    url: '/project/billAdditionRecord/setCount',
    method: 'post',
    data: data,
    showLoading: true,
  })
}


