<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="河道名称" prop="reverName">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.reverName"
          placeholder="请输入河道名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属地区" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="数据状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择数据状态"
          pvalue="dataStatus"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reports:diversionResponsiblePerson:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reports:diversionResponsiblePerson:export']"
        >导出
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>



    <my-table url="/reports/diversionResponsiblePerson/page" :fixed="true"  ref="diversionResponsiblePersonTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="河道名称" min-width="120" fixed="left"  prop="reverName" sortable="custom" header-align="center" align="center" column-key="REVER_NAME"></el-table-column>
  <el-table-column  label="所在市" min-width="120"   prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
  <el-table-column  label="所在县/区" min-width="120"   prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
  <el-table-column  label="起始位置" min-width="120" prop="startAddr" sortable="custom" header-align="center" align="center" column-key="START_ADDR"></el-table-column>
  <el-table-column  label="终止位置" min-width="120" prop="endAddr" sortable="custom" header-align="center" align="center" column-key="END_ADDR"></el-table-column>
  <el-table-column  label="县级责任人" min-width="150" prop="responsibleXian" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIAN"></el-table-column>
  <el-table-column  label="县名称及职务" min-width="180" prop="positionXian" sortable="custom" header-align="center" align="center" column-key="POSITION_XIAN"></el-table-column>
  <el-table-column  label="乡级责任人" min-width="150" prop="responsibleXiang" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIANG"></el-table-column>
  <el-table-column  label="乡镇名称及职务" min-width="160" prop="positionXiang" sortable="custom" header-align="center" align="center" column-key="POSITION_XIANG"></el-table-column>
  <el-table-column  label="村级责任人" min-width="150" prop="responsibleCun" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_CUN"></el-table-column>
  <el-table-column  label="村名称及职务" min-width="160" prop="positionCun" sortable="custom" header-align="center" align="center" column-key="POSITION_CUN"></el-table-column>
  <el-table-column  label="县级水行政主管部门责任人" min-width="220" prop="responsibleCompetentDept" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_COMPETENT_DEPT"></el-table-column>
  <el-table-column  label="县级水行政主管部门单位及职务" min-width="240" prop="positionCompetentDept" sortable="custom" header-align="center" align="center" column-key="POSITION_COMPETENT_DEPT"></el-table-column>
  <el-table-column  label="现场监管责任人" min-width="160" prop="responsibleSupervise" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_SUPERVISE"></el-table-column>
  <el-table-column  label="现场监管单位及职务" min-width="180" prop="positionSupervise" sortable="custom" header-align="center" align="center" column-key="POSITION_SUPERVISE"></el-table-column>
  <el-table-column  label="行政执法责任人" min-width="160" prop="responsibleEnforce" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_ENFORCE"></el-table-column>
  <el-table-column  label="行政执法单位及职务" min-width="180" prop="positionEnforce" sortable="custom" header-align="center" align="center" column-key="POSITION_ENFORCE"></el-table-column>
  <el-table-column  label="南水北调中线一期工程管理单位责任人" min-width="270" prop="responsibleDiversion"  sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_DIVERSION"></el-table-column>
  <el-table-column  label="南水北调中线一期工程管理单位责任人职务" min-width="300" prop="positionDiversion" sortable="custom" header-align="center" align="center" column-key="POSITION_DIVERSION"></el-table-column>
  <el-table-column  label="数据状态" min-width="120" fixed="right"  prop="status" sortable="custom" header-align="center" align="center" column-key="STATUS">
      <template #default="scope">
        <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
      </template>
  </el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right"  min-width="160"   align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        title="修改"
        v-if="scope.row.status == 'daiXiuGai' || scope.row.status == 'daiXianShenHe'|| scope.row.status == 'yiTongGuo'"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['reports:diversionResponsiblePerson:update']"
      ></el-button>
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-document-copy"
        title="查看详情"
        @click="handleView(scope.row)"
        v-has-permi="['reports:diversionResponsiblePerson:list']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        title="删除"
        @click="handleDelete(scope.row)"
        v-if="scope.row.status == 'daiXiuGai'|| scope.row.status == 'daiXianShenHe'"
        v-hasPermi="['reports:diversionResponsiblePerson:delete']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        title="删除"
        @click="handleDelete(scope.row)"
        v-hasPermi="['reports:diversionResponsiblePerson:shengDelete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改南水北调交叉河道监管责任人表对话框 -->
    <my-dialog :title="title" :visible.sync="open" append-to-body width="1000px"  height="70vh">
      <el-form ref="form" :model="diversionResponsiblePerson"  label-width="150px">
        <el-divider content-position="center">
          <i class="el-icon-info"></i>基本信息
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="cityAreaCode" :rules="[{notNull:true,message:'请输入所在市'}]" label="所在市" >
              <my-select
                v-model="diversionResponsiblePerson.cityAreaCode"
                disabled
                :options="cityAreaList"
                itemValue ="areaCode"
                itemName="areaName">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12" >
            <my-form-item  ref="districtAreaCode"  label="所在县/区" prop="districtAreaCode">
              <my-select
                v-model="diversionResponsiblePerson.districtAreaCode"
                :disabled="isShow"
                :options="areaList"
                itemValue ="areaCode"
                itemName="areaName">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="河道名称" ref="reverName" prop="reverName" :rules="[{notNull:true,message:'请输入河道名称'}]">
              <my-input v-model.trim="diversionResponsiblePerson.reverName" placeholder="请输入河道名称" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="起始位置" ref="startAddr" prop="startAddr" :rules="[{notNull:true,message:'请输入起始位置'}]">
              <my-input v-model.trim="diversionResponsiblePerson.startAddr" placeholder="请输入起始位置" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="终止位置" ref="endAddr" prop="endAddr" :rules="[{notNull:true,message:'请输入终止位置'}]">
              <my-input v-model.trim="diversionResponsiblePerson.endAddr" placeholder="请输入终止位置" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>各级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="县级责任人" ref="responsibleXian" prop="responsibleXian" :rules="[{notNull:true,message:'请输入县级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleXian" placeholder="请输入县级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="县名称及职务" ref="positionXian" prop="positionXian" :rules="[{notNull:true,message:'请输入县名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionXian" placeholder="请输入县名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="乡级责任人" ref="responsibleXiang" prop="responsibleXiang" :rules="[{notNull:true,message:'请输入乡级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleXiang" placeholder="请输入乡级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="乡镇名称及职务" ref="positionXiang" prop="positionXiang" :rules="[{notNull:true,message:'请输入乡镇名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionXiang" placeholder="请输入乡镇名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="村级责任人" ref="responsibleCun" prop="responsibleCun" :rules="[{notNull:true,message:'请输入村级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleCun" placeholder="请输入村级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="村名称及职务" ref="positionCun" prop="positionCun" :rules="[{notNull:true,message:'请输入村名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionCun" placeholder="请输入村名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>水行政主管部门责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleCompetentDept" prop="responsibleCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleCompetentDept" placeholder="请输入县级水行政主管部门责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionCompetentDept" prop="positionCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionCompetentDept" placeholder="请输入县级水行政主管部门单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>现场监管责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleSupervise" prop="responsibleSupervise" :rules="[{notNull:true,message:'请输入现场监管责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleSupervise" placeholder="请输入现场监管责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionSupervise" prop="positionSupervise" :rules="[{notNull:true,message:'请输入现场监管单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionSupervise" placeholder="请输入现场监管单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>行政执法责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleEnforce" prop="responsibleEnforce" :rules="[{notNull:true,message:'请输入行政执法责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleEnforce" placeholder="请输入行政执法责任人" :maxlength="128"/>
            </my-form-item>

          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionEnforce" prop="positionEnforce" :rules="[{notNull:true,message:'请输入行政执法单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionEnforce" placeholder="请输入行政执法单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>南水北调中线一期工程管理单位责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleDiversion" prop="responsibleDiversion" :rules="[{notNull:true,message:'请输入南水北调中线一期工程管理单位责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleDiversion" placeholder="请输入南水北调中线一期工程管理单位责任人" :maxlength="128"/>
            </my-form-item>

          </el-col>
          <el-col :span="12">
            <my-form-item label="职务" ref="positionDiversion" prop="positionDiversion" :rules="[{notNull:true,message:'请输入南水北调中线一期工程管理单位责任人职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionDiversion" placeholder="请输入南水北调中线一期工程管理单位责任人职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>备注
        </el-divider>
        <el-row>
          <el-col :span="24">
            <my-form-item ref="remark"  label="备注" prop="remark">
              <my-input type="textarea" v-model.trim="diversionResponsiblePerson.remark" placeholder="请输入备注" :maxlength="255"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>审核流程
        </el-divider>
        <el-row>
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item,index) in processList" :key="item.id">
              <td style="width: 30%">{{item.createTime}}</td>
              <td >{{item.createUser}}</td>
              <td >
                <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>
              </td>
              <td >{{item.opinion}}</td>
            </tr>
          </table>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
    <MyDiversionReportDetails :detailsOpen="detailsOpen" :id="id" :cityName ="rowCityName" :districtName="rowDistrictName" @close="detailsOpen=false" />

  </div>
</template>

<script>
import { getDiversionResponsiblePerson, delDiversionResponsiblePerson, delDiversionResponsiblePersonBatch,addDiversionResponsiblePerson, updateDiversionResponsiblePerson } from "@/api/reports/diversionResponsiblePerson";
import Template from '@/views/sms/template/index.vue'
import { getUser } from '@/api/statistics/projectStatistics'
import { getProcessList } from '@/api/reports/responsiblePersion'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyDiversionReportDetails from '@/components/YB/MyDiversionReportDetails.vue'
import { getSubList } from '@/api/system/area'

export default {
  name: "DiversionResponsiblePerson",
  components: { MyDiversionReportDetails, MyAreaSelect, Template },
  data() {
    return {
      detailsOpen:false,
      id:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      diversionResponsiblePerson: {
        cityAreaCode:'',
        districtAreaCode:''
      },
      cityName:'',
      districtName:'',
      rowCityName:'',
      rowDistrictName:'',
      row:'',
      processList:[],
      areaList:[],
      cityAreaList:[],
      isShow:false,
    };
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      const r = await getUser()
      const res = await getSubList('000_013')
      this.cityAreaList = res.areaList
      if(r.cityAreaCode){
        this.diversionResponsiblePerson.cityAreaCode = r.cityAreaCode
      }
      if(r.districtAreaCode){
        this.isShow = true
        this.diversionResponsiblePerson.districtAreaCode = r.districtAreaCode
        const response = await getSubList(r.cityAreaCode)
        this.areaList = response.areaList
      }else {
        this.isShow = false
        const response = await getSubList(r.cityAreaCode)
        this.areaList = response.areaList
      }
    },
    /** 查询南水北调交叉河道监管责任人表列表 */
    reload(restart) {
      this.$refs.diversionResponsiblePersonTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.diversionResponsiblePerson = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        reverName: '',
        areaCode: '',
        status: '',
        startAddr: '',
        endAddr: '',
        responsibleXian: '',
        positionXian: '',
        responsibleXiang: '',
        positionXiang: '',
        responsibleCun: '',
        positionCun: '',
        responsibleCompetentDept: '',
        positionCompetentDept: '',
        responsibleSupervise: '',
        positionSupervise: '',
        responsibleEnforce: '',
        positionEnforce: '',
        responsibleDiversion: '',
        positionDiversion: '',
        remark:'',
        cityName:'',
        districtName:'',
        cityAreaCode:'',
        districtAreaCode:''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.diversionResponsiblePersonTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getUserInfo()
      this.processList = []
      this.diversionResponsiblePerson.cityName = this.cityName
      this.diversionResponsiblePerson.districtName = this.districtName
      this.open = true;
      this.title = "添加南水北调交叉河道监管责任人";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.row = row
      this.getUserInfo()
      const id = row.id || this.ids[0];
      getDiversionResponsiblePerson(id).then(r => {
        this.diversionResponsiblePerson = r.diversionResponsiblePerson;
        this.diversionResponsiblePerson.cityName = row.cityName
        this.diversionResponsiblePerson.districtName = row.districtName
        if(r.diversionResponsiblePerson.areaCode.length>11){
          this.diversionResponsiblePerson.districtAreaCode = r.diversionResponsiblePerson.areaCode
        }else {
          this.diversionResponsiblePerson.cityAreaCode = r.diversionResponsiblePerson.areaCode
          this.diversionResponsiblePerson.districtAreaCode = ''
        }
        this.open = true;
        this.title = "修改南水北调交叉河道监管责任人";
      });
      getProcessList(id).then(res=>{
        this.processList = res.page.list
      })
    },
    setAreaCode(){
      if(this.diversionResponsiblePerson.districtAreaCode){
        this.diversionResponsiblePerson.areaCode = this.diversionResponsiblePerson.districtAreaCode
      }else {
        this.diversionResponsiblePerson.areaCode = this.diversionResponsiblePerson.cityAreaCode
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          this.setAreaCode()
          if (this.diversionResponsiblePerson.id) {
            this.diversionResponsiblePerson.version = this.row.version;
            updateDiversionResponsiblePerson(this.diversionResponsiblePerson).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            console.log(this.diversionResponsiblePerson)
            addDiversionResponsiblePerson(this.diversionResponsiblePerson).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delDiversionResponsiblePerson(row.id);
        }else{
          return delDiversionResponsiblePersonBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExport(){
      this.download('/reports/diversionResponsiblePerson/export', {
        ...this.queryParams
      }, `南水北调监管负责人_${new Date().getTime()}.xlsx`, 'application/json');
    },
    handleView(row){
      this.id = row.id
      this.detailsOpen = true
      this.rowCityName = row.cityName
      this.rowDistrictName = row.districtName
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.diversionResponsiblePersonTable.changeTableHeight();
  },
};
</script>

<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
