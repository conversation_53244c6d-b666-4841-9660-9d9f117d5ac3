<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="公司/个人" prop="type">
        <my-select
          v-model="queryParams.type"
          pvalue="carType"
          placeholder="请选择公司/个人">
        </my-select>
      </el-form-item>
      <el-form-item label="公司名称" prop="company">
        <el-input
          clearable
          v-model.trim="queryParams.company"
          placeholder="请输入公司名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号码" prop="number">
        <el-input
          clearable
          v-model.trim="queryParams.number"
          placeholder="请输入车牌号码"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车主姓名" prop="ownerName">
        <el-input
          clearable
          v-model.trim="queryParams.ownerName"
          placeholder="请输入车主姓名"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司机姓名" prop="number">
        <el-input
          clearable
          v-model.trim="queryParams.driverName"
          placeholder="请输入司机姓名"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司机电话" prop="">
        <el-input
          clearable
          v-model.trim="queryParams.driverMobile"
          placeholder="请输入司机电话"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['project:car:save']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['project:car:update']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:car:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/car/page" ref="carTable" row-key="id" @my-selection-change="handleSelectionChange" :fixed="true">
      <el-table-column  label="公司/个人" align="center" min-width="120" sortable="custom" column-key="TYPE" fixed="left">
        <template #default="scope">
          <my-view pvalue="carType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="公司名称" header-align="center"   align="left" min-width="140" prop="company" sortable="custom" column-key="COMPANY"></el-table-column>
      <el-table-column  label="车牌号" header-align="center"   align="center" min-width="120" prop="number" sortable="custom" column-key="NUMBER"></el-table-column>
      <el-table-column  label="品牌型号" header-align="center"   align="center" min-width="120" prop="brand" sortable="custom" column-key="BRAND"></el-table-column>
      <el-table-column  label="车主姓名" header-align="center"   align="center" min-width="120" prop="ownerName" sortable="custom" column-key="OWNER_NAME"></el-table-column>
      <el-table-column  label="车主电话" header-align="center"   align="center" min-width="140" prop="mobile" sortable="custom" column-key="MOBILE"></el-table-column>
      <el-table-column  label="司机姓名" header-align="center"   align="center" min-width="120" prop="driverName" sortable="custom" column-key=""></el-table-column>
      <el-table-column  label="司机电话" header-align="center"   align="center" min-width="140" prop="driverMobile" sortable="custom" column-key=""></el-table-column>
      <el-table-column  label="自重（吨）" header-align="center"   align="center" min-width="140" prop="kerbWeight" sortable="custom" column-key="KERB_WEIGHT">
        <template #default="scope">
          {{common.toThousands(scope.row.kerbWeight,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="最大载重（吨）" header-align="center"   align="center" min-width="140" prop="maxPayload" sortable="custom" column-key="MAX_PAYLOAD">
        <template #default="scope">
          {{common.toThousands(scope.row.maxPayload,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="操作" header-align="center"   min-width="120"  column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-document-copy"
            title="查看详情"
            @click="handleView(scope.row)"
            v-hasPermi="['project:car:info']"
          ></el-button>
          <el-button
            size="mini"
            title="删除"
            class="btn-table-operate"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:car:delete']"
          >
          </el-button>
        </template>
      </el-table-column>
    </my-table>

<!--     添加或修改车辆管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="car" label-width="110px" :disabled="mode==='detail'">
        <el-row>
          <el-col :span="12">
            <my-form-item label="公司/个人" ref="company" prop="company" label-width="110px"
            >
              <my-select
                v-model="car.type"
                pvalue="carType"
                placeholder="请选择公司/个人">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item v-if="car.type == 'company'" label="公司名称" ref="company" prop="company" label-width="110px"
            >
                <my-input v-model="car.company"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="车牌号" ref="number" prop="number">
              <my-input v-model="car.number"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="品牌型号" ref="brand" prop="brand" >
              <my-input v-model="car.brand"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="车主姓名" ref="ownerName" prop="ownerName"
            >
              <my-input v-model="car.ownerName"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="车主电话" ref="mobile" prop="mobile"
            >
              <my-input v-model="car.mobile"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="司机姓名" ref="ownerName" prop="ownerName"
            >
              <my-input v-model="car.driverName"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="司机电话" ref="mobile" prop="mobile"
            >
              <my-input v-model="car.driverMobile"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="自重（吨）" ref="kerbWeight" prop="kerbWeight"
            >
              <my-input v-model="car.kerbWeight"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="最大载重（吨）" ref="maxPayload" prop="maxPayload"
            >
              <my-input v-model="car.maxPayload"/>
            </my-form-item>
          </el-col>
          <el-col :span="24">
            <my-form-item label="车辆照片" v-if="this.carImgList.length>0">
              <el-image
                style="width: 100px; height: 100px;margin: 5px"
                v-for="(item,index) in carImgList"
                :key="item.id"
                title="查看照片"
                class="currentPicture"
                :src="item.thumbnailPath"
                fit="cover"
                @click="handleViewPicture(item)"
              />
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>

    </el-dialog>

    <!-- 预览图片遮罩层 -->
    <el-image-viewer
      style="z-index: 2050"
      v-if="imageVisible"
      :url-list="[imageUrl]"
      :on-close="() => {imageVisible = false}"
    />
  </div>
</template>

<script>
import { getCar, delCar, delCarBatch,addCar, updateCar } from "@/api/project/car";
import common from '../../../utils/common'

export default {
  name: "CarManage",
  computed: {
    common() {
      return common
    }
  },
  components: {
    'el-image-viewer': () => import("element-ui/packages/image/src/image-viewer"),
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        company:'',
        ownerName:'',
        number:'',
        driverMobile:'',
        driverName:'',
      },
      // 表单参数
      car: {},
      mode:'',
      //车辆照片
      carImgList:[],
      // 是否显示预览图片弹出层
      imageVisible: false,
      // 图片路径
      imageUrl: '',
    };
  },
  mounted() {
  },
  methods: {
    /** 查询车辆管理列表 */
    reload(restart) {
      this.$refs.carTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.car = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        company: '',
        number: '',
        brand: '',
        mobile:'',
        ownerName: '',
        kerbWeight: null ,
        maxPayload: null ,
        times: null ,
        inputType: '',
        driverMobile:'',
        driverName:'',
      };
      this.carImgList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.driverName = '';
      this.queryParams.driverMobile = '';
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.carTable.getSelectRowKeys();
      this.single = this.ids.length!==1;
      this.multiple = !this.ids.length;
    },
    /** 预览图片操作 */
    handleViewPicture(row) {
      this.imageVisible = true;
      this.imageUrl = row.uploadFilePath;
    },
    // /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加车辆管理";
    // },
    // /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids[0];
    //   getCar(id).then(r => {
    //     this.car = r.car;
    //     this.open = true;
    //     this.title = "修改车辆管理";
    //   });
    // },
    // /** 提交按钮 */
    // submitForm() {
    //   this.$refs["form"].validate((valid, errorObj) => {
    //     if (valid) {
    //       if (this.car.id) {
    //         updateCar(this.car).then(r => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.reload();
    //         });
    //       } else {
    //         addCar(this.car).then(r => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.reload();
    //         });
    //       }
    //     }else{
    //       this.$scrollView(errorObj);
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(()=> {
        if (row.id) {
          return delCar(row.id);
        } else {
          return delCarBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 查看详情 */
    handleView(row){
      this.reset();
      this.title = "查看车辆详情";
      getCar(row.id).then(response => {
        this.car = response.car;
        this.carImgList = response.car.fileList
        this.open = true;
        this.mode = 'detail'
      });
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.carTable.changeTableHeight();
  },
};
</script>
