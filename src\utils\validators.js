export const numberValidator = (value, allowNegative = false) => {
  let filtered = String(value)
    .replace(allowNegative ? /[^\d.-]/g : /[^\d.]/g, '') // 过滤非法字符
    .replace(/(\..*)\./g, '$1') // 禁止多个小数点
    .replace(/(\-{2,})/g, '-') // 禁止多个负号

  // 处理负号位置
  if (allowNegative) {
    filtered = filtered.startsWith('-')
      ? '-' + filtered.replace(/-/g, '')
      : filtered.replace(/-/g, '')
  }

  // 处理小数点开头
  if (filtered.startsWith('.')) return '0' + filtered
  if (filtered === '-.') return '-0.'

  return filtered === '-' ? '' : filtered
}
