import request from '@/utils/request'

// 查询运砂申请详细
export function getApply(id) {
  return request({
    url: '/bill/apply/info/' + id,
    method: 'post'
  })
}

// 新增运砂申请
export function addApply(data) {
  return request({
    url: '/bill/apply/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改运砂申请
export function updateApply(data) {
  return request({
    url: '/bill/apply/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除运砂申请
export function delApply(id) {
  return request({
    url: '/bill/apply/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除运砂申请
export function delApplyBatch(ids) {
  return request({
    url: '/bill/apply/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

export function getMyCarList(userId){
  return request({
    url: '/project/car/myCarListByUserId/' + userId,
    method: 'post',
  })
}


