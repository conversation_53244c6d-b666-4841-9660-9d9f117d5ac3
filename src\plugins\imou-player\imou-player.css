@font-face {
  font-family: "iconfont"; /* Project id 3238821 */
  src: url(7f673f135b5f680f2b0a.eot?t=1700014427793); /* IE9 */
  src: url(7f673f135b5f680f2b0a.eot?t=1700014427793#iefix) format('embedded-opentype'), /* IE6-IE8 */
       url(data:application/x-font-woff2;charset=utf-8;base64,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) format('woff2'),
       url(3d2912821e420145e1e0.woff?t=1700014427793) format('woff'),
       url(3589d2e6dacc20b46ecf.ttf?t=1700014427793) format('truetype'),
       url(208e9c2cf1eceb5caac8.svg?t=1700014427793#iconfont) format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-videoRecord:before {
  content: "\e635";
}

.icon-videoRecord_1:before {
  content: "\e636";
}

.icon-luzhizhihui:before {
  content: "\e634";
}

.icon-icon_zantingbofang:before {
  content: "\e60d";
}

.icon-talk_1:before {
  content: "\e677";
}

.icon-icon_yuyinguanbi:before {
  content: "\e678";
}

.icon-talk:before {
  content: "\e67d";
}

.icon-laydate-prev-m-copy:before {
  content: "\e684";
}

.icon-icon_shouqi:before {
  content: "\e675";
}

.icon-icon_zhankai:before {
  content: "\e676";
}

.icon-Icon_YunLuXiang:before {
  content: "\e673";
}

.icon-Icon_BenDiLuXiang:before {
  content: "\e674";
}

.icon-voice_1:before {
  content: "\e67c";
}

.icon-Icon_JingYin:before {
  content: "\e672";
}

.icon-fullPage:before {
  content: "\e67e";
}

.icon-Icon_QuanPing:before {
  content: "\e671";
}

.icon-clarity_1:before {
  content: "\e682";
}

.icon-voice:before {
  content: "\e67f";
}

.icon-capture:before {
  content: "\e680";
}

.icon-Icon_LuPing:before {
  content: "\e669";
}

.icon-Icon_HD:before {
  content: "\e66a";
}

.icon-Icon_Voice:before {
  content: "\e66b";
}

.icon-Icon_ScreenShot:before {
  content: "\e66d";
}

.icon-Icon_SD:before {
  content: "\e66e";
}

.icon-Icon_YunTai:before {
  content: "\e66f";
}

.icon-Icon_WangYeQuanPing:before {
  content: "\e670";
}

.icon-clarity:before {
  content: "\e681";
}

.icon-WangYeQuanPing:before {
  content: "\e683";
}

.icon-Icon_Play:before {
  content: "\e667";
}

.icon-Icon_Stop:before {
  content: "\e668";
}

.icon-play_1:before {
  content: "\e67a";
}

.icon-play:before {
  content: "\e67b";
}

.icon-Icon_SDcard:before {
  content: "\e665";
}

.icon-Icon_Cloud:before {
  content: "\e666";
}

.icon-Icon_Left:before {
  content: "\e663";
}

.icon-Icon_Right:before {
  content: "\e664";
}

.icon-Console_icon_delete:before {
  content: "\e608";
}

.icon-Console_icon_calendar:before {
  content: "\e609";
}

.icon-code_icon_copy:before {
  content: "\e60c";
}


.icon {
  font-size: 35px !important;
  color: #f18d00;
  position: absolute;
  bottom: 10px;
}
.record-icon {
  font-size: 35px !important;
  color: #f18d00;
  position: absolute;
  bottom: 60px;
}
.topRight {
  top: 20px;
  bottom: unset;
}
.topLeft {
  top: 16px;
  left: 16px;
  position: absolute;
  font-size: 20px;
}
.position_left_0 {
  left: 10px;
}
.position_left_1 {
  left: 65px;
}
.position_left_2 {
  left: 120px;
}
.position_left_3 {
  left: 175px;
}
.position_left_4 {
  left: 230px;
}
.position_left_5 {
  left: 285px;
}
.position_left_6 {
  left: 340px;
}
.position_left_7 {
  left: 395px;
}
.position_left_8 {
  left: 450px;
}
.position_right_0 {
  right: 10px;
}
.position_right_1 {
  right: 65px;
}
.position_right_2 {
  right: 120px;
}
.position_right_3 {
  right: 175px;
}
.position_right_4 {
  right: 230px;
}
.position_right_5 {
  right: 285px;
}
.position_right_6 {
  right: 340px;
}
.position_right_7 {
  right: 395px;
}
.position_right_8 {
  right: 450px;
}
.player__PTZArea {
  height: 120px;
  width: 120px;
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0);
  z-index: 9999;
}
.player__PTZArea::after {
  content: '';
  width: 20px;
  height: 20px;
  top: 50%;
  right: 50%;
  background: #2b8bf7;
  position: absolute;
  transform: translate(50%, -50%);
  border-radius: 50%;
}
.arrow-up {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ffffff;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  position: absolute;
  pointer-events: none;
}
.arrow-down {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ffffff;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  position: absolute;
  pointer-events: none;
}
.arrow-left {
  border-right: 6px solid #ffffff;
  border-bottom: 6px solid transparent;
  border-top: 6px solid transparent;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  position: absolute;
  pointer-events: none;
}
.arrow-right {
  border-left: 6px solid #ffffff;
  border-bottom: 6px solid transparent;
  border-top: 6px solid transparent;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  position: absolute;
  pointer-events: none;
}
.reckon-time {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  top: 80px;
  width: 213px;
  height: 66px;
  border-radius: 35px 35px 35px 35px;
  background: #000000;
  box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.16);
  opacity: 0.6;
  color: #ffffff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 26px;
  padding-left: 16px;
}
.reckon-time::after {
  content: '';
  position: absolute;
  background-color: #FF461C;
  border-radius: 100%;
  width: 18px;
  height: 18px;
  top: 24px;
  left: 36px;
}

/** 图标字体 **/
@font-face {font-family: 'laydate-icon';
  src: url(8b41e97a50a295189017.eot);
  src: url(8b41e97a50a295189017.eot#iefix) format('embedded-opentype'),
  url(0e1027e32c5f29465471.svg#iconfont) format('svg'),
  url(914b22a8dfbd6493fabf.woff) format('woff'),
  url(a4c318e3c86c5c8ee79a.ttf) format('truetype');
}
                    
.laydate-icon{
  font-family:"laydate-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/**
 
 @Name: laydata
 
 **/
 

html #layuicss-laydate{display: none; position: absolute; width: 1989px;}

/* 初始化 */
.layui-laydate *{margin: 0; padding: 0;}

/* 主体结构 */
.layui-laydate, .layui-laydate *{box-sizing: border-box;}
.layui-laydate{position: absolute; z-index: 66666666; margin: 5px 0; border-radius: 2px; font-size: 14px; -webkit-animation-duration: 0.2s; animation-duration: 0.2s; -webkit-animation-fill-mode: both; animation-fill-mode: both;}
.layui-laydate-main{width: 272px;}
.layui-laydate-header *,
.layui-laydate-content td,
.layui-laydate-list li{transition-duration: .3s; -webkit-transition-duration: .3s;}

/* 微微往下滑入 */
@keyframes laydate-downbit {
  0% {opacity: 0.3; transform: translate3d(0, -5px, 0);}
  100% {opacity: 1; transform: translate3d(0, 0, 0);}
}

.layui-laydate{animation-name: laydate-downbit;}
.layui-laydate-static{ position: relative; z-index: 0; display: inline-block; margin: 0; -webkit-animation: none; animation: none;}

/* 展开年月列表时 */
.laydate-ym-show .laydate-prev-m,
.laydate-ym-show .laydate-next-m{display: none !important;}
.laydate-ym-show .laydate-prev-y,
.laydate-ym-show .laydate-next-y{display: inline-block !important;}
.laydate-ym-show .laydate-set-ym span[lay-type="month"]{display: none !important;}

/* 展开时间列表时 */
.laydate-time-show .layui-laydate-header .layui-icon,
.laydate-time-show .laydate-set-ym span[lay-type="year"],
.laydate-time-show .laydate-set-ym span[lay-type="month"]{display: none !important;}

/* 头部结构 */
.layui-laydate-header{position: relative; line-height:30px; padding: 10px 70px 5px;}
.layui-laydate-header *{display: inline-block; vertical-align: bottom;}
.layui-laydate-header i{position: absolute; top: 10px; padding: 0 5px; color: #999; font-size: 18px; cursor: pointer;}
.layui-laydate-header i.laydate-prev-y{left: 15px;}
.layui-laydate-header i.laydate-prev-m{left: 45px;}
.layui-laydate-header i.laydate-next-y{right: 15px;}
.layui-laydate-header i.laydate-next-m{right: 45px;}
.laydate-set-ym{width: 100%; text-align: center; box-sizing: border-box; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;}
.laydate-set-ym span{padding: 0 10px; cursor: pointer;}
.laydate-time-text{cursor: default !important;}

/* 主体结构 */
.layui-laydate-content{position: relative; padding: 10px; -moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}
.layui-laydate-content table{border-collapse: collapse; border-spacing: 0;}
.layui-laydate-content th,
.layui-laydate-content td{width: 36px; height: 30px; padding: 5px; text-align: center;}
.layui-laydate-content th{font-weight: 400;}
.layui-laydate-content td{position: relative; cursor: pointer;}
.laydate-day-mark{position: absolute; left: 0; top: 0; width: 100%; line-height: 30px; font-size: 12px; overflow: hidden;}
.laydate-day-mark::after{position: absolute; content:''; right: 2px; top: 2px; width: 5px; height: 5px; border-radius: 50%;}

/* 底部结构 */
.layui-laydate-footer{position: relative; height: 46px; line-height: 26px; padding: 10px;}
.layui-laydate-footer span{display: inline-block;  vertical-align: top; height: 26px; line-height: 24px; padding: 0 10px; border: 1px solid #C9C9C9; border-radius: 2px; background-color: #fff; font-size: 12px; cursor: pointer; white-space: nowrap; transition: all .3s;}
.layui-laydate-footer span:hover{color: #5FB878;}
.layui-laydate-footer span.layui-laydate-preview{cursor: default; border-color: transparent !important;}
.layui-laydate-footer span.layui-laydate-preview:hover{color: #666;}
.layui-laydate-footer span:first-child.layui-laydate-preview{padding-left: 0;}
.laydate-footer-btns{position: absolute; right: 10px; top: 10px;}
.laydate-footer-btns span{margin: 0 0 0 -1px;}

/* 年月列表 */
.layui-laydate-list{position: absolute; left: 0; top: 0; width: 100%; height: 100%; padding: 10px; box-sizing: border-box; background-color: #fff;}
.layui-laydate-list>li{position: relative; display: inline-block; width: 33.3%; height: 36px; line-height: 36px; margin: 3px 0; vertical-align: middle; text-align: center; cursor: pointer;}
.laydate-month-list>li{width: 25%; margin: 17px 0;}
.laydate-time-list{}
.laydate-time-list>li{height: 100%; margin: 0; line-height: normal; cursor: default;}
.laydate-time-list p{position: relative; top: -4px; line-height: 29px;}
.laydate-time-list ol{height: 181px; overflow: hidden;}
.laydate-time-list>li:hover ol{overflow-y: auto;}
.laydate-time-list ol li{width: 130%; padding-left: 33px; height: 30px; line-height: 30px; text-align: left; cursor: pointer;}

/* 提示 */
.layui-laydate-hint{position: absolute; top: 115px; left: 50%; width: 250px; margin-left: -125px; line-height: 20px; padding: 15px; text-align: center; font-size: 12px; color: #FF5722;}


/* 双日历 */
.layui-laydate-range{width: 546px;}
.layui-laydate-range .layui-laydate-main{display: inline-block; vertical-align: middle;}
.layui-laydate-range .laydate-main-list-1 .layui-laydate-header,
.layui-laydate-range .laydate-main-list-1 .layui-laydate-content{border-left: 1px solid #e2e2e2;}


/* 默认简约主题 */
.layui-laydate, .layui-laydate-hint{border: 1px solid #d2d2d2; box-shadow: 0 2px 4px rgba(0,0,0,.12); background-color: #fff; color: #666;}
.layui-laydate-header{border-bottom: 1px solid #e2e2e2;}
.layui-laydate-header i:hover,
.layui-laydate-header span:hover{color: #5FB878;}
.layui-laydate-content{border-top: none 0; border-bottom: none 0;}
.layui-laydate-content th{color: #333;}
.layui-laydate-content td{color: #666;}
.layui-laydate-content td.laydate-selected{background-color: #B5FFF8;}
.laydate-selected:hover{background-color: #00F7DE !important;}
.layui-laydate-content td:hover,
.layui-laydate-list li:hover{background-color: #eee; color: #333;}
.laydate-time-list li ol{margin: 0; padding: 0; border: 1px solid #e2e2e2; border-left-width: 0;}
.laydate-time-list li:first-child ol{border-left-width: 1px;}
.laydate-time-list>li:hover{background: none;}
.layui-laydate-content .laydate-day-prev,
.layui-laydate-content .laydate-day-next{color: #d2d2d2;}
.laydate-selected.laydate-day-prev,
.laydate-selected.laydate-day-next{background-color: #f8f8f8 !important;}
.layui-laydate-footer{border-top: 1px solid #e2e2e2;}
.layui-laydate-hint{color: #FF5722;}
.laydate-day-mark::after{background-color: #5FB878;}
.layui-laydate-content td.layui-this .laydate-day-mark::after{display: none;}
.layui-laydate-footer span[lay-type="date"]{color: #5FB878;}
.layui-laydate .layui-this{background-color: #009688 !important; color: #fff !important;}
.layui-laydate .laydate-disabled,
.layui-laydate .laydate-disabled:hover{background:none !important; color: #d2d2d2 !important; cursor: not-allowed !important; -moz-user-select: none; -webkit-user-select: none; -ms-user-select: none;}

/* 墨绿/自定义背景色主题 */
.laydate-theme-molv{border: none;}
.laydate-theme-molv.layui-laydate-range{width: 548px}
.laydate-theme-molv .layui-laydate-main{width: 274px;}
.laydate-theme-molv .layui-laydate-header{border: none; background-color: #009688;}
.laydate-theme-molv .layui-laydate-header i,
.laydate-theme-molv .layui-laydate-header span{color: #f6f6f6;}
.laydate-theme-molv .layui-laydate-header i:hover,
.laydate-theme-molv .layui-laydate-header span:hover{color: #fff;}
.laydate-theme-molv .layui-laydate-content{border: 1px solid #e2e2e2; border-top: none; border-bottom: none;}
.laydate-theme-molv .laydate-main-list-1 .layui-laydate-content{border-left: none;}
.laydate-theme-molv .layui-laydate-footer{border: 1px solid #e2e2e2;}

/* 格子主题 */
.laydate-theme-grid .layui-laydate-content td,
.laydate-theme-grid .layui-laydate-content thead,
.laydate-theme-grid .laydate-year-list>li,
.laydate-theme-grid .laydate-month-list>li{border: 1px solid #e2e2e2;}
.laydate-theme-grid .laydate-selected,
.laydate-theme-grid .laydate-selected:hover{background-color: #f2f2f2 !important; color: #009688 !important;}
.laydate-theme-grid .laydate-selected.laydate-day-prev,
.laydate-theme-grid .laydate-selected.laydate-day-next{color: #d2d2d2 !important;}
.laydate-theme-grid .laydate-year-list,
.laydate-theme-grid .laydate-month-list{margin: 1px 0 0 1px;}
.laydate-theme-grid .laydate-year-list>li,
.laydate-theme-grid .laydate-month-list>li{margin: 0 -1px -1px 0;}
.laydate-theme-grid .laydate-year-list>li{height: 43px; line-height: 43px;}
.laydate-theme-grid .laydate-month-list>li{height: 71px; line-height: 71px;}


