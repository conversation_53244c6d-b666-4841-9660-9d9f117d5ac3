@font-face {
  font-family: "iconfont"; /* Project id 4653611 */
  src: url('iconfont.woff2?t=1729674048267') format('woff2'),
       url('iconfont.woff?t=1729674048267') format('woff'),
       url('iconfont.ttf?t=1729674048267') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-icon-smm-keshihuajianguan:before {
  content: "\e60c";
}

.el-icon-smm-caisha:before {
  content: "\e636";
}

.el-icon-smm-zaiyun:before {
  content: "\e62d";
}

.el-icon-smm-hexiao:before {
  content: "\e614";
}

