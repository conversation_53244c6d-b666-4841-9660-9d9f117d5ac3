import Vue from 'vue'
import BaiduMap from 'vue-baidu-map'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import 'font-awesome/css/font-awesome.min.css'

import './assets/iconfont/iconfont.css'
// import 'default-passive-events';  // 解决开发警告

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import '@/assets/styles/main.scss'
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import {download} from '@/utils/request';
import LiTaost from '@/components/LiTaost/index.vue'
import ErrorTaost from '@/components/ErrorTaost/index.vue'
// 全局挂载Leaflet对象
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import "leaflet.pm";
import "leaflet.pm/dist/leaflet.pm.css";

//为了保证后续地图的正常加载，修改icon的路径
/* leaflet icon */
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
})

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import config from "@/utils/configUtils";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
import {scrollView} from '@/utils/validate'
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'

//el组件包装
import MyFormItem from "@/components/YB/MyFormItem";
import MyInput from "@/components/YB/MyInput";
import MyRadio from "@/components/YB/MyRadio";
import MyConfig from "@/components/YB/MyConfig";
import MyCheckbox from "@/components/YB/MyCheckbox";
import MySelect from "@/components/YB/MySelect";
import MyView from "@/components/YB/MyView";
import MyTable from "@/components/YB/MyTable";
import MyDialog from "@/components/YB/MyDialog";
import CamerDialog from '@/views/video/components/CamerDialog'
import MapDialog from '@/views/project/supervision/components/MapDialog';

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigValue = config.getConfigValue
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$scrollView = scrollView

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('LiTaost', LiTaost)
Vue.component('ErrorTaost', ErrorTaost)

Vue.component('MyInput', MyInput);
Vue.component('MyFormItem', MyFormItem);
Vue.component('MyRadio', MyRadio);
Vue.component('MyCheckbox', MyCheckbox);
Vue.component('MySelect', MySelect);
Vue.component('MyView', MyView);
Vue.component('MyConfig', MyConfig);
Vue.component('MyTable', MyTable);
Vue.component('MyDialog', MyDialog);
Vue.component("CamerDialog",CamerDialog)
Vue.component("MapDialog",MapDialog)
Vue.use(BaiduMap, {
  // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
  ak: 'gMTgolcmX4ucGEewrWBHoWt4feLfRKw6'
})
Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
//把后台api地址放到cookie中，用于集成数据大屏
Cookies.set('API-BASE-PATH', process.env.VUE_APP_API_SERVER_URL);



