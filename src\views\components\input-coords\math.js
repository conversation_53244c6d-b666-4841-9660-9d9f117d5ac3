

function toRadians(x) {
  return x / 180 * Math.PI;
}
function toDegrees(x) {
  return x / Math.PI * 180;
}
/**
 * 向投影系转换
 * @param {Object} lng  经度，度
 * @param {Object} lat  纬度，度
 * @param {Object} centralMeridian  中央子午线，度
 */
export function toGauss(lng, lat, centralMeridian) {
	let B = toRadians(lat);
	let L = toRadians(lng);
	let L0 = toRadians(centralMeridian);
	let pi = 3.1415926535897932;
	let a = 6378137;
	let b = 6356752.314245179;
	let c = Math.sqrt(a * a - b * b);
	let e = c / a;
	let ee = e * e;
	let e2 = c / b;

	let sb = Math.sin(B);
	let s2b = Math.sin(2*B);
	let s4b = Math.sin(4*B);
	let s6b = Math.sin(6*B);
	let s8b = Math.sin(8*B);
	let cb = Math.cos(B);
	let t = Math.tan(B);

	let n = e2 * cb;
	let nn = n * n;
	let nnnn = nn * nn;
	let tt = t * t;
	let tttt = tt * tt;

	let m = (L - L0) * cb;
	let mm = m * m;
	let mmmm = mm * mm;

	let N = a / Math.sqrt(1 - ee * sb * sb);
	let m0 = a * (1 - ee);
	let m2 = 3 / 2 * ee * m0;
	let m4 = 5 / 4 * ee * m2;
	let m6 = 7 / 6 * ee * m4;
	let m8 = 9 / 8 * ee * m6;

	let a0 = m0 	+ 1/2*m2 	+ 3/8*m4 	+ 5/16*m6 	+ 35/128*m8;
	let a2 = 				1/2*m2 	+ 1/2*m4 	+ 15/32*m6 	+ 7/16*m8;
	let a4 = 									1/8*m4 	+ 3/16*m6 	+ 7/32*m8;
	let a6 = 														1/32*m6 	+ 1/16*m8;
	let a8 = 																				1/128*m8;
	// let X = a0*B - a2/2*s2b + a4/4*s4b - a6/6*s6b	+ a8/8*s8b;

	let aa0 = 6367449.145766291;
	let aa2 = 16038.508615363438;
	let aa4 = 16.832599650911032;
	let aa6 = 0.021980996239556235;
	let aa8 = 0.00003057868500356003;
	let X = aa0*B - aa2*s2b + aa4*s4b - aa6*s6b	+ aa8*s8b;

	let x = X + N * t * mm * (1/2 + (5-tt+9*nn+4*nnnn)*mm/24 + (61-58*tt+tttt)*mmmm/720);
	let y = N * m * (1 + (1-tt+nn)*mm/6 + (5-18*tt+tttt+72*nn-58*e2*e2)*mmmm/120);
	return [y + 500000, x];
}

/**
 * 向大地坐标系转换
 * @param {Object} y  东西向坐标，米
 * @param {Object} x  南北向坐标，米
 * @param {Object} centralMeridian  中央子午线，度
 */
export function fromGauss(y, x, centralMeridian) {

	let pi = 3.1415926535897932;
	let a = 6378137;
	let b = 6356752.314245179;
	let c = Math.sqrt(a * a - b * b);
	let e = c / a;
	let ee = e * e;
	let e2 = c / b;

	let B1 = x / a / (1 - ee/4 - ee*ee*3/64 - ee*ee*ee*5/256);
	let s2b = Math.sin(2*B1);
	let s4b = Math.sin(4*B1);
	let s6b = Math.sin(6*B1);
	let s8b = Math.sin(8*B1);

	let f = (a - b) / a;
	let ef = f / (2 - f);

	let a0 = 1;
	let a2 = 3/2*ef - 27/32*ef*ef*ef;
	let a4 = 21/16*ef*ef - 55/32*ef*ef*ef*ef;
	let a6 = 151/96*ef*ef*ef;
	let a8 = 1097/512*ef*ef*ef*ef;

	let Bf = B1 + a2*s2b + a4*s4b + a6*s6b + a8*s8b;
	let Cf = e2*e2*Math.cos(Bf)*Math.cos(Bf);
	let Tf = Math.tan(Bf) * Math.tan(Bf);
	let k = Math.sqrt(1-ee*Math.sin(Bf)*Math.sin(Bf));
	let Rf = a * (1 - ee) / k / k / k;
	let Nf = a / k;
	let D = (y - 500000) / Nf;

	let DD = D * D;
	let DDDD = DD * DD;
	let B = Bf - Nf * Math.tan(Bf) / Rf * DD * (1/2 - (5+3*Tf+10*Cf-9*e2*e2)*DD/24 + (61+90*Tf+45*Tf*Tf)*DDDD/720);
	let L = 1 / Math.cos(Bf) * D * (1 - (1+2*Tf+Cf)*DD/6 + (5-2*Cf+28*Tf+8*e2*e2+24*Tf*Tf)*DDDD/120);

	let lng = toDegrees(L) + centralMeridian;
	let lat = toDegrees(B);
	return [lng, lat];
}
