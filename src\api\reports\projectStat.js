import request from '@/utils/request'

// 查询项目开单统计表详细
export function getProjectStat(id) {
  return request({
    url: '/reports/projectStat/info/' + id,
    method: 'post'
  })
}

// 新增项目开单统计表
export function addProjectStat(data) {
  return request({
    url: '/reports/projectStat/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改项目开单统计表
export function updateProjectStat(data) {
  return request({
    url: '/reports/projectStat/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除项目开单统计表
export function delProjectStat(id) {
  return request({
    url: '/reports/projectStat/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除项目开单统计表
export function delProjectStatBatch(ids) {
  return request({
    url: '/reports/projectStat/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


