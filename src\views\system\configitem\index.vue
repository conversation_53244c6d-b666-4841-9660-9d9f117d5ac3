<template>
  <div class="app-container">
    <!-- tabs页签 -->
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane :label="category.name" :name="category.value" v-for="(category,index) in categoryList">
      </el-tab-pane>
    </el-tabs>
    <!-- 操作行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sys:configitem:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sys:configitem:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="batchDelete"
          v-hasPermi="['sys:configitem:delete']"
        >删除</el-button>
      </el-col>
    </el-row>
    <!-- 列表 -->
    <my-table :url="tableUrl" ref="configItemTable" :select-keys="ids" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column label="配置项编码" align="center" prop="configCode"/>
      <el-table-column label="配置项名称" align="center" prop="configName" />
      <el-table-column label="顺序号" align="center" prop="orderNo" />
      <el-table-column label="是否允许为空" align="center" prop="nullable">
        <template slot-scope="scope">
          {{scope.row.nullable?'是':'否'}}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            title="修改"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sys:configitem:update']"
          ></el-button>
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sys:configitem:delete']"
            title="删除"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>
    <!-- 添加或修改参数配置对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="24">
            <my-form-item label="配置项编码" prop="configCode"
                          :rules="[{ required: true, message: '配置项编码不能为空', trigger: 'blur' },{fn: this.checkCode, message: '配置项编码重复'}]">
              <el-input v-model="form.configCode" placeholder="请输入配置项编码" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <my-form-item label="配置项名称" prop="configName" :rules="[{ required: true, message: '配置项名称不能为空', trigger: 'blur' }]">
              <el-input v-model="form.configName" placeholder="请输入配置项名称（中文）" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="配置类别" prop="category">
              <my-select v-model="form.category" :options="categorySelectList"></my-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="顺序号" prop="orderNo">
              <el-input v-model="form.orderNo" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="是否允许为空" prop="nullable">
              <my-radio :options="[{name:'是',value:true},{name:'否',value:false}]" v-model="form.nullable"></my-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="默认值" prop="defaultValue">
              <el-input v-model="form.defaultValue" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { delConfig,batchDelConfig, addConfig,getConfig, updateConfig,getCategoryList,checkCode  } from "@/api/system/configitem";
export default {
  data() {
    return {
      activeName:'basic',
      tableUrl:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      categoryList:[],
      categorySelectList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {}
    };
  },
  mounted() {
    this.getCategoryList();
  },
  methods: {
    /** 查询列表 */
    getList(restart) {
      this.$refs.configItemTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
      this.ids = []
    },
    /** 点击页签事件 */
    handleClick(tab, event){
      this.tableUrl = '/sys/configitem/list/' + tab.name;
      this.ids = [];
    },
    /** 查询配置类别 */
    getCategoryList() {
      getCategoryList().then(response => {
        this.categoryList = response.configCategorys;
        this.categorySelectList = response.configCategorys.map(function (e) {
          return {"name": e.name, "value": e.value}
        });
        this.tableUrl = '/sys/configitem/list/' + this.categoryList[0].value;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        configCode: undefined,
        configName: undefined,
        category: undefined,
        orderNo: undefined,
        nullable: false,
        defaultValue:undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.category = this.activeName;
      this.title = "添加配置项";
    },
    // 多选框选中数据
    handleSelectionChange() {
      this.ids = this.$refs.configItemTable.getSelectRowKeys();
      this.single = this.ids.length!==1;
      this.multiple = !this.ids.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.id || this.ids
      getConfig(configId).then(response => {
        this.form = response.sysConfigItem;
        this.open = true;
        this.title = "修改配置项";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中');
          if (this.form.id) {
            updateConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          } else {
            addConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.configName + '"的配置项？').then(function() {
          return delConfig(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 批量删除按钮操作 */
    batchDelete() {
      const ids = this.$refs.configItemTable.getSelectRowKeys();
      this.$modal.confirm('是否确认删除所选配置项？').then(function() {
        return batchDelConfig(ids);
      }).then(() => {
        this.open = false;
        this.ids = [];
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {
      });
    },
    checkCode(){
      var that = this;
      return new Promise(function(resolve, reject) {
        checkCode(that.form.configCode,that.form.id).then(r => {
          if (r.result) {
            resolve();
          } else {
            reject();
          }
        }).catch(reject)
      })
    },
  }
};
</script>
