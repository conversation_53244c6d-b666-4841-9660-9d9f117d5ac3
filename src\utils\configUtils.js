import cache from "@/plugins/cache"
import {projectCode} from "@/settings"
import {encrypt,decrypt} from "@/utils/jsencrypt";
import {getConfigValueByCode} from "@/api/system/config";

/**
 * 系统配置工具类,走缓存
 */

//配置前缀
const prefix = "config";

/**
 * 获取配置
 * @param configCode
 */
 function getConfigValue(configCode) {
  let localVersion = cache.local.get("webSite.innerVersion");
  var key = projectCode + "." + prefix + '_' + localVersion + "." + configCode;

  return new Promise((resolve, reject)=>{
    let configValue = cache.session.get(key);
    if (configValue) {
      resolve(decrypt(configValue))
      return;
    }
    getConfigValueByCode(configCode).then(r => {
      cache.session.set(key, encrypt(r.configValue));
      resolve(r.configValue);
    }).catch(reject);
  })
}

/**
 * 设置缓存
 */
function setConfigValue(configCode, configValue) {
  var key = projectCode + "." + prefix + "." + configCode;
  cache.session.set(key, encrypt(configValue));
}

/**
 *<li>功能描述：清除配置缓存，storage没有提供获取所有key的api，只能全部清除了</li>
 * @author: <EMAIL>
 * @date:  2022/10/27 13:57
 */
function clearCache() {
  cache.session.clear();
}

export default {
  getConfigValue,
  setConfigValue,
  clearCache,
};
