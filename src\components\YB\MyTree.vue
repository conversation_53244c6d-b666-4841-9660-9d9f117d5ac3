<template>
  <div>
    <el-card style="height: 80vh">
      <div slot="header" style="display: flex;align-items: center;justify-content: space-between">
        项目信息
        <div style="padding-left: 30px">
          <el-input
            size="small"
            placeholder="输入项目名称进行搜索"
            clearable
            v-model="filterText">
          </el-input>
        </div>
      </div>

      <div style="overflow: auto;height: 65vh">
        <el-tree
          ref="leftTree"
          :data="deptList"
          :props="deptTreeProps"
          :node-key="nodeKey"
          :highlight-current="highlightCurrent"
          :filter-node-method="filterNode"
          :default-expanded-keys="deptExpandedKeys"
          @node-click="handleLefTreeClick"
        >
          <template v-slot="{ node, data }">
            <slot  v-bind::node="node"  v-bind::data="data"></slot>
          </template>
        </el-tree>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MyTree',
  props:{
    deptList:{
      type: Array,
      default: () => []
    },
    deptTreeProps:{
      type: Object,
      default: () => ({
        label: 'name',
        children: 'children'
      })
    },
    nodeKey:{
      type: String,
      default: 'deptId'
    },
    deptExpandedKeys:{
      type: Array,
      default: () => []
    },
    highlightCurrent:{
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      filterText: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.leftTree.filter(val);
    }
  },
  mounted() {
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data[this.deptTreeProps.label].indexOf(value)!== -1;
    },
    handleLefTreeClick(data) {
      this.$emit('handleLefTreeClick', data)
    }
  }
}
</script>

<style scoped>

</style>
