import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { Notification } from 'element-ui';
import { getMessage } from '@/api/reports/responsiblePersionReport'
const user = {
  state: {
    token: getToken(),
    name: '',
    deptname: '',
    deptcode:'',
    rolenames:'',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_ROLENAMES: (state, name) => {
      state.rolenames = name
    },
    SET_DEPTCODE: (state, name) => {
      state.deptcode = name
    },
    SET_DEPTNAME: (state, name) => {
      state.deptname = name
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    Login({ commit }, loginParams) {
      return new Promise((resolve, reject) => {
        login(loginParams).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          // commit('SET_PERMISSIONS', res.perms && res.perms.split(","));
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          console.log(res.user,'哪个先出来')
          const user = res.user
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_DEPTNAME', user.deptName);
           commit('SET_DEPTCODE', user.deptCode);
           commit('SET_ROLENAMES', user.roleNames)
          commit('SET_NAME', user.showName)
          commit('SET_PERMISSIONS', res.perms);
          commit('SET_AVATAR', avatar)
          resolve(res)
          getMessage().then(r => {
            console.log(r)
            if(r.message!=""){
              setTimeout(() => {
                Notification.warning({
                  title: '系统提示',
                  dangerouslyUseHTMLString: true,
                  message: r.message,
                  offset: 35
                })
              },500)
            }
          })

        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
