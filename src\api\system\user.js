import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/sys/user/list',
    method: 'post',
    data: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/sys/user/info/' + parseStrEmpty(userId),
    method: 'post'
  })
}

// 查询当前登录用户详细
export function getLoginUser() {
  return request({
    url: '/sys/user/userInfo',
    method: 'post'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/sys/user/save',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/sys/user/update',
    method: 'post',
    data: data
  })
}
// 个人中心修改用户
export function updateUserInfo(data) {
  return request({
    url: '/sys/user/updateUserInfo',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/sys/user/delete/' + userId,
    method: 'post'
  })
}
// 批量删除用户
export function batchDelUser(userIds) {
  return request({
    url: '/sys/user/delete',
    method: 'post',
    data:userIds,
  })
}

// 用户密码重置
export function resetUserPwd(userId) {
  return request({
    url: '/sys/user/reset/'+userId,
    method: 'post',
  })
}


// 用户密码重置
export function updateUserPwd(password, newPassword) {
  const data = {
    password,
    newPassword
  }
  return request({
    url: '/sys/user/password',
    method: 'post',
    params: data
  })
}

//获取数据权限数据
export function getDataGroupData() {
  return request({
    url: '/sys/dataGroup/groupList',
    method: 'post',
  })
}

//手机号验证重复
export function checkMobile(mobile,userId) {
  return request({
    url: '/sys/user/checkMobile?mobile='+mobile+'&id='+userId,
    method: 'post',
    headers:{
      allowRepeatSubmit: true,
    }
  })
}

//邮箱验证重复
export function checkEmail(email,userId) {
  return request({
    url: '/sys/user/checkEmail?email='+email+'&id='+userId,
    method: 'post',
    headers:{
      allowRepeatSubmit: true,
    }
  })
}
//验证用户名重复
export function checkUserName(username,userId) {
  return request({
    url: '/sys/user/checkUserName?userName='+username+'&id='+userId,
    method: 'post',
    headers:{
      allowRepeatSubmit: true,
    }
  })
}
//上传头像
export function uploadAvatar(formData) {
  return request({
    url: '/sys/user/uploadAvatar/avatar',
    method: 'post',
    data:formData
  })
}

//禁用或启用用户
export function changeUserStatus(userId,status) {
  return request({
    url: '/sys/user/changeStatus',
    method: 'post',
    params:{
      userId:userId,
      status:status
    },
    showLoading: true,
  })
}
