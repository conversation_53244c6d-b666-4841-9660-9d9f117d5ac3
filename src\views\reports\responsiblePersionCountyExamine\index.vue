<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item>
        <my-quarter-select style="width: 573px;height: 0"  ref="myQuarterSelect"  v-model="params" @change="handleChange"></my-quarter-select>
      </el-form-item>
      <el-form-item label="所属区域" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="数据状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择数据状态"
          pvalue="dataStatus"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--          v-hasPermi="['reports:responsiblePersionReport:save']"-->
      <!--        >新增</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['reports:responsiblePersionReport:update']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="danger"-->
      <!--          icon="el-icon-delete"-->
      <!--          size="mini"-->
      <!--          :disabled="multiple"-->
      <!--          @click="handleDelete"-->
      <!--          v-hasPermi="['reports:responsiblePersionReport:delete']"-->
      <!--        >删除</el-button>-->
      <!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['reports:responsiblePersion:export']"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              :cell-style="{padding: '0px'}" url="/reports/responsiblePersionReport/waitXianPage" :default-query-params="queryParams" ref="responsiblePersionReportTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="所在市" min-width="120" prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="所在县/区" min-width="120" prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="年度" min-width="120" prop="year" header-align="center" align="center" sortable="custom" column-key="YEAR"></el-table-column>
      <el-table-column  label="季度" min-width="120" prop="quarter" header-align="center" align="center" sortable="custom" column-key="QUARTER"></el-table-column>
      <el-table-column  label="数据状态" min-width="120" prop="status" header-align="center" align="center" sortable="custom" column-key="STATUS">
        <template  #default="scope">
          <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <!--      <el-button-->
          <!--        size="mini"-->
          <!--        type="success"-->
          <!--        class="btn-table-operate"-->
          <!--        icon="el-icon-edit"-->
          <!--        @click="handleUpdate(scope.row)"-->
          <!--        v-hasPermi="['reports:responsiblePersionReport:update']"-->
          <!--      ></el-button>-->
          <!--      <el-button-->
          <!--        size="mini"-->
          <!--        type="danger"-->
          <!--        class="btn-table-operate"-->
          <!--        icon="el-icon-delete"-->
          <!--        @click="handleDelete(scope.row)"-->
          <!--        v-hasPermi="['reports:responsiblePersionReport:delete']"-->
          <!--      ></el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            title="填报"-->
<!--            type="success"-->
<!--            class="btn-table-operate"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleFill(scope.row)"-->
<!--          ></el-button>-->
          <el-button
            size="mini"
            type="primary"
            class="btn-table-operate"
            icon="el-icon-finished"
            title="审核"
            @click="handleFill(scope.row)"
            v-has-permi="['reports:responsiblePersionReport:xianExamine']"
          >审核</el-button>
        </template>
      </el-table-column>
    </my-table>

<!--    &lt;!&ndash; 添加或修改采砂监管责任人填报表对话框 &ndash;&gt;-->
<!--    <my-dialog :title="title" :visible.sync="open" height="70vh" width="75vw" append-to-body>-->
<!--      <el-form :model="queryParamsView" ref="queryFormView" size="small" :inline="true" v-show="showSearchView" label-width="68px" @submit.native.prevent>-->
<!--        <el-form-item label="河道名称" prop="reverName">-->
<!--          <my-input v-model="queryParamsView.reverName" label="河道名称" @input="change($event)" placeholder="请输入河道名称" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="负责人" prop="name">-->
<!--          <my-input v-model="queryParamsView.name" label="负责人" @input="change($event)" placeholder="请输入负责人" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="手机号" prop="mobile">-->
<!--          <my-input v-model="queryParamsView.mobile" label="手机号" @input="change($event)" placeholder="请输入手机号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryView">搜索</el-button>-->
<!--          <el-button icon="el-icon-refresh" size="mini" @click="resetQueryView">重置</el-button>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <el-row :gutter="10" class="mb8">-->
<!--&lt;!&ndash;        <el-col :span="1.5">&ndash;&gt;-->
<!--&lt;!&ndash;          <el-button&ndash;&gt;-->
<!--&lt;!&ndash;            type="primary"&ndash;&gt;-->
<!--&lt;!&ndash;            icon="el-icon-document-checked"&ndash;&gt;-->
<!--&lt;!&ndash;            size="mini"&ndash;&gt;-->
<!--&lt;!&ndash;            @click="handleDownload"&ndash;&gt;-->
<!--&lt;!&ndash;          >下载模板</el-button>&ndash;&gt;-->
<!--&lt;!&ndash;        </el-col>&ndash;&gt;-->
<!--&lt;!&ndash;        <el-col :span="1.5">&ndash;&gt;-->
<!--&lt;!&ndash;          <el-button&ndash;&gt;-->
<!--&lt;!&ndash;            type="warning"&ndash;&gt;-->
<!--&lt;!&ndash;            icon="el-icon-upload"&ndash;&gt;-->
<!--&lt;!&ndash;            size="mini"&ndash;&gt;-->
<!--&lt;!&ndash;            @click="handleUpload"&ndash;&gt;-->
<!--&lt;!&ndash;          >导入</el-button>&ndash;&gt;-->
<!--&lt;!&ndash;          <input&ndash;&gt;-->
<!--&lt;!&ndash;            ref="excel"&ndash;&gt;-->
<!--&lt;!&ndash;            style="display: none;"&ndash;&gt;-->
<!--&lt;!&ndash;            type="file"&ndash;&gt;-->
<!--&lt;!&ndash;            @change="fileImport"&ndash;&gt;-->
<!--&lt;!&ndash;            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"&ndash;&gt;-->
<!--&lt;!&ndash;          >&ndash;&gt;-->
<!--&lt;!&ndash;        </el-col>&ndash;&gt;-->
<!--        <right-toolbar :showSearch.sync="showSearchView" @queryTable="reloadView"></right-toolbar>-->
<!--      </el-row>-->
<!--      <el-tabs type="border-card" v-model="activeName"  @tab-click="handleClick">-->
<!--        <el-tab-pane name="1"  label="信息填报">-->
<!--          <my-table :show-radio="false" :multiselect=false  url="/reports/responsiblePersion/page" :fixed="true" ref="responsiblePersionTable" row-key="id" :height="320">-->
<!--            <el-table-column  label="河道名称" min-width="110" fixed="left" prop="reverName" sortable="custom" header-align="center" align="center" column-key="REVER_NAME"></el-table-column>-->
<!--            <el-table-column  label="所在市" min-width="120" prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>-->
<!--            <el-table-column  label="所在县/区" min-width="120" prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>-->
<!--            <el-table-column  label="起始位置" min-width="160" prop="startAddr" sortable="custom" header-align="center" align="center" column-key="START_ADDR"></el-table-column>-->
<!--            <el-table-column  label="终止位置" min-width="160" prop="endAddr" sortable="custom" header-align="center" align="center" column-key="END_ADDR"></el-table-column>-->
<!--            <el-table-column  label="县级责任人" min-width="350" prop="responsibleXian" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIAN">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleXian}} , {{scope.row.positionXian}} , {{scope.row.mobileXian}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrXian}} - {{scope.row.endAddrXian}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="乡级责任人" min-width="350" prop="responsibleXiang" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIANG">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleXiang}} , {{scope.row.positionXiang}} , {{scope.row.mobileXiang}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrXiang}} - {{scope.row.endAddrXiang}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="村级责任人" min-width="350" prop="responsibleCun" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_CUN">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleCun}} , {{scope.row.positionCun}} , {{scope.row.mobileCun}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrCun}} - {{scope.row.endAddrCun}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="县级水行政主管部门责任人" min-width="350" prop="responsibleCompetentDept" header-align="center" align="center" sortable="custom" column-key="RESPONSIBLE_COMPETENT_DEPT">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleCompetentDept}} , {{scope.row.positionCompetentDept}} , {{scope.row.mobileCompetentDept}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrCompetentDept}} - {{scope.row.endAddrCompetentDept}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="现场监管责任人" min-width="350" prop="responsibleSupervise" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_SUPERVISE">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleSupervise}} , {{scope.row.positionSupervise}} , {{scope.row.mobileSupervise}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrSupervise}} - {{scope.row.endAddrSupervise}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="行政执法责任人" min-width="350" prop="responsibleEnforce" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_ENFORCE">-->
<!--              <template  #default="scope">-->
<!--                <div>-->
<!--                  {{scope.row.responsibleEnforce}} , {{scope.row.positionEnforce}} , {{scope.row.mobileEnforce}}-->
<!--                </div>-->
<!--                <div>-->
<!--                  {{scope.row.startAddrEnforce}} - {{scope.row.endAddrEnforce}}-->
<!--                </div>-->
<!--              </template>-->
<!--            </el-table-column>-->
<!--            <el-table-column  label="备注" min-width="150" prop="remark" sortable="custom" header-align="center" align="center" column-key="REMARK"></el-table-column>-->
<!--          </my-table>-->
<!--        </el-tab-pane>-->
<!--        <el-tab-pane name="2"  label="操作记录">-->
<!--          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">-->
<!--            <th style="width: 30%">操作时间</th>-->
<!--            <th>操作人</th>-->
<!--            <th>操作状态</th>-->
<!--            <th>操作备注</th>-->
<!--            <tr align="center" v-for="(item,index) in processList" :key="item.id">-->
<!--              <td style="width: 30%">{{item.createTime}}</td>-->
<!--              <td >{{item.createUser}}</td>-->
<!--              <td >-->
<!--                <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>-->
<!--              </td>-->
<!--              <td >{{item.opinion}}</td>-->
<!--            </tr>-->
<!--          </table>-->

<!--        </el-tab-pane>-->
<!--      </el-tabs>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button type="primary" @click="submit">审核通过</el-button>-->
<!--        <el-button @click="cancel">审核不通过</el-button>-->
<!--      </div>-->
<!--    </my-dialog>-->
    <responsibleDetils
      :detailsOpen="detailsOpen"
      @close="close"
      :report-id="reportId"
      :status="status"
      :isAudit="true"
      :title="title"
      :process-list="processList"
      :version="String(version)"/>
  </div>
</template>

<script>
import {
  getResponsiblePersionReport,
  delResponsiblePersionReport,
  delResponsiblePersionReportBatch,
  addResponsiblePersionReport,
  updateResponsiblePersionReport,
  importResponsiblePersionReport, checkPassXian
} from '@/api/reports/responsiblePersionReport'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyQuarterSelect from '@/components/YB/MyQuarterSelect.vue'
import Template from '@/views/sms/template/index.vue'
import { getProcessList } from '@/api/reports/responsiblePersion'
import ResponsibleDetils from '@/views/reports/commponents/responsibleDetils.vue'

export default {
  name: "responsiblePersionCountyExamine",
  components: { ResponsibleDetils, Template, MyQuarterSelect, MyAreaSelect },
  data() {
    return {
      detailsOpen:false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showSearchView: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        status: '',
        areaCode: '',
      },
      params:{},
      queryParamsView: {},
      // 表单参数
      responsiblePersionReport: {},
      reportId:'',
      version:'',
      status:'',
      activeName:'1',
      processList:[]
    };
  },
  mounted() {
  },
  methods: {
    close(){
      this.detailsOpen = false;
      this.$refs.responsiblePersionReportTable &&this.$refs.responsiblePersionReportTable.search(this.queryParams);
      this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.search(this.queryParamsView);
    },
    /** 查询采砂监管责任人填报表列表 */
    reload(restart) {
      this.$refs.responsiblePersionReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 表单重置
    reset() {
      this.responsiblePersionReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        year: '',
        quarter: '',
        status: '',
        reportUserId: '',
        reportTime: '',
        version: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.myQuarterSelect.resetQuery();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.responsiblePersionReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采砂监管责任人填报表";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        if(row.id) {
          return delResponsiblePersionReport(row.id);
        }else{
          return delResponsiblePersionReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleFill(row){
      this.detailsOpen = true;
      this.title = "采砂监管责任人县级审核";
      this.responsiblePersionReport = row;
      this.reportId = row.id;
      this.status = row.status;
      this.version = row.version;
      this.handleClick()
    },
    handleClick(){
      this.processList = []
      getProcessList(this.reportId).then(res=>{
        this.processList = res.list
      })
    },
    handleChange(value){
      this.queryParams.year = value.year;
      this.queryParams.quarter =  value.quarter;
    },
    change(e){
      this.$forceUpdate()
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.responsiblePersionReportTable && this.$refs.responsiblePersionReportTable.changeTableHeight();
    this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.changeTableHeight();
  },
};
</script>
<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
