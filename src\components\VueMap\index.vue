<template>
  <div>
    <!-- <div id="appMap">
      <input v-model="address" @keyup.enter="searchAddress" placeholder="输入地址搜索" />
      <div id="map" style="width: 100%; height: 500px;"></div>
    </div> -->
    <baidu-map class="bm-view" scroll-wheel-zoom :center="location" :zoom="zoom" @ready="mapReady">
      <bm-view class="map" style="width: 100%; height:100%; flex: 1"></bm-view>
      <!-- 自定义控件 -->
      <bm-control :offset="{width: '50px', height: '10px'}">
        <!-- 自动填充 -->
        <bm-auto-complete :sugStyle="{zIndex: 999999}" @confirm="handleConfirm">
          <el-input v-model="inputValue" placeholder="输入关键字进行模糊查询" @change="confirmAddress"></el-input>
        </bm-auto-complete>
      </bm-control>
      <!-- 手动放大缩小到省市街道 位置：右下方-->
      <bm-navigation anchor="BMAP_ANCHOR_BOTTOM_RIGHT" />
      <!-- <bm-panorama></bm-panorama> -->
    </baidu-map>
    <span slot="footer" class="dialog-footer">
      <el-button @click="mapTaost = false">取 消</el-button>
      <el-button type="primary" @click="selectBt">确 定</el-button>
    </span>
  </div>
</template>

<script>
  export default {
    data() {
      return { // 百度地图参数
        model: {},
        BMap: {},
        map: {},
        location: {
          lng: 113.93588,
          lat: 22.74894
        },
        zoom: 14,
        inputValue: "",
        keyword: '',
        selectAddress: undefined
      };
    },
    computed: {},
    beforeMount() {},
    beforeDestroy() {},
    mounted() {
      // this.$nextTick(() => {
      //   this.initMap();
      // });
    },
    methods: {
      selectBt() {
        console.log(this.model.address, this.location)
        let dizhi = this.model.address || this.selectAddress;
        let location = {
          lng: 113.93588,
          lat: 22.74894
        };
        let myGeo = new this.BMap.Geocoder()
        myGeo.getPoint(dizhi, function(point) {
          if (point) {
            console.log(point, '逆地址解析',dizhi)
            // that.map.centerAndZoom(point,10)
            location.lat = point.lat
            location.lng = point.lng
            // that.location.infoWindowShow=true
          }
        })
        console.log(dizhi)
      },
      handleConfirm(e) {
        // 处理用户选择的地址信息
        console.log(e);
        this.model.address = undefined;
        this.selectAddress = e.item.value.city + e.item.value.district + e.item.value.business;
      },
      // 搜索框的搜索事件
      confirmAddress(e) {
        console.log("this.model.address:", this.map)
        if (this.inputValue != '') {
          // console.log("搜索字段为:" + this.inputValue)
          this.keyword = this.inputValue
        }

        //为啥使用延时？？
        //因为上面搜索框是change事件，变化的太快了看起来效果不好所以添加了延时
        setTimeout(() => {
          //搜索时把需要标点的地址传入local.search中
          var local = new BMap.LocalSearch(this.map, {
            renderOptions: {
              map: this.map
            }
          });
          local.search(this.keyword);
        }, 600)
      },


      // baidu-map组件的ready事件
      mapReady({
        BMap,
        map
      }) {
        this.BMap = BMap;
        this.map = map;

        let geocoder = new BMap.Geocoder(); //创建地址解析器的实例
        if (this.model.hasOwnProperty('address')) { //如果当前model中包含address 则证明是修改弹框里面的地址数据（地址存在，打开弹框显示地址标点）
          this.keyword = this.model.address;
          this.inputValue = this.model.address;
        } else { //否则显示默认标点（这里的经纬度代表成都）
          //第二个参数  10 代表地图缩 放级别,最大为19,最小为0

          setTimeout(() => {
            this.map.centerAndZoom(new BMap.Point(114.514793, 38.042225), 10);
          }, 1000);
          this.keyword = ''
          this.inputValue = ''
        }
        // 点击百度地图上的搜索出来的红色标记点
        map.addEventListener("click", ({
          point
        }) => {
          this.location.lng = point.lng;
          this.location.lat = point.lat;
          this.selectAddress = undefined;
          geocoder.getLocation(point, res => {
            this.inputValue = res.address;
            this.model.address = res.address;
            this.model.storeLongitude = point.lng
            this.model.storeLatitude = point.lat
            this.$forceUpdate();
          });
        });
      },

      // 地区组件的markersset事件
      setAddressList(e) {
        console.log(e);
      },
    },
  };
</script>

<style>
  .bm-view {
    width: 100%;
    height: 400px;
  }

  .address_wrap {
    width: 100%;
    height: 100%;
  }

  ::v-deep .bm-view {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid red;
  }

  ::v-deep .BMap_noprint.anchorTL {
    width: 22%;
    top: 10px !important;
    left: 10px !important;
  }
</style>
