import request from '@/utils/request'

// 查询摄像头表详细
export function getLiveCamera(id) {
  return request({
    url: '/video/liveCamera/info/' + id,
    method: 'post'
  })
}

// 新增摄像头表
export function addLiveCamera(data) {
  return request({
    url: '/video/liveCamera/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改摄像头表
export function updateLiveCamera(data) {
  return request({
    url: '/video/liveCamera/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除摄像头表
export function delLiveCamera(id) {
  return request({
    url: '/video/liveCamera/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除摄像头表
export function delLiveCameraBatch(ids) {
  return request({
    url: '/video/liveCamera/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//部门树状结构
export function getDeptTreeData() {
  return request({
    url: 'video/liveCamera/selectDeptTree',
    method: 'post',
  })
}


