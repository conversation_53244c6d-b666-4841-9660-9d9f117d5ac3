<template>
  <el-row :gutter="10" style="border: 1px solid #DCDFE6;border-radius: 4px;">
    <!-- 关键字搜索 -->
    <el-col :span="12" v-if="!disabled">
      <el-form-item
        label="关键字"
        label-width="80px"
        style="margin-bottom: 0;width: 100%;margin-top: 22px"
      >
        <el-autocomplete
          :clearable="true"
          style="width: 100%;"
          v-model="searchValue"
          :fetch-suggestions="querySearch"
          placeholder="请输入关键字"
          :trigger-on-focus="false"
          @select="handleSelect"
          @clear="clearSearch"
        >
          <template slot-scope="data">
            <div>
              <div>
                {{ JSON.stringify(data.item.value).replaceAll("\"", "") }}
              </div>
              <div style="color: #8c8c8c;font-size: 12px;">
                {{ JSON.stringify(data.item.address).replaceAll("\"", "") }}
              </div>
            </div>
          </template>
        </el-autocomplete>
      </el-form-item>
    </el-col>

    <!-- 切换地图模式 -->
    <el-col v-if="switchMode" :span="6" >
      <el-form-item
        label="地图模式"
        label-width="80px"
        style="margin-bottom: 0;width: 100%;margin-top: 22px"
      >
        <el-radio-group
          v-model="viewMode"
          @input="changeViewMode"
        >
          <el-radio-button label="2D"></el-radio-button>
          <el-radio-button label="3D"></el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-col>

    <!-- 切换地图类型 -->
    <el-col v-if="switchType" :span="6" >
      <el-form-item
        style="margin-bottom: 0;width: 100%;margin-top: 22px"
      >
        <el-switch
          v-model="viewType"
          active-text="卫星图"
          inactive-text="矢量图"
          @change="changeViewType"
        >
        </el-switch>
      </el-form-item>
    </el-col>

    <!-- 腾讯地图组件 -->
    <div id="mapContainer" class="mapContainer"></div>
  </el-row>
</template>

<script>
import TMap from "@/utils/initMap";
import {jsonp} from 'vue-jsonp';
import marker from "@/assets/icons/map/marker.png";
import { createLogger } from 'vuex'

export default {
  name: "MyTencentMap",
  emits: ["change"],
  props: {
    //是否可编辑
    disabled:{
      type:Boolean,
      defualt: false,
    },
    // 地图中心点
    center: {
      type: Object,
      default: function () {
        return {lat: 38.041323, lng: 114.514686};
      }
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10
    },
    // 是否移除指南针
    removeCompass: {
      type: Boolean,
      default: false,
    },
    // 是否移除缩放
    removeZoom: {
      type: Boolean,
      default: false,
    },
    // 是否开启模式切换
    switchMode: {
      type: Boolean,
      default: false,
    },
    // 是否开启类型切换
    switchType: {
      type: Boolean,
      default: false,
    },
    // 以城市为中心搜索 默认石家庄市
    region: {
      type: String,
      default: '石家庄'
    },
    // 经度
    lat: {
      type: Number,
      default: null,
    },
    // 纬度
    lng: {
      type: Number,
      default: null,
    }
  },
  data() {
    return {
      tencentMapKey: '',  //腾讯地图密钥
      tencentWebServiceUrlPrefix: '',  //腾讯地图api请求前缀
      tMap: null, //腾讯地图原型
      TXMap: null,  //腾讯地图实例
      innerCenter: {},  //默认中心点
      innerZoom: null,  //默认缩放等级
      viewMode: '2D',  //地图模式
      viewType: false,  //地图类型
      searchValue: '',  //关键字搜索
      markerGeometries: [],  //标记点数据
      infoWindow: null,  //信息窗口实例
      // 抛出属性-标记点信息
      positionData: {
        latitude: '',
        longitude: '',
        address: '',
      },
    }
  },
  created() {
    //获取腾讯地图apk和请求前缀
    this.getConfigValue("tencent.map.key").then(tencentMapKey => {
      this.tencentMapKey = tencentMapKey;
      const Geolocation_URL = "https://mapapi.qq.com/web/mapComponents/geoLocation/v/geolocation.min.js";
      // 插入script脚本
      let script = document.createElement("script");
      script.setAttribute("type", "text/javascript");
      script.setAttribute("src", Geolocation_URL);
      document.body.appendChild(script);
    }).then(() => {
      this.getConfigValue("tencent.webService.urlPrefix").then(tencentWebServiceUrlPrefix => {
        this.tencentWebServiceUrlPrefix = tencentWebServiceUrlPrefix;
      });
    })
  },
  mounted() {
    //设置中心点和缩放等级
    this.innerCenter = this.center;
    this.innerZoom = this.zoom;
    //初始化腾讯地图
    TMap.init().then((TMap) => {
      this.tMap = TMap;
      this.TXMap = new TMap.Map("mapContainer", {
        //设置地图中心点坐标 默认石家庄市
        center: new TMap.LatLng(this.innerCenter.lat, this.innerCenter.lng),
        zoom: this.zoom, //设置地图缩放级别
        viewMode: this.viewMode,
      });
      if (this.removeCompass) {
        //移除腾讯地图旋转控件
        this.TXMap.removeControl(this.tMap.constants.DEFAULT_CONTROL_ID.ROTATION);
      }
      if (this.removeZoom) {
        //移除腾讯地图缩放控件
        this.TXMap.removeControl(this.tMap.constants.DEFAULT_CONTROL_ID.ZOOM);
      }
      //绑定地图点击事件
      this.mapClick();

      //针对回显数据 延时处理展示在地图上
      setTimeout(()=> {
        if (this.lng && this.lat) {
          this.mapMarkByParent();
        }
      },1000);
    });

    //获取地图中心点
    var that=this;
    var timer=setInterval(function(){
      if (qq) {
        that.getgetLocation()
        clearInterval(timer)
      }
    },100)

  },
  /** 监听父层经纬度输入框变化 */
  watch: {
    lng(nval, oval) {
      if (this.lng && this.lat) {
        if (this.lng >= 113 && this.lng <= 120 && this.lat >= 36 && this.lat <= 43) {
          this.mapMarkByParent();
        }
      } else {
        this.searchValue = '';
        this.restore();
      }
    },
    lat(nval, oval) {
      if (this.lng && this.lat) {
        if (this.lng >= 113 && this.lng <= 120 && this.lat >= 36 && this.lat <= 43) {
          this.mapMarkByParent();
        }
      } else {
        this.searchValue = '';
        this.restore();
      }
    }
  },
  methods: {
    getgetLocation(){
      let geolocation = new qq.maps.Geolocation(this.tencentMapKey, 'myMap');
      console.log(geolocation)
      geolocation.getLocation((position)=>{
        console.log(position)
        if(position){
          if((position.lng <= 120 && position.lng >= 113 )&& (position.lat <= 42 && position.lat >= 35)){
            this.mapTranslate(500, {lat: position.lat,lng: position.lng},);
          }
        }
      })
    },
    /** 输入关键字获取全称 */
    async querySearch(queryString, cb) {
      try {
        let params = {
          region: this.region,
          keyword: queryString,
          key: this.tencentMapKey,
        }
        let res = await this.keywordSearch(params);
        let {data} = res;
        const results = data.map(item => {
          return {
            value: item.title,
            address: item.address,
            city: item.city,
            province: item.province,
            location: item.location
          }
        })
        cb(results);
        return results;
      } catch (e) {
        console.log(e);
      }
    },

    /**
     * 调用接口获取地址提示
     * GET请求示例，注意参数值要进行URL编码
     * 请求地址：https://apis.map.qq.com/ws/place/v1/suggestion
     */
    keywordSearch(params) {
      return jsonp(this.tencentWebServiceUrlPrefix + '/place/v1/suggestion', {
        output: 'jsonp',
        region: params.region,
        region_fix: 0,
        keyword: params.keyword,
        key: params.key,
        page_index: 1,
        page_size: 20
      });
    },

    /** 选择关键字下拉提示的选项 */
    handleSelect(e) {
      this.mapTranslate(500, {lat: e.location.lat,lng: e.location.lng});
      this.markerGeometries = [{
          id: '1',
          styleId: 'myStyle',
          position: new this.tMap.LatLng(e.location.lat, e.location.lng),
          properties: {
            address: e.address,
            city: e.city,
            province: e.province,
          }
      }];
      this.renderMarker();
      this.initWindow(e.location.lat, e.location.lng, e.address);
      this.positionData = {
        latitude: e.location.lat,
        longitude: e.location.lng,
        address: e.address,
      };
      this.afterThrowData();
    },

    /** 清空关键字搜索 */
    clearSearch() {
      this.restore();
      this.afterThrowData();
    },

    /** 还原操作 */
    restore() {
      this.markerGeometries = [];
      this.renderMarker();
      this.closeWindow();
      this.mapTranslate(500, this.center);
      this.positionData = {
        latitude: '',
        longitude: '',
        address: '',
      }
    },

    /** 平移动画事件 */
    mapTranslate(time, center, zoom, rotation, pitch) {
      this.TXMap.easeTo({
        center: new this.tMap.LatLng(center.lat, center.lng),
        zoom: zoom ? zoom : this.TXMap.getZoom(),
        rotation: rotation ? rotation : 0,
        pitch: pitch ? pitch : 0
      }, {
        duration: time
      });
    },

    /** 渲染点标记点 */
    renderMarker() {
      if (this.marker) {
        this.marker.setMap(null);
        this.marker = null;
      }
      this.marker = new this.tMap.MultiMarker({
        id: "marker-layer",
        //指定地图容器
        map: this.TXMap,
        //样式定义
        styles: {
          //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
          "myStyle": new this.tMap.MarkerStyle({
            "width": 35,  // 点标记样式宽度（像素）
            "height": 35, // 点标记样式高度（像素）
            "src": marker,  //图片路径
            "anchor": {x: 16, y: 32},  //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
          })
        },
        //点标记数据数组
        geometries: this.markerGeometries
      });

      //给标识添加点击事件
      // this.marker.on("click", (event) => {
      //   let {lat, lng} = event.latLng;
      //   let {address, city, province} = event.geometry.properties;
      //   this.initWindow(lat, lng, address);
      // });
    },

    /** 加载点标记的信息窗体 */
    initWindow(lat, lng, address) {
      // 一次只能打开一个信息窗体
      if (this.infoWindow) {
        this.closeWindow();
      }

      // 创建信息窗体实例
      this.infoWindow = new this.tMap.InfoWindow({
        map: this.TXMap,
        position: new this.tMap.LatLng(lat, lng),
        content: '<div style=\'text-align:left\'>' +
          '地址：' + address + '<br>' +
          '经度：' + lng + '<br>' +
          '纬度：' + lat +
          '</div>',
        offset: { x: 1.5, y: -35 }, //设置信息窗相对position偏移像素
      });

      // 信息窗口关闭回调
      this.infoWindow.on("closeclick", () => {
        console.log("当前信息窗口已关闭");
      });

      // 打开信息窗口
      this.infoWindow.open();
    },

    /** 关闭点标记信息窗体 */
    closeWindow() {
      if (this.infoWindow) {
        this.infoWindow.close();
      }
    },

    /** 绑定事件：点击地图标记点 */
    mapClick() {
      if (this.disabled) {
        return;
      }
      let that = this;
      this.TXMap.on("click", function (e) {
        that.searchValue = '';
        let params = {
          location: e.latLng.lat + ',' + e.latLng.lng,
          key: that.tencentMapKey,
        }
        that.getAddressAndMapMark(params);
      });
    },

    /**
     * 根据经纬度获取详细地址并在地图上标点
     * GET请求示例，注意参数值要进行URL编码
     * 请求地址：https://apis.map.qq.com/ws/geocoder/v1/?location=
     */
    getAddressAndMapMark(params) {
      //解析坐标获取详细地址
      jsonp(this.tencentWebServiceUrlPrefix + '/geocoder/v1/?location=', {
        output: 'jsonp',
        location: params.location,
        key: params.key
      }).then(res => {
        if (res.status === 0) {
          this.markerGeometries = [{
            id: '1',
            styleId: 'myStyle',
            position: new this.tMap.LatLng(res.result.location.lat, res.result.location.lng),
            properties: {
              address: res.result.address,
              city: res.result.address_component.city,
              province: res.result.address_component.province,
            }
          }];
          this.renderMarker();
          this.initWindow(res.result.location.lat, res.result.location.lng, res.result.address);
          this.positionData.latitude = res.result.location.lat;
          this.positionData.longitude = res.result.location.lng;
          this.positionData.address = res.result.address;
          this.afterThrowData();
        }
      });
    },

    /** 选好标记点后抛出数据 */
    afterThrowData() {
      this.$emit("change", this.positionData);
    },

    /** 根据父级传的经纬度进行点标记 */
    mapMarkByParent() {
      let params = {
        location: this.lat + ',' + this.lng,
        key: this.tencentMapKey,
      }
      this.getAddressAndMapMark(params);
      this.mapTranslate(500, {lat: this.lat, lng: this.lng});
    },

    /** 切换地图模式操作 */
    changeViewMode(val) {
      if (val === '2D') {
        this.TXMap.setViewMode('2D');
      } else if (val === '3D') {
        this.TXMap.setViewMode('3D');
        this.TXMap.setPitch(60);
      }
    },

    /** 切换地图类型操作 */
    changeViewType(flag) {
      if (!flag) {
        this.TXMap.setBaseMap({type:'vector'});
      } else {
        this.TXMap.setBaseMap({type:'satellite'});
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search {
  position: absolute;
  top: 10px;
  left: 0;
  z-index: 1001;
  display: flex;
  align-items: center;
}

.mapMode {
  position: absolute;
  top: 10px;
  left: 320px;
  right: 10px;
  z-index: 1001;
  display: flex;
  align-items: center;
}

.mapType {
  position: absolute;
  top: 10px;
  left: 540px;
  right: 10px;
  z-index: 1001;
  display: flex;
  align-items: center;
}

.mapContainer {
  margin-top: 80px;
  width: 100%;
  height: 420px;
}
</style>

<style>
.el-autocomplete-suggestion li {
  padding: 0 10px!important;
}

canvas {
  vertical-align: bottom;
}
</style>
