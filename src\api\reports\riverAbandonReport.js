import request from '@/utils/request'

// 查询区域项目统计表详细
export function getAreaStat(id) {
  return request({
    url: '/reports/areaStat/info/' + id,
    method: 'post'
  })
}

// 新增区域项目统计表
export function addAreaStat(data) {
  return request({
    url: '/reports/areaStat/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改区域项目统计表
export function updateAreaStat(data) {
  return request({
    url: '/reports/areaStat/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除区域项目统计表
export function delAreaStat(id) {
  return request({
    url: '/reports/areaStat/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除区域项目统计表
export function delAreaStatBatch(ids) {
  return request({
    url: '/reports/areaStat/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


