<template>
  <div>
    <el-upload
      action="#"
      ref="uploadRef"
      v-if="(fileArr&&fileArr.length>0)||!disabled"
      :limit="1"
      :multiple="multiple"
      :disabled="disabled"
      :accept="fileType"
      :class="multiple?(disabled?'hidden-upload':''):(((fileArr&&fileArr.length===1)||disabled)?'hidden-upload':'')"
      :file-list="innerFileList"
      :http-request="handleUpload"
      :before-upload="handleDetect"
      :on-success="handleSuccess"
      :on-preview="handleDownload"
      :on-remove="handleRemove"
    >
      <el-tooltip class="box-item" effect="dark" :content="'支持文件格式（' + fileType + '）'" placement="right">
        <el-button type="primary" icon="Upload">选择文件</el-button>
      </el-tooltip>
    </el-upload>

    <div v-else>暂无附件</div>
  </div>
</template>

<script>
import {uploadFileList} from "@/api/system/common";
export default {
  name: "MyFileUpload",
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    pathFieldName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    fileType: {
      type: String,
      default: '.doc,.docx,.pdf'
    }
  },
  data() {
    return {
      fileArr: [],
      innerFileList: []
    }
  },
  methods: {
    /** 文件上传前 验证格式 */
    handleDetect(file) {
      const detectType = file.name.substring(file.name.lastIndexOf(".") + 1, file.name.length).toLowerCase();
      if (this.fileType.indexOf(detectType) !== -1) {
        return true;
      } else {
        this.$modal.msgError("文件格式错误，请上传正确格式的文件！");
        return false;
      }
    },

    /** 文件上传中 */
    handleUpload(params) {
      const file = params.file;
      const formData = new FormData();
      formData.append('file', file);
      this.$modal.loading("正在上传文件，请稍候...");
      uploadFileList(formData, this.pathFieldName).then(res => {
        params.onSuccess(res);
        this.$modal.closeLoading();
      }).catch(error => {
        params.onError(error);
        this.$modal.closeLoading();
      });
    },

    /** 上传成功回调函数 */
    handleSuccess(res, file) {
      file.uploadFile = res.file;
      this.fileArr=[file.uploadFile];
      this.$emit("getFileList", this.fileArr);
    },

    /** 删除文件操作 */
    handleRemove(file) {
      for (let index in this.fileArr) {
        if (file.uploadFile.id === this.fileArr[index].id) {
          this.fileArr.splice(index, 1);
          break;
        }
      }
      this.$emit("getFileList", this.fileArr);
    },

    /** 下载文件操作 */
    handleDownload(file) {
      this.download("/com/uploadfile/download/" + file.uploadFile.id, {}, file.uploadFile.uploadFileName);
    }
  },
  mounted() {
    //初始化回显fileList
    // console.log(this.fileList,'这里？？？？？')
    // if (this.fileList && this.fileList.length > 0) {
    //   this.fileArr = this.fileList;
    //   this.innerFileList = this.fileList.map(item => {
    //     return {
    //       name: item.uploadFileName,
    //       url: item.uploadFilePath,
    //       uploadFile: item,
    //     }
    //   });
    //   console.log(this.innerFileList,'这里没执行')
    // } else {
    //   this.fileArr = [];
    // }
  },
  watch: {
    fileList(nval, oval) {
      if (nval && nval.length > 0) {
           this.$nextTick(() => {
                this.fileArr = nval;
                 this.innerFileList = nval;
                console.log(nval,'这里没执行111')
              });
      } else {
        this.$nextTick(() => {
          console.log('这执行了111')
        this.fileArr = [];
        this.innerFileList = [];
        });
      }
    }
  }
}
</script>

<style>
.el-upload-list {
  margin: 0;
}

.el-upload-list__item {
  margin-bottom: 0;
}

.hidden-upload .el-upload-list__item:first-child {
  margin-top: 5px;
}

.hidden-upload .el-upload {
  display: none;
}
</style>

