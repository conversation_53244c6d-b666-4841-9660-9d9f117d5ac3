import request from '@/utils/request'

//获取菜单树
export function getMenuTreeData() {
  return request({
    url: '/sys/menu/select',
    method: 'post',
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/sys/menu/save',
    method: 'post',
    data: data
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/sys/menu/update',
    method: 'post',
    data: data
  })
}

// 删除菜单
export function delMenu(menuId) {
  return request({
    url: '/sys/menu/delete/' + menuId,
    method: 'post'
  })
}

//获取所有角色列表
export function allRoleList() {
  return request({
    url: '/sys/role/select',
    method: 'post'
  })
}

//保存角色
export function updateRoles(roleMenuObj) {
  return request({
    url:'sys/menu/updateRoles',
    method: 'post',
    data: roleMenuObj,
  })
}

