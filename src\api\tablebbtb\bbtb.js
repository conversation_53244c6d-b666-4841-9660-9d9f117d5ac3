import request from "@/utils/request";
export function getTbaleData(query) {
  return request({
    url: "/statement/page",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
// 市县级获取审批数据
export function getToexamineData(query) {
  return request({
    url: "/auditFlow/page",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
export function getAreaName(query) {
  return request({
    url: "/statement/getAreaAndDept",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
// 删除多条数据
export function getDeleteS(query) {
  return request({
    url: "/statement/deleteByIds",
    method: "post",
    data: query,
    // showLoading: true,
  });
}
// 删除单条数据
export function getDelete(data) {
  return request({
    url: "/statement/deleteById",
    method: "post",
    params: data,
    // showLoading: true,
  });
}
// 提交非法采砂表单数据
export function addReportForms(data) {
  return request({
    url: '/statement/insert',
    method: 'post',
    data: data
  })
}
// 修改非法采砂表单数据
export function modifyReportForms(data) {
  return request({
    url: '/statement/updateStatement',
    method: 'post',
    data: data
  })
}
// 获取非法采砂单条数据
export function getFormsId(query) {x
  return request({
    url: "/statement/get",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
// 修改非法采砂审核状态
export function examineType(data) {
  return request({
    url: '/auditFlow/update',
    method: 'post',
    params: data
  })
}
//查询非法采砂台账
export function getTableDataNew(query) {
  return request({
    url: "/statement/pageStatement",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//查询非法采砂情况统计表
export function getTableDataDetails(query) {
  return request({
    url: "/statement/getByAreaName",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//查询非法采砂全年统计表
export function getTableDataDetailsYear(query) {
  return request({
    url: "/statement/getByYear",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//附件上传
export function upFiledId(data) {
  return request({
    url: '/statement/getUploadFileByFileId',
    method: 'post',
    data: data
  })
}
//附件查询
export function getUploadFile(query) {
  return request({
    url: "/statement/getUploadFile",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//修改是否结案
export function updateCaseClosed(data) {
  return request({
    url: '/statement/updateCaseClosed',
    method: 'post',
    data: data
  })
}
//获取异常采运单
export function getBillData(data) {
  return request({
    url: '/bill/bill/page',
    method: 'post',
    data: data
  })
}
//获取项目列表
export function getProject(query) {
  return request({
    url: "/indexStat/getProjectId",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//获取市区县
export function getCitys(query) {
  return request({
    url: "/statement/getFileName",
    method: "GET",
    params: query,
    // showLoading: true,
  });
}
//修改项目状态
export function reProjectStatus(data) {
  return request({
    url: '/project/project/updateProjectStatus',
    method: 'post',
    data: data
  })
}
