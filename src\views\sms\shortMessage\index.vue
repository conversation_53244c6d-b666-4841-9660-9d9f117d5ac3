<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item prop="mobile" label="手机号码">
        <el-input v-model="queryParams.mobile" placeholder="请输入手机号码" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item prop="content" label="短信内容">
        <el-input v-model="queryParams.content" placeholder="请输入短信内容" clearable style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="primary"-->
      <!--          icon="el-icon-plus"-->
      <!--          size="mini"-->
      <!--          @click="handleAdd"-->
      <!--          v-hasPermi="['sms:shortMessage:save']"-->
      <!--        >新增</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-view" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['sms:shortMessage:update']">查看</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['sms:shortMessage:delete']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/sms/shortMessage/page" ref="shortMessageTable" row-key="id"
      @my-selection-change="handleSelectionChange">
      <el-table-column label="手机号码" min-width="80" prop="mobile" sortable="custom"
        column-key="MOBILE"></el-table-column>
      <el-table-column label="短信签名" min-width="80" prop="signName" sortable="custom"
        column-key="SIGN_NAME"></el-table-column>
      <el-table-column label="模板名称" min-width="80" prop="templateName" sortable="custom"
        column-key="TEMPLATE_NAME"></el-table-column>
      <el-table-column label="模板编码" min-width="80" prop="templateCode" sortable="custom"
        column-key="TEMPLATE_CODE"></el-table-column>
      <el-table-column label="短信内容" min-width="80" prop="content" sortable="custom"
        column-key="CONTENT"></el-table-column>
      <el-table-column label="发送状态" min-width="80" prop="status" sortable="custom">
      </el-table-column>
      <el-table-column label="短信类型" min-width="80" prop="type" sortable="custom">
      </el-table-column>
      <el-table-column label="发送时间" min-width="80" prop="sendTime" sortable="custom"
        column-key="SEND_TIME"></el-table-column>
      <el-table-column label="接收时间" min-width="80" prop="receiveTime" sortable="custom"
        column-key="RECEIVE_TIME"></el-table-column>
      <el-table-column label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button size="mini" title="查看" type="success" class="btn-table-operate" icon="el-icon-view"
            @click="handleUpdate(scope.row)" v-hasPermi="['sms:shortMessage:update']"></el-button>
          <el-button size="mini" title="删除" type="danger" class="btn-table-operate" icon="el-icon-delete"
            @click="handleDelete(scope.row)" v-hasPermi="['sms:shortMessage:delete']"></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改短信表 对话框 -->
    <my-dialog v-el-drag-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="shortMessage" label-width="100px">
        <el-row>
          <el-col :span="12">
            <my-form-item label="手机号码" prop="mobile" :rules="[{ notNull: true, message: '请输入手机号码' }]">
              <my-input :value="shortMessage.mobile" readonly placeholder="请输入手机号码" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="短信签名" prop="signName" :rules="[{ notNull: true, message: '请输入短信签名' }]">
              <my-input :value="shortMessage.signName" readonly placeholder="请输入短信签名" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="模板名称" prop="templateId" :rules="[{ notNull: true, message: '请输入模板名称' }]">
              <my-input :value="shortMessage.templateName" readonly placeholder="请输入模板名称" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="模板编码" prop="templateCode" :rules="[{ notNull: true, message: '请输入模板编码' }]">
              <my-input :value="shortMessage.templateCode" readonly placeholder="请输入模板编码" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="模板参数" prop="templateParams" :rules="[{ notNull: true, message: '请输入模板参数' }]">
              <el-input type="textarea" id="templateParams" class="form-control"
                :value="JSON.stringify(shortMessage.templateParams)" placeholder="请输入模板参数" readonly rows="5"
                style="width:380px"></el-input>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="短信内容" prop="content" :rules="[{ notNull: true, message: '请输入短信内容' }]">
              <el-input type="textarea" id="content" class="form-control" :value="shortMessage.content"
                placeholder="请输入短信内容" readonly rows="5" style="width:380px"></el-input>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="业务ID" prop="ownerId" :rules="[{ notNull: true, message: '请输入业务ID' }]">
              <my-input :value="shortMessage.ownerId" readonly placeholder="请输入业务ID" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="发送回执ID" prop="bizId" :rules="[{ notNull: true, message: '请输入发送回执ID' }]">
              <my-input :value="shortMessage.bizId" readonly placeholder="请输入发送回执ID" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <my-form-item label="响应内容" prop="response" :rules="[{ notNull: true, message: '请输入响应内容' }]">
              <el-input type="textarea" id="templateParams" class="form-control"
                :value="JSON.stringify(shortMessage.response)" placeholder="请输入模板参数" readonly rows="5"></el-input>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="字数" prop="charCount" :rules="[{ notNull: true, message: '请输入字数' }]">
              <my-input :value="shortMessage.charCount" readonly placeholder="请输入字数" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="计费条数" prop="smsCount" :rules="[{ notNull: true, message: '请输入计费条数' }]">
              <my-input :value="shortMessage.smsCount" readonly placeholder="请输入计费条数" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="发送状态" prop="status" :rules="[{ notNull: true, message: '请输入发送状态' }]">
              <my-input readonly>
                <my-view pvalue="shortMessageStatus" :value="shortMessage.status"></my-view>
              </my-input>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="短信类型" prop="type" :rules="[{ notNull: true, message: '请输入短信类型 验证码，短信通知' }]">
              <my-input readonly>
                <my-view pvalue="shortMessageType" :value="shortMessage.type"></my-view>
              </my-input>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="发送时间" prop="sendTime" :rules="[{ notNull: true, message: '请输入发送时间' }]">
              <my-input :value="shortMessage.sendTime" readonly placeholder="请输入发送时间" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="接收时间" prop="receiveTime" :rules="[{ notNull: true, message: '请输入接收时间' }]">
              <my-input :value="shortMessage.receiveTime" readonly placeholder="请输入接收时间" />
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <!--        <el-button type="primary" @click="submitForm">确 定</el-button>-->
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getShortMessage, delShortMessage, delShortMessageBatch, addShortMessage, updateShortMessage } from "@/api/sms/shortMessage";
import JsonViewer from 'vue-json-viewer'
import elDragDialog from '@/directive/dialog/drag'
export default {
  name: "ShortMessage",
  components: {
    JsonViewer
  },
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      shortMessage: {},
    };
  },
  methods: {
    /** 查询短信表 列表 */
    reload(restart) {
      this.$refs.shortMessageTable.search(this.queryParams, restart);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.shortMessage = {
        ID: '',
        CREATE_USER_ID: '',
        SEND_TIME: '',
        RECEIVE_TIME: '',
        MOBILE: '',
        SIGN_NAME: '',
        TEMPLATE_ID: '',
        TEMPLATE_CODE: '',
        TEMPLATE_PARAMS: null,
        CONTENT: '',
        OWNER_ID: '',
        SERVICE_BEAN: '',
        BIZ_ID: '',
        RESPONSE: null,
        CHAR_COUNT: null,
        SMS_COUNT: null,
        STATUS: '',
        REPORT_JSON: null,
        TYPE: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.shortMessageTable.getSelectRowKeys()
      this.single = this.ids.length != 1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加短信表 ";
    },
    /** 查看按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getShortMessage(id).then(r => {
        this.shortMessage = r.shortMessage;
        this.open = true;
        this.title = "查看短信表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中')
          if (this.shortMessage.id) {
            updateShortMessage(this.shortMessage).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          } else {
            addShortMessage(this.shortMessage).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function () {
        this.$modal.loading('加载中')
        if (row.id) {
          return delShortMessage(row.id);
        } else {
          return delShortMessageBatch(this.ids);
        }
      }).then(() => {
        this.$modal.closeLoading()
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.log(error)
        this.$modal.closeLoading()
      });
    },
  }
};
</script>
<style>
.jv-container .jv-code {
  overflow: hidden;
  padding: 4px 15px;
}
</style>
<style scoped>
/deep/ .my-dialog__body {
  height: 70vh;
  overflow: auto;
}
</style>
