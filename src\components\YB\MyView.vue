<template>
  <div>
    <slot v-bind:dict="dict">
      <span v-if="!dict.className">{{dict.name}}</span>
      <el-tag v-else :type="dict.className.substring(6,dict.className.length)" effect="plain">{{ dict.name }}</el-tag>
    </slot>
  </div>
</template>

<script>
import DictUtils from "@/utils/dictUtils";

export default {
  name: "MyView",
  props: {
    pvalue: {
      type: String,
    },
    value: {
      type: String
    },
  },
  data() {
    return {
      dictList: [],
    }
  },
  watch: {
    pvalue: function (nVal) {
      if (nVal) {
        DictUtils.cList(nVal).then(r => {
          this.dictList = r[nVal]
        });
      }
    },
  },
  computed:{
    dict: function () {
      if (this.dictList.length == 0) {
        return {value:'', name: ''}
      }
      var dicts = this.dictList.filter(e => e.value == this.value);
      if (dicts.length > 0) {
        return dicts[0];
      }
      return {value:'', name: ''}
    },
  },
  mounted() {
    if (this.pvalue) {
      DictUtils.cList(this.pvalue)
        .then(r => this.dictList = r[this.pvalue]);
    }
  }
}
</script>

<style scoped>

</style>
