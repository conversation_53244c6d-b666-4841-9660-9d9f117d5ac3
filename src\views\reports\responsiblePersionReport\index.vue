<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item>
        <my-quarter-select style="width: 573px;height: 0"  ref="myQuarterSelect"  v-model="params" @change="handleChange"></my-quarter-select>
      </el-form-item>
      <el-form-item label="所属区域" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="数据状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择数据状态"
          pvalue="dataStatus"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          :disabled="!params.quarter||!params.year"
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reports:responsiblePersion:export']"
        >导出数据
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-document-checked"
          size="mini"
          @click="handleDownload"
          v-has-permi="['reports:responsiblePersion:import']"
        >下载模板</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              :cell-style="{padding: '0px'}" :tree-props="{children: 'children'}" url="/reports/responsiblePersionReport/list" :show-pager="false" :default-query-params="queryParams" ref="responsiblePersionReportTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="所在城市" min-width="120" prop="districtName" header-align="center" align="left" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="年度" min-width="120" prop="year" header-align="center" align="center" column-key="YEAR"></el-table-column>
      <el-table-column  label="季度" min-width="120" prop="quarter" header-align="center" align="center" column-key="QUARTER">
        <template  #default="scope">
          <my-view pvalue="quarter" :value="scope.row.quarter"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="数据状态" min-width="120" prop="status" header-align="center" align="center" column-key="STATUS">
        <template  #default="scope">
          <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center" min-width="150">
    <template slot-scope="scope">
      <el-button
        v-if="scope.row.status=='daiXiuGai'||scope.row.status=='daiXianShenHe'||scope.row.status=='weiTianBao'"
        size="mini"
        title="填报"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleFill(scope.row)"
        v-hasPermi="['reports:responsiblePersionReport:save']"
      >填报</el-button>
      <el-button
        v-if="scope.row.status"
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-document-copy"
        title="查看详情"
        v-has-permi="['reports:responsiblePersionReport:info']"
        @click="handleView(scope.row)"
      >详情</el-button>
      <el-button
        v-if="scope.row.status&&scope.row.status=='yiTongGuo'"
        size="mini"
        type="primary"
        class="btn-table-operate"
        icon="el-icon-bottom"
        title="打回"
        @click="handleHitBack(scope.row)"
        v-has-permi="['reports:responsiblePersionReport:shengBack']"
        >
        打回
      </el-button>
    </template>
  </el-table-column>
    </my-table>


    <responsibleDetils
      :detailsOpen="detailsOpen"
      @close="close"
      :report-id="reportId"
      :status="status"
      :title="title"
      :process-list="processList"
      :version="String(version)"/>

  </div>
</template>

<script>
import {
  getResponsiblePersionReport,
  delResponsiblePersionReport,
  delResponsiblePersionReportBatch,
  addResponsiblePersionReport,
  updateResponsiblePersionReport,
  importResponsiblePersionReport, responsibleBack
} from '@/api/reports/responsiblePersionReport'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyQuarterSelect from '@/components/YB/MyQuarterSelect.vue'
import Template from '@/views/sms/template/index.vue'
import { getProcessList } from '@/api/reports/responsiblePersion'
import ResponsibleDetils from '@/views/reports/commponents/responsibleDetils.vue'
import MyResponsibleReportDetails from '@/components/YB/MyResponsibleReportDetails.vue'
import { diversionBack } from '@/api/reports/diversionPersonReport'

export default {
  name: "ResponsiblePersionReport",
  components: { MyResponsibleReportDetails, ResponsibleDetils, Template, MyQuarterSelect, MyAreaSelect },
  data() {
    return {
      detailsOpen:false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showSearchView: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        areaCode: '',
        status:''
      },
      params:{},
      queryParamsView: {},
      // 表单参数
      responsiblePersionReport: {},
      reportId:'',
      version:'',
      status:'',
      activeName:'1',
      processList:[]
    };
  },
  mounted() {
  },
  methods: {
    /** 查询采砂监管责任人填报表列表 */
    reload(restart) {
      this.$refs.responsiblePersionReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    reloadView(restart) {
      this.$refs.responsiblePersionTable.search(this.queryParamsView, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.responsiblePersionReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        year: '',
        quarter: '',
        status: '',
        reportUserId: '',
        reportTime: '',
        version: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    handleQueryView() {
      this.reloadView(true);

    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.myQuarterSelect.resetQuery();
      this.handleQuery();
    },
    resetQueryView() {
      this.resetForm("queryFormView");
      this.handleQueryView();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.responsiblePersionReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采砂监管责任人填报表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getResponsiblePersionReport(id).then(r => {
        this.responsiblePersionReport = r.responsiblePersionReport;
        this.open = true;
        this.title = "修改采砂监管责任人填报表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.responsiblePersionReport.id) {
            updateResponsiblePersionReport(this.responsiblePersionReport).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addResponsiblePersionReport(this.responsiblePersionReport).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delResponsiblePersionReport(row.id);
        }else{
          return delResponsiblePersionReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    //填报
    handleFill(row){
      // this.open = true;
      this.detailsOpen = true;
      this.title = "采砂监管责任人填报";
      this.responsiblePersionReport = row;
      this.reportId = row.id;
      this.status = row.status;
      this.version = row.version;
      this.handleClick()
    },
    handleView(row){
      this.detailsOpen = true;
      this.title = "采砂监管责任人详情";
      this.diversionPersonReport = row;
      this.status = ''
      this.reportId = row.id;
      this.version = row.version;
      this.handleClick()
    },
    handleHitBack(row){
      this.$prompt('请输入打回原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType:'textarea',
      }).then(({ value }) => {
        responsibleBack({id:row.id,opinion:value,version:row.version}).then(r => {
          this.reload();
          this.$modal.msgSuccess("操作成功");
        })
      })
    },
    handleChange(value){
      this.queryParams.year = value.year;
      this.queryParams.quarter =  value.quarter;
    },
    //下载模板
    handleDownload(){
      this.download("/reports/responsiblePersion/downloadTemplate", {},"采砂管理责任人导入模板.xlsx")
    },
    handleUpload(){
      this.$refs.excel.click();
    },
    //导入
    fileImport(e){
      if (!this.$refs.excel.files.length) {
        return;
      }
      console.log(this.$refs.excel.files[0])
      var formData = new FormData();
      formData.append("file",this.$refs.excel.files[0]);
      importResponsiblePersionReport(formData,this.reportId,this.version).then(res=>{
        if (res.code == 0){
          this.$modal.msgSuccess("导入成功");
          this.$refs.responsiblePersionTable.search(this.queryParams);
          e.target.value = null
        }
      }).catch((err) => {
        console.log('err', err)
      });
    },
    handleClick() {
      this.processList = []
      getProcessList(this.reportId).then(res=>{
          this.processList = res.list
        })
    },
    change(e){
      this.$forceUpdate()
    },
    // 导出
    handleExport(){
      this.download('/reports/responsiblePersion/export', {
        ...this.queryParams
      }, `采砂管理负责人_${new Date().getTime()}.xlsx`, 'application/json');
    },
    close(){
      this.detailsOpen = false;
      this.$refs.responsiblePersionReportTable &&this.$refs.responsiblePersionReportTable.search(this.queryParams);
      this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.search(this.queryParamsView);
    }

  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.responsiblePersionReportTable && this.$refs.responsiblePersionReportTable.changeTableHeight();
    this.$refs.responsiblePersionTable && this.$refs.responsiblePersionTable.changeTableHeight();
  },
};
</script>
<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
