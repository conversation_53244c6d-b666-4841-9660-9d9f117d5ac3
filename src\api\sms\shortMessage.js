import request from '@/utils/request'

// 查询短信表 详细
export function getShortMessage(id) {
  return request({
    url: '/sms/shortMessage/info/' + id,
    method: 'post'
  })
}

// 新增短信表
export function addShortMessage(data) {
  return request({
    url: '/sms/shortMessage/save',
    method: 'post',
    data: data
  })
}

// 修改短信表
export function updateShortMessage(data) {
  return request({
    url: '/sms/shortMessage/update',
    method: 'post',
    data: data
  })
}

// 删除短信表
export function delShortMessage(id) {
  return request({
    url: '/sms/shortMessage/delete/' + id,
    method: 'post'
  })
}

// 批量删除短信表
export function delShortMessageBatch(ids) {
  return request({
    url: '/sms/shortMessage/delete',
    data: ids,
    method: 'post'
  });
}


