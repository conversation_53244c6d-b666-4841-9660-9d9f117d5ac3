import reques from "@/utils/request";
export function getYunSha(query) {
  return reques({
    url: "/indexStat/totalVehicleLoadPenetrate",
    method: "get",
    params: query,
  });
}
export function getYunShaRen(startTime, endTime, areaCode,projectId) {
  console.log(startTime, endTime, areaCode,projectId);
  return reques({
    url: '/indexStat/getProvinceLoad',
    data: {startTime: startTime, endTime: endTime, areaCode: areaCode,projectId:projectId},
    method: 'post'
  })
}
export function getYunShaRenNum(startTime, endTime, areaCode,projectId) {
  console.log(startTime, endTime, areaCode,projectId);
  return reques({
    url: '/indexStat/driverCount',
    data: {startTime: startTime, endTime: endTime, areaCode: areaCode,projectId:projectId},
    method: 'post'
  })
}
