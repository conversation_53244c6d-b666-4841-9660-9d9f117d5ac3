<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bill:applyExpire:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bill:applyExpire:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bill:applyExpire:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/bill/applyExpire/page" ref="applyExpireTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
  <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
  <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
  <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
  <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
  <el-table-column  label="项目ID" min-width="80" prop="projectId" sortable="custom" column-key="PROJECT_ID"></el-table-column>
  <el-table-column  label="车牌号" min-width="80" prop="carNumber" sortable="custom" column-key="CAR_NUMBER"></el-table-column>
  <el-table-column  label="所属公司/个人" min-width="80" prop="carCompany" sortable="custom" column-key="CAR_COMPANY"></el-table-column>
  <el-table-column  label="品牌型号" min-width="80" prop="carBrand" sortable="custom" column-key="CAR_BRAND"></el-table-column>
  <el-table-column  label="车主姓名" min-width="80" prop="carOwnerName" sortable="custom" column-key="CAR_OWNER_NAME"></el-table-column>
  <el-table-column  label="自重（吨）" min-width="80" prop="carKerbWeight" sortable="custom" column-key="CAR_KERB_WEIGHT">
    <template #default="scope">
      {{common.toThousands(scope.row.carKerbWeight,2,',')}}
    </template>
  </el-table-column>
  <el-table-column  label="最大载重（吨）" min-width="80" prop="carMaxPayload" sortable="custom" column-key="CAR_MAX_PAYLOAD">
    <template #default="scope">
      {{common.toThousands(scope.row.carMaxPayload,2,',')}}
    </template>
  </el-table-column>
  <el-table-column  label="卸砂地点" min-width="80" prop="destination" sortable="custom" column-key="DESTINATION"></el-table-column>
  <el-table-column  label="卸砂地点经度（gps）" min-width="80" prop="destLongitudeGps" sortable="custom" column-key="DEST_LONGITUDE_GPS"></el-table-column>
  <el-table-column  label="卸砂地点纬度（gps）" min-width="80" prop="destLatitudeGps" sortable="custom" column-key="DEST_LATITUDE_GPS"></el-table-column>
  <el-table-column  label="卸砂地点经度（腾讯）" min-width="80" prop="destLongitude" sortable="custom" column-key="DEST_LONGITUDE"></el-table-column>
  <el-table-column  label="卸砂地点维度（腾讯）" min-width="80" prop="destLatitude" sortable="custom" column-key="DEST_LATITUDE"></el-table-column>
  <el-table-column  label="预计到达时间" min-width="80" prop="estimatedArrivalTime" sortable="custom" column-key="ESTIMATED_ARRIVAL_TIME"></el-table-column>
  <el-table-column  label="卸砂点负责人？" min-width="80" prop="destLeader" sortable="custom" column-key="DEST_LEADER"></el-table-column>
  <el-table-column  label="负责人电话？" min-width="80" prop="destLeaderMobile" sortable="custom" column-key="DEST_LEADER_MOBILE"></el-table-column>
  <el-table-column  label="状态;字典值。notBilled-未开单，billed-已开单" min-width="80" prop="status" sortable="custom" column-key="STATUS"></el-table-column>
  <el-table-column  label="数据版本" min-width="80" prop="version" sortable="custom" column-key="VERSION"></el-table-column>
  <el-table-column  label="删除事假" min-width="80" prop="deletedTime" sortable="custom" column-key="DELETED_TIME"></el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['bill:applyExpire:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['bill:applyExpire:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改过期的运砂申请对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="applyExpire"  label-width="80px">
                              <my-form-item label="创建人id" ref="createUserId" prop="createUserId" :rules="[{notNull:true,message:'请输入创建人id'}]">
        <my-input v-model="applyExpire.createUserId" placeholder="请输入创建人id" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="修改人id" ref="updateUserId" prop="updateUserId" :rules="[{notNull:true,message:'请输入修改人id'}]">
        <my-input v-model="applyExpire.updateUserId" placeholder="请输入修改人id" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="创建时间" ref="createTime" prop="createTime" :rules="[{notNull:true,message:'请输入创建时间'}]">
        <my-input v-model="applyExpire.createTime" placeholder="请输入创建时间" :maxlength="20"/>
      </my-form-item>
                              <my-form-item label="修改时间" ref="updateTime" prop="updateTime" :rules="[{notNull:true,message:'请输入修改时间'}]">
        <my-input v-model="applyExpire.updateTime" placeholder="请输入修改时间" :maxlength="20"/>
      </my-form-item>
                              <my-form-item label="项目ID" ref="projectId" prop="projectId" :rules="[{notNull:true,message:'请输入项目ID'}]">
        <my-input v-model="applyExpire.projectId" placeholder="请输入项目ID" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="车牌号" ref="carNumber" prop="carNumber" :rules="[{notNull:true,message:'请输入车牌号'}]">
        <my-input v-model="applyExpire.carNumber" placeholder="请输入车牌号" :maxlength="10"/>
      </my-form-item>
                              <my-form-item label="所属公司/个人" ref="carCompany" prop="carCompany" :rules="[{notNull:true,message:'请输入所属公司/个人'}]">
        <my-input v-model="applyExpire.carCompany" placeholder="请输入所属公司/个人" :maxlength="128"/>
      </my-form-item>
                              <my-form-item label="品牌型号" ref="carBrand" prop="carBrand" :rules="[{notNull:true,message:'请输入品牌型号'}]">
        <my-input v-model="applyExpire.carBrand" placeholder="请输入品牌型号" :maxlength="128"/>
      </my-form-item>
                              <my-form-item label="车主姓名" ref="carOwnerName" prop="carOwnerName" :rules="[{notNull:true,message:'请输入车主姓名'}]">
        <my-input v-model="applyExpire.carOwnerName" placeholder="请输入车主姓名" :maxlength="128"/>
      </my-form-item>
                      <my-form-item label="自重（吨）" ref="carKerbWeight" prop="carKerbWeight" :rules="[{notNull:true,message:'请输入自重（吨）'}]">
        <my-input v-model="applyExpire.carKerbWeight" placeholder="请输入自重（吨）" />
      </my-form-item>
                      <my-form-item label="最大载重（吨）" ref="carMaxPayload" prop="carMaxPayload" :rules="[{notNull:true,message:'请输入最大载重（吨）'}]">
        <my-input v-model="applyExpire.carMaxPayload" placeholder="请输入最大载重（吨）" />
      </my-form-item>
                              <my-form-item label="卸砂地点" ref="destination" prop="destination" :rules="[{notNull:true,message:'请输入卸砂地点'}]">
        <my-input v-model="applyExpire.destination" placeholder="请输入卸砂地点" :maxlength="128"/>
      </my-form-item>
                      <my-form-item label="卸砂地点经度（gps）" ref="destLongitudeGps" prop="destLongitudeGps" :rules="[{notNull:true,message:'请输入卸砂地点经度（gps）'}]">
        <my-input v-model="applyExpire.destLongitudeGps" placeholder="请输入卸砂地点经度（gps）" />
      </my-form-item>
                      <my-form-item label="卸砂地点纬度（gps）" ref="destLatitudeGps" prop="destLatitudeGps" :rules="[{notNull:true,message:'请输入卸砂地点纬度（gps）'}]">
        <my-input v-model="applyExpire.destLatitudeGps" placeholder="请输入卸砂地点纬度（gps）" />
      </my-form-item>
                      <my-form-item label="卸砂地点经度（腾讯）" ref="destLongitude" prop="destLongitude" :rules="[{notNull:true,message:'请输入卸砂地点经度（腾讯）'}]">
        <my-input v-model="applyExpire.destLongitude" placeholder="请输入卸砂地点经度（腾讯）" />
      </my-form-item>
                      <my-form-item label="卸砂地点维度（腾讯）" ref="destLatitude" prop="destLatitude" :rules="[{notNull:true,message:'请输入卸砂地点维度（腾讯）'}]">
        <my-input v-model="applyExpire.destLatitude" placeholder="请输入卸砂地点维度（腾讯）" />
      </my-form-item>
                              <my-form-item label="预计到达时间" ref="estimatedArrivalTime" prop="estimatedArrivalTime" :rules="[{notNull:true,message:'请输入预计到达时间'}]">
        <my-input v-model="applyExpire.estimatedArrivalTime" placeholder="请输入预计到达时间" :maxlength="20"/>
      </my-form-item>
                              <my-form-item label="卸砂点负责人？" ref="destLeader" prop="destLeader" :rules="[{notNull:true,message:'请输入卸砂点负责人？'}]">
        <my-input v-model="applyExpire.destLeader" placeholder="请输入卸砂点负责人？" :maxlength="128"/>
      </my-form-item>
                              <my-form-item label="负责人电话？" ref="destLeaderMobile" prop="destLeaderMobile" :rules="[{notNull:true,message:'请输入负责人电话？'}]">
        <my-input v-model="applyExpire.destLeaderMobile" placeholder="请输入负责人电话？" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="状态;字典值。notBilled-未开单，billed-已开单" ref="status" prop="status" :rules="[{notNull:true,message:'请输入状态;字典值。notBilled-未开单，billed-已开单'}]">
        <my-input v-model="applyExpire.status" placeholder="请输入状态;字典值。notBilled-未开单，billed-已开单" :maxlength="64"/>
      </my-form-item>
                              <my-form-item label="数据版本" ref="version" prop="version" :rules="[{notNull:true,message:'请输入数据版本'}]">
        <my-input v-model="applyExpire.version" placeholder="请输入数据版本" :maxlength="11"/>
      </my-form-item>
                              <my-form-item label="删除事假" ref="deletedTime" prop="deletedTime" :rules="[{notNull:true,message:'请输入删除事假'}]">
        <my-input v-model="applyExpire.deletedTime" placeholder="请输入删除事假" :maxlength="20"/>
      </my-form-item>
          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getApplyExpire, delApplyExpire, delApplyExpireBatch,addApplyExpire, updateApplyExpire } from "@/api/bill/applyExpire";
import common from '../../../utils/common'

export default {
  name: "ApplyExpire",
  computed: {
    common() {
      return common
    }
  },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      applyExpire: {},
    };
  },
  mounted() {
  },
  methods: {
    /** 查询过期的运砂申请列表 */
    reload(restart) {
      this.$refs.applyExpireTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.applyExpire = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        projectId: '',
        carNumber: '',
        carCompany: '',
        carBrand: '',
        carOwnerName: '',
        carKerbWeight: null ,
        carMaxPayload: null ,
        destination: '',
        destLongitudeGps: null ,
        destLatitudeGps: null ,
        destLongitude: null ,
        destLatitude: null ,
        estimatedArrivalTime: '',
        destLeader: '',
        destLeaderMobile: '',
        status: '',
        version: null ,
        deletedTime: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.applyExpireTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加过期的运砂申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getApplyExpire(id).then(r => {
        this.applyExpire = r.applyExpire;
        this.open = true;
        this.title = "修改过期的运砂申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.applyExpire.id) {
            updateApplyExpire(this.applyExpire).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addApplyExpire(this.applyExpire).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delApplyExpire(row.id);
        }else{
          return delApplyExpireBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.applyExpireTable.changeTableHeight();
  },
};
</script>
