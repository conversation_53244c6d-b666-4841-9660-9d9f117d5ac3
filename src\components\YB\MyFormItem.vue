<template>
  <el-form-item v-bind="$attrs" :rules="innerRules" :prop="prop" :key="prop">
    <template #label>
      <slot name="label"></slot>
    </template>
    <slot></slot>
  </el-form-item>
</template>

<script>
import {idCardValidate} from '@/utils/validate';
import Template
  from "@/views/sms/template/index.vue";

export default {
  name: "MyFormItem",
  components: {Template},
  props: {
    prop: '',
    /*  form 校验
		rules="[
        {notNull:true, message:'姓名不能为空'},
        {isNumStr:true,message:'只能是数字'},//数字串校验，用于电话号码，银行卡号等
        {isMobile:true,message:'手机号码不正确'}, //手机号
        {isTel:true,message:'电话号码不正确'}, //固话
        {isMobileOrTel:true,message:'联系电话不正确'}, //手机号或固话号
        {isNumber:true, message:'只能是数字'},//浮点数
        {isDigit:true, message:'只能是整数'},
				{isEmail:true,message:'电子邮件格式不正确'},
				{isIdCard:true,message:'身份证号无效'},
				{minLength:6,message:'帐号长度至少为6'},
				{maxLength:6,message:'帐号长度最多为6'},
				{minValue:0,message:'年龄最小值为0'},
				{maxValue:100,message:'年龄最大值为100'},
				{greaterThanValue:0,message:'年龄必须大于0'},
				{lessThanValue:100,message:'年龄必须小于100'},
				{regExp:/^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/,message:'自定义正在表达式验证'},
				{regExp:/^[^\<|\>]{0,}$/,message:'用户名不可包含<或>'}，
				{regExp:/^[^\u4e00-\u9fa5]{0,}$/,message:'用户名不可包含汉字'},
				{fn:someFunction,message:'不满足条件'},
			]"
	*/
    rules: {
      type: Array,
      default: function () {
        return null;
      },
    },
  },
  data: function () {
    return {

    }
  },
  methods: {
    /**
     * 1、浮点数校验
     */
    isNumber: function (num) {
      return /^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/.test(num);
    },
    /**
     * 2、整数校验
     */
    isDigit: function (num) {
      return /^((-?[1-9]\d*)|0)$/.test(num);
    },

    /**
     * 3、email校验
     */
    isEmail: function (email) {
      return /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i.test(email);
    },
    /**
     * 4、数字串校验，用于电话号码，银行卡号等
     */
    isNumStr: function (num) {
      return /^\d+$/.test(num);
    }

  },
  computed:{
    innerRules: function () {
      if (!this.rules) {
        return null;
      }
      var that=this;

      var rules=this.rules.map(e=>{
        //非空
        if (e.notNull) {
          return {required:true,message:e.message, trigger: e.trigger ? e.trigger : 'blur'}
        }
        //是手机号
        else if (e.isMobile) {
          return {
            validator: function (rule, value, callback) {
              if (value && !(/^1[3456789]\d{9}$/.test(value))) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //只能是固话
        else if (e.isTel) {
          return {
            validator: function (rule, val, callback) {
              if (val && !/^((\d{3,4})|\d{3,4}-|\s)?\d{7,14}$/.test(val)) {
                callback(new Error(e.message))
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //是手机号或者固话号
        else if (e.isMobileOrTel) {
          return {
            validator: function (rule, value, callback) {
              if (value && !(/^1[3456789]\d{9}$/.test(value))
                && !(/^(\d{4}-|\d{3}-)(\d{8}|\d{7})$/.test(value))) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //只能是浮点数
        else if(e.isNumber){
          return {
            validator: function (rule, value, callback) {
              if (value && !that.isNumber(value)) {
                callback(new Error(e.message))
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //只能是数字串
        else if (e.isNumStr) {
          return {
            validator: function (rule, value, callback) {
              if (value && !that.isNumStr(value)) {
                callback(new Error(e.message));
              } else {
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          };
        }
        //只能是整数
        else if (e.isDigit) {
          return {
            validator: function (rule, value, callback) {
              if (value && !that.isDigit(value)) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //是邮箱
        else if (e.isEmail) {
          return {
            validator: function (rule, value, callback) {
              if (value && !that.isEmail(value)) {
                callback(new Error(e.message))
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //是身份证号吗
        else if (e.isIdCard) {
          return {
            validator: function (rule, value, callback) {
              if (value && !idCardValidate(value)) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //最小长度
        else if (e.minLength) {
          return {
            validator: function (rule, value, callback) {
              if (value && (value.length < e.minLength)) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //最大长度
        else if (e.maxLength) {
          return {
            validator: function (rule, value, callback) {
              if (value && (value.length > e.maxLength)) {
                callback(new Error(e.message));
              } else {
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //最小值
        else if (e.minValue != undefined) {
          return {
            validator: function (rule, val, callback) {
              var test = val;
              if (typeof (val) === "string") {
                test = parseFloat(val.replace(/,/g, ""));
              }
              if (val && (!isNaN(test) && test < e.minValue)) {
                callback(new Error(e.message))
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //最大值
        else if (e.maxValue != undefined) {
          return {
            validator: function (rule, val, callback) {
              var test = val;
              if (typeof (val) === "string") {
                test = parseFloat(val.replace(/,/g, ""));
              }

              if (val && (!isNaN(test) && test > e.maxValue)) {
                callback(new Error(e.message));
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        //最小值，不等于
        else if (e.greaterThanValue != undefined) {
          return {
            validator: function (rule, val, callback) {
              var test = val;
              if (typeof (val) === "string") {
                test = parseFloat(val.replace(/,/g, ""));
              }

              if (val && (!isNaN(test) && test <= e.greaterThanValue)) {
                callback(new Error(e.message))
              }else{
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          }
        }
        else if (e.lessThanValue != undefined) {
          return {
            validator: function (rule, val, callback) {
              var test = val;
              if (typeof (val) === "string") {
                test = parseFloat(val.replace(/,/g, ""));
              }

              if (val && (!isNaN(test) && test >= e.lessThanValue)) {
                callback(new Error(e.message))
              } else {
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          };
        }
        //正则
        else if (e.regExp) {
          return {
            validator: function (rule, val, callback) {
              if (val && !e.regExp.test(val)) {
                callback(new Error(e.message));
              } else {
                callback();
              }
            },
            trigger: e.trigger ? e.trigger : 'blur',
          };
        } else if (e.fn) {
          return {
            validator: function (rule, val, callback) {
              /*if (!e.fn(val)) {
                callback(new Error(e.message));
              } else {
                callback();
              }*/
              e.fn(val).then(callback).catch(function () {
                callback(new Error(e.message));
              });
            },
            trigger: e.trigger ? e.trigger : 'blur',
          };
        }
        //没有符合要求的，原样返回，从而保证支持原有的验证规则写法
        else {
          return e;
        }

      })
      return rules;

    },
  }
}
</script>

<style scoped>

</style>
