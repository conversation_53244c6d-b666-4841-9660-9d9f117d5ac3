<template>
  <div class="app-container">
    <!-- 省市区采砂统计筛选条件 -->
    <el-form :model="queryParams" ref="queryForms" size="small" :inline="true" v-show="showSearch" label-width="68px"
      style="margin-left: 15px">
      <el-form-item label="项目位置" prop="areaCode" style="margin-bottom: 0px"
        v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
        <my-area-select v-model="queryParams.areaCode" @change="getAreaName" :external-parent-value="parentAreaCode"
          :external-children-value="childrenAreaCode" :read-only-parent="isReadOnlyParent"
          :read-only-children="isReadOnlyChildren" :is-clear="isClear" style="font-size: medium;" />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectId" style="margin-bottom: 0px"
        v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
        <el-select v-model="queryParams.projectId" placeholder="请选择项目" clearable @change="getAreaName1" :disabled="mydisabled">
          <el-option v-for="dict in projectList" :key="dict.deptId" :label="dict.name" :value="dict.deptId" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目时间" prop="timeRange" style="margin-bottom: 0px"
        v-hasPermi="['mobile:puichasingOrderManage:dateRange']">
<!--        <el-date-picker v-model="timeRange" type="daterange" @change="handleDateChange" value-format="yyyy-MM-dd"
          align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          :picker-options="pickerOptions" :clearable="true">
        </el-date-picker>-->
       <div style="display: flex;align-items: center;justify-content: center">
         <el-date-picker
           v-model="provinecSandStartTime"
           type="date"
           :picker-options="pickerStartDate"
           @change="handleDateChange"
           :clearable="false"
           value-format="yyyy-MM-dd"
           style="width: 130px"
           placeholder="开始日期">
         </el-date-picker>
         <div style="padding: 0px 10px">-</div>
         <el-date-picker
           v-model="provinecSandEndTime"
           type="date"
           :clearable="false"
           :picker-options="pickerEndDate"
           @change="handleDateChange"
           value-format="yyyy-MM-dd"
           style="width: 130px"
           placeholder="结束日期">
         </el-date-picker>
       </div>

      </el-form-item>

      <el-form-item prop="selectedTime" style="margin-bottom: 0px"
        v-hasPermi="['mobile:puichasingOrderManage:timeTags']">
        <el-radio-group v-model="selectedTime" @change="handleTimeChange">
          <el-radio-button label="本年"></el-radio-button>
          <el-radio-button label="本月"></el-radio-button>
          <el-radio-button label="昨日"></el-radio-button>
          <el-radio-button label="本日"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item style="margin-bottom: 0px;">
        <!-- <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button> -->
        <el-button icon="el-icon-refresh" @click="resetQuery" type="primary">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 统计信息 -->
    <el-row style="margin-top: 10px">
      <el-col :xs="24" :sm="12" class="card-box custom-md-col custom-xl-col"
        v-hasPermi="['mobile:puichasingOrderManage:totalVehicleLoad']">
        <div class="mycard cardOne" @click="gotoTaost('累计运砂量')">
          <!-- <el-tooltip :content="totalVehicle+unit" placement="top-start" effect="light"> -->
          <div class="content">{{ totalVehicle }} </div>
          <!-- </el-tooltip> -->
          <div class="title">累计运砂量({{ unit }})</div>
          <i class="iconfont el-icon-smm-caisha icon"></i>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" class="card-box custom-md-col custom-xl-col "
        v-hasPermi="['mobile:puichasingOrderManage:transitBillCount']">
        <div class="mycard cardTwo" @click="gotoTaost('在运单数')">
          <!-- <el-tooltip :content="transitBillCount+''" placement="top-start" effect="light"> -->
          <div class="content">{{ transitBillCount }} </div>
          <!-- </el-tooltip> -->
          <div class="title">在运单数(个)</div>
          <i class="iconfont el-icon-smm-zaiyun icon"></i>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" class="card-box custom-md-col custom-xl-col "
        v-hasPermi="['mobile:puichasingOrderManage:completedBillCount']">
        <div class="mycard cardFive" @click="gotoTaost('核销单数')">
          <!-- <el-tooltip :content="completedBillCount+''" placement="top-start" effect="light"> -->
          <div class="content">{{ completedBillCount }} </div>
          <!-- </el-tooltip> -->
          <div class="title">核销单数(个)</div>
          <i class="iconfont el-icon-smm-hexiao icon"></i>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" class="card-box custom-md-col custom-xl-col "
        v-hasPermi="['mobile:puichasingOrderManage:projectCount']">
        <div class="mycard cardThree" @click="gotoTaost('项目数量')">
          <!-- <el-tooltip :content="projectCount+''" placement="top-start" effect="light"> -->
          <div class="content">{{ projectCount }} </div>
          <!-- </el-tooltip> -->
          <div class="title">项目数量(个)</div>
          <i class="fa fa-files-o icon"></i>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" class="card-box custom-md-col custom-xl-col "
        v-hasPermi="['mobile:puichasingOrderManage:driverCount']">
        <div class="mycard cardFour" @click="gotoTaost1('运砂人数量')">
          <div class="content">{{ driverCount }} </div>
          <div class="title">运砂人数量(人)</div>
          <i class="fa fa-users icon"></i>
        </div>
      </el-col>

    </el-row>
    <!-- 指定位置采砂总体统计图表 -->
    <el-row style="margin-top: 10px">
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" style="padding: 0 15px" v-show="provinceShow"
        v-hasPermi="['mobile:puichasingOrderManage:provinceSandMap']">
        <el-card>
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">{{ provinceSandMapName }}采砂统计</span>
          </div>
          <div ref="provinceSandMap" style="height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="provinceShow ? 12 : 24" :md="provinceShow ? 12 : 24" :lg="provinceShow ? 12 : 24"
        :xl="provinceShow ? 12 : 24" style="padding:0 15px" v-hasPermi="['mobile:puichasingOrderManage:citySandMap']">
        <el-card>
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">{{ citySandMapName }}采砂统计</span>
          </div>
          <div ref="citySandMap" style="height: 400px;padding-right: 30px"></div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 项目级采砂统计图表 -->
    <el-row v-hasPermi="['mobile:puichasingOrderManage:projectSandMap']">
      <el-col :span="24" style="padding: 0 5px">
        <el-card>
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">{{ projectSandMapName }}采砂统计</span>
          </div>
          <div ref="projectSandMap" style="height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计表格模块 -->
    <el-row v-hasPermi="['mobile:puichasingOrderManage:projectStatisticsModule']">
      <el-card style="margin: 15px 15px;">
        <!-- 采砂统计表格筛选条件 -->
        <el-row style="margin-top: 10px">
          <el-form :model="queryProject" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="项目名称" prop="projectName" style="margin-bottom: 0px;"
              v-hasPermi="['mobile:puichasingOrderManage:projectName']">
              <my-select id="projectName" :options="queryProjectName" v-model="queryProject.projectName"
                placeholder="项目名称" @change="handleProjectNameSelect" />
            </el-form-item>
            <el-form-item label="项目类型" prop="type" v-hasPermi="['mobile:puichasingOrderManage:projectType']">
              <my-select id="type" pvalue="projectType" v-model="queryProject.type" placeholder="项目类型"
                @change="handleProjectTypeSelect" />
            </el-form-item>
            <el-form-item label="预警状态" prop="warningType" v-hasPermi="['mobile:puichasingOrderManage:warningType']">
              <my-select id="warningType" pvalue="minedWarningType" v-model="queryProject.warningType"
                placeholder="状态类型" @change="handleWarningTypeSelect" />
            </el-form-item>
          </el-form>
        </el-row>
        <!-- 登陆人可见项目统计信息表格 -->
        <el-row>
          <el-col :span="24" style="padding: 0 5px" v-hasPermi="['mobile:puichasingOrderManage:projectTable']">
            <my-table url="/project/statistics/page" ref="projectTable" row-key="deptId" :multiselect="false"
              :fixed="true" :height=485 show-summary :summary-method="getSummaries" @query-success="handleQuerySuccess"
              cell-class-name="el-table-max-cell2" @my-selection-change="handleSelectionChange"
              @my-sort-change="handleSortChange" class="el-table-auto-height" :tree-props="{
                children: 'sectionList'
              }">
              <el-table-column label="采区名称" fixed="left" header-align="center" align="left" min-width="246" prop="name"
                column-key="project.NAME">
                <template #default="scope">
                  <el-tooltip :content="scope.row.name" placement="top" effect="light">
                    <div>{{ scope.row.name }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="项目位置" align="center" header-align="center" min-width="160" prop="areaName"
                column-key="project.AREA_NAME">
              </el-table-column>
              <el-table-column label="监管部门" align="center" header-align="center" min-width="160" prop="deptName"
                column-key="project.DEPT_NAME">
              </el-table-column>
              <el-table-column label="项目类型" align="center" header-align="center" min-width="160" prop="type"
                column-key="project.TYPE">
                <template slot-scope="scope">
                  <my-view pvalue="projectType" :value="scope.row.type"></my-view>
                </template>
              </el-table-column>
              <el-table-column label="控制总量(吨)" align="right" header-align="center" min-width="130" prop="totalYield"
                column-key="project.TOTAL_YIELD">
                <template #default="scope">
                  {{ common.toThousands(scope.row.totalYield, 2, ',') }}
                </template>
              </el-table-column>
              <el-table-column label="已采总量(吨)" align="right" header-align="center" min-width="130" prop="mined"
                column-key="project.MINED">
                <template #default="scope">
                  {{ common.toThousands(scope.row.mined, 2, ',') }}
                </template>
              </el-table-column>
              <el-table-column label="采区剩余总量(吨)" align="right" header-align="center" min-width="130"
                prop="remainingSand" column-key="project.REMAINING_SAND">
                <template #default="scope">
                  {{ common.toThousands(scope.row.remainingSand, 2, ',') }}
                </template>
              </el-table-column>
              <el-table-column label="预警状态" fixed="right" align="center" header-align="center" min-width="180"
                prop="minedRate" column-key="project.MINED_RATE">
                <template slot-scope="scope">
                  <el-progress
                    :percentage="scope.row.minedRate * 100 > 100 ? 100 : Number((scope.row.minedRate * 100).toFixed(2))"
                    :color="getProgressColor(scope.row.minedRate)" :text-inside="true" :stroke-width="15"
                    class="my-progress" />
                </template>
              </el-table-column>
            </my-table>
          </el-col>
        </el-row>
      </el-card>
    </el-row>
    <el-dialog :title="tv" :visible.sync="taost" width="700px" append-to-body>
      <YunSha :title="tv" v-if="taost" :contents="queryParams" :timeRange="timeRange"></YunSha>
      <div slot="footer" class="dialog-footer">
        <el-button @click="taost = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="tv1" :visible.sync="taost1" width="700px" append-to-body>
      <YunShaRen :title="tv1" v-if="taost1" :contents="queryParams" :timeRange="timeRange"></YunShaRen>
      <div slot="footer" class="dialog-footer">
        <el-button @click="taost1 = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  driverCountNum,
  getProvinceLoad,
  getCityLoad,
  getUserInfo,
  getProjectLoad
} from '@/api';
import { getProject } from "@/api/tablebbtb/bbtb";
import { getProjectList } from '@/api/statistics/projectStatistics'
import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
import YunSha from "@/components/HomeTable/YunSha.vue";
import YunShaRen from "@/components/HomeTable/YunShaRen.vue";
import * as echarts from "echarts";
import { options } from 'runjs';
import common from '../utils/common'


export default {
  name: "Index",
  components: {
    MyAreaSelect,
    YunSha,
    YunShaRen
  },
  data() {
    return {
      pickerStartDate:this.pickerStartDate1(),
      pickerEndDate:this.pickerEndDate1(),
      mydisabled:true,
      projectContent: [],
      projectList: [],
      totalVehicleLoad: '',// 累计运砂量
      billCount: '',// 运单数量
      completedBillCount: '',// 已完成运单数量
      transitBillCount: '',// 核销运单数量
      driverCount: '',// 运砂人数量
      projectCount: '',// 项目数量

      activeNames: ['1', '2', '3'],  // 折叠面板
      currentTime: '',  //当前日期时间

      selectedTime: '',

      // 区域选择框默认选中
      parentAreaCode: '',
      childrenAreaCode: '',
      isReadOnlyParent: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren: false, // 控制子下拉框是否可编辑
      isClear: false, // 父选择框是否可清空

      // 显示搜索条件
      showSearch: true,
      queryParams: {
        areaCode: '',
        projectId: '',
        code:''
      },
      queryProject: {
        name: '',
        sectionName: '',
        type: '',
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },
      timeRange: ['', ''], // 默认不选中,

      //provinceSandMap
      provinceSandMap: null,  //echarts图表对象
      provinceSandMapName: '',
      provinceSandMapXAxis: [],
      provinceSandMapTotalLoad: [],
      provinceSandMapUnit: '吨',
      provinceSandMapCompleted: [],
      provinceSandMapTranit: [],
      provinceSandMapProject: [],
      provinecSandStartTime: common.formatDate(this.getLastYearStart(), 'yyyy-MM-dd'),
      provinecSandEndTime: common.formatDate(new Date(), 'yyyy-MM-dd'),
      provinceShow: true,

      //citySandMap
      citySandMap: null,  //echarts图表对象
      defaultCity: '',
      defaultCityCode: '',
      citySandMapName: '',
      citySandMapXAxis: [],
      citySandMapTotalLoad: [],
      citySandMapUnit: '吨',
      citySandStartTime: this.formatDate(this.getLastYearStart()),
      citySandEndTime: this.formatDate(new Date()),

      //projectSandMap
      projectSandFlag: false, // 项目信息统计
      projectSandMap: null, //echarts图表对象
      projectSandMapName: '',
      projectSandMapXAxis: [],
      projectSandMapTotalLoad: [],
      projectSandMapUnit: '吨',
      projectSandStartTime: this.formatDate(this.getLastYearStart()),
      projectSandEndTime: this.formatDate(new Date()),

      // 表格项目统计
      queryProjectName: [],
      querySectionName: [],
      totalList: [],
      taost: false,
      taost1: false,
      tv: '累计运砂量',
      tv1: '运砂人数量',
    };
  },
  watch: {
    provinceShow(newVal) {
      this.$nextTick(() => {
        console.log("provinceShow:" + newVal);
        if (this.citySandMap) {
          this.citySandMap.resize();
        }
        if (this.provinceSandMap && newVal) {
          this.provinceSandMap.resize();
        }
      });
    }
  },
  computed: {
    common() {
      return common
    },
    totalVehicle: function () {

      if (this.totalVehicleLoad < 100000) {
        return (this.totalVehicleLoad)
      }
      else if (this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000) {
        return (this.totalVehicleLoad / 10000).toFixed(2)
      }
      else if (this.totalVehicleLoad >= 1000000000 && this.totalVehicleLoad < 10000000000000) {
        return (this.totalVehicleLoad / 100000000).toFixed(2)
      }
      else {
        return (this.totalVehicleLoad / 1000000000000).toFixed(2)
      }
    },
    unit: function () {
      if (this.totalVehicleLoad < 100000) {
        return '吨'
      } else if (this.totalVehicleLoad >= 100000 && this.totalVehicleLoad < 1000000000) {
        return '万吨'
      } else if (this.totalVehicleLoad >= 1000000000) {
        return '亿吨'
      } else {
        return '万亿吨'
      }
    },
  },
  created() {
    // this.getProjectList1();
  },
  mounted() {
    this.getUserInfo();
    this.getProjectList();
    // this.getMeprojectList(areaCode, startTime, endTime);
  },
  methods: {
    pickerStartDate1(){
      let that = this;
      return {
        disabledDate(time) {
          return  Date.parse(new Date(common.formatDate(time, 'yyyy-MM-dd')))  > Date.parse(new Date(that.provinecSandEndTime))
        }
      }
    },
    pickerEndDate1() {
      let that = this;
      return {
        disabledDate(time) {
          return  Date.parse(new Date(common.formatDate(time, 'yyyy-MM-dd')))  < Date.parse(new Date(that.provinecSandStartTime))
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.timeRange = ['', ''] //重置为空
      this.selectedTime = ''
      this.provinecSandStartTime = common.formatDate(this.getLastYearStart(), 'yyyy-MM-dd');
      this.provinecSandEndTime = common.formatDate(new Date(), 'yyyy-MM-dd');
      this.resetForm("queryForms");
      this.getUserInfo();
    },
    gotoTaost(title) {
      this.tv = title;
      this.taost = true;
    },
    gotoTaost1(title) {
      this.tv1 = title;
      this.taost1 = true;
    },
    getMeprojectList(areaCode, startTime, endTime) {
      getProject({ areaCode: areaCode, startTime: startTime, endTime: endTime }).then((res) => {
        console.log(res, '我得项目列表')
        this.projectList = res.projectId;
      });
    },
    handleQuerySuccess(data) {
      this.totalList = data.total
    },
    getSummaries(params) {
      let showTotal = this.totalList;
      const { columns } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          sums[5] = common.toThousands(showTotal.totalYield, 2, ',')
          sums[6] = common.toThousands(showTotal.mined, 2, ',')
          sums[7] = common.toThousands(showTotal.remainingSand, 2, ',')
          return;
        }
      })
      return sums;
    },
    //折叠面板
    handleChange(val) {
      console.log(val);
    },

    updateCurrentTime() {
      const now = new Date();
      this.currentTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
      // this.currentTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    },

    /** 查询运砂人数量 */
    queryDriverCount() {
      let code = "";
      if (this.queryParams.areaCode == '') {
        code = this.defaultCityCode
      } else {
        code = this.queryParams.areaCode
      }
      driverCountNum(code, this.queryParams.projectId, this.provinecSandStartTime, this.provinecSandEndTime).then(res => {
        console.log(res,'运沙人数量')
        this.driverCount = res.count.driverCountAll
      })
    },

    //根据窗口更改图表大小
    resizeCharts() {
      this.provinceSandMap.resize();
      this.citySandMap.resize();
      this.projectSandMap.resize();
    },

    //获取当前登录用户信息
    getUserInfo() {
      getUserInfo().then(res => {
        const { deptCode, areaCode, areaName, parentCode, parentName } = res.userInfo;
        let deptCodeLength = deptCode.length;
        console.log("********getUserInfo:",areaCode)
        if(areaCode.length==15){
          this.mydisabled=false;
        }else{
          this.mydisabled=true;
        }
        // 定义公共属性
        this.defaultCityCode = areaCode;
        this.citySandMapName = areaName;

        switch (deptCodeLength) {
          case 4: // 省级用户
            this.parentAreaCode = ''; // 默认显示石家庄市
            this.provinceSandMapName = '河北省';
            this.defaultCityCode = '000_013';
            this.citySandMapName = '河北省';
            this.isClear = true;
            break;
          case 9: // 市级用户
            this.provinceSandMapName = areaName;
            this.parentAreaCode = areaCode;
            this.isReadOnlyParent = true;
            break;
          case 14: // 区县级用户
            this.parentAreaCode = parentCode;
            this.childrenAreaCode = areaCode;
            this.citySandMapName = parentName + areaName;
            this.isReadOnlyParent = true;
            this.isReadOnlyChildren = true;
            this.provinceShow = false;
            break;
          case 19: // 项目级用户
            this.projectSandFlag = true;
            this.projectShow = true;
            break;
          default:
            return;
        }
        console.log(this.parentAreaCode,this.childrenAreaCode,'这个市区约')
        console.log(areaCode,'这个市区约')
        this.queryParams.code=areaCode;
        this.getMeprojectList(areaCode, this.provinecSandStartTime, this.provinecSandEndTime);
        this.initCharts();
        this.queryDriverCount();
      });
    },

    //初始化图表
    initCharts() {
      console.log("初始化initCharts执行方法:")
      if (this.projectSandFlag) { //项目级别信息图表初始化
        this.projectSandMap = echarts.init(this.$refs.projectSandMap);
        this.getProjectLoadByTime();
      } else { //省市区级别信息图表初始化
        this.provinceSandMap = echarts.init(this.$refs.provinceSandMap);
        this.citySandMap = echarts.init(this.$refs.citySandMap);
        this.getProvinceLoadByYear();
        this.getCityLoadByTimeAndCity();
      }

      window.addEventListener('resize', () => { this.resizeCharts() });
      document.addEventListener('fullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('webkitfullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('mozfullscreenchange', () => { this.resizeCharts() });
      document.addEventListener('msfullscreenchange', () => { this.resizeCharts() });
    },
    //各市/各区采砂信息横向对比图表
    getProvinceLoadByYear() {
      //获取各市采砂和项目信息
      getProvinceLoad(this.provinecSandStartTime, this.provinecSandEndTime, this.queryParams.areaCode?this.queryParams.areaCode:this.queryParams.code, this.queryParams.projectId).then(res => {
        this.provinceSandMapXAxis = [];
        this.provinceSandMapTotalLoad = [];
        this.provinceSandMapCompleted = [];
        this.provinceSandMapTranit = [];
        this.provinceSandMapProject = [];
        res.list.forEach(item => {
          if (item.areaName != '河北省') {
            this.provinceSandMapXAxis.push(item.areaName);
            this.provinceSandMapTotalLoad.push(item.totalLoad !== null ? item.totalLoad : 0);
            this.provinceSandMapCompleted.push(item.completedBill !== null ? item.completedBill : 0);
            this.provinceSandMapTranit.push(item.transitBill !== null ? item.transitBill : 0);
            this.provinceSandMapProject.push(item.proCount !== null ? item.proCount : 0);
          }
        });
        // 计算总和
        this.totalVehicleLoad = this.provinceSandMapTotalLoad.reduce((acc, val) => acc + val, 0).toFixed(2);
        this.completedBillCount = this.provinceSandMapCompleted.reduce((acc, val) => acc + val, 0);
        this.transitBillCount = this.provinceSandMapTranit.reduce((acc, val) => acc + val, 0);
        this.projectCount = this.provinceSandMapProject.reduce((acc, val) => acc + val, 0);

        //判断是否需要转变数据位数和单位
        if (this.provinceSandMapTotalLoad.length > 0) {
          if (this.provinceSandMapTotalLoad.some(value => value >= 10000000000000)) {
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 1000000000000).toFixed(2)); //万亿吨
            this.provinceSandMapUnit = '万亿吨'
          } else if (this.provinceSandMapTotalLoad.some(value => value >= 1000000000 && value < 10000000000000)) {
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 100000000).toFixed(2)); // 亿吨
            this.provinceSandMapUnit = '亿吨'
          } else if (this.provinceSandMapTotalLoad.some(value => value >= 100000 && value < 1000000000)) {
            this.provinceSandMapTotalLoad = this.provinceSandMapTotalLoad.map(value => (value / 10000).toFixed(2)); // 万吨
            this.provinceSandMapUnit = '万吨'
          } else if (this.provinceSandMapTotalLoad.every(value => value < 100000)) {
            this.provinceSandMapUnit = '吨'
          }
        }
        // // 找出 totalLoad 的最大值和最小值
        // const totalLoadMax = Math.max(...this.provinceSandMapTotalLoad);
        // const totalLoadMin = Math.min(...this.provinceSandMapTotalLoad);

        // // 找出 completed、transit、project 的最大值和最小值
        // const combinedValues = [
        //     ...this.provinceSandMapCompleted,
        //     ...this.provinceSandMapTranit,
        //     ...this.provinceSandMapProject
        // ];
        // const combinedMax = Math.max(...combinedValues);
        // const combinedMin = Math.min(...combinedValues);

        // // 计算 Y 轴的范围和间隔
        // const totalLoadRange = [totalLoadMin, totalLoadMax];
        // const combinedRange = [combinedMin, combinedMax];
        // const totalLoadInterval = (totalLoadMax - totalLoadMin) / 5;
        // const combinedInterval = (combinedMax - combinedMin) / 5;
        const freshColors = ['#1890FF', '#13CE66', '#FFBA00', '#FFA4A4', '#827CFA'];
        var option = {
          title: {
            // text: '河北省采砂统计'
          },
          color: freshColors,
          tooltip: {
            trigger: 'axis',
            // axisPointer: {
            //   type: 'cross',
            //   crossStyle: {
            //     color: '#999'
            //   }
            // }
          },
          // toolbox: {
          //   feature: {
          //     dataView: { show: true, readOnly: false },
          //     magicType: { show: false, type: ['line', 'bar'] },
          //     restore: { show: false },
          //     // saveAsImage: { show: true }
          //   },
          //   right:"5%",
          // },
          legend: {
            data: ['累计运砂量', '在运单数', '核销单数', '项目数量'],
            left: 'center',
            orient: 'horizontal'
          },
          xAxis: [
            {
              type: 'category',
              data: this.provinceSandMapXAxis,
              axisPointer: {
                type: 'shadow'
              },
              axisLine: {
                lineStyle: {
                  color: '#808080'
                }
              },
              axisLabel: {
                textStyle: {
                  color: '#808080'
                },
                rotate: 30,
                interval: 0
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '累计运砂量(' + this.provinceSandMapUnit + ')',
              // min: totalLoadRange[0],
              // max: totalLoadRange[1] + totalLoadInterval,
              // interval: totalLoadInterval,
              axisLabel: {
                formatter: '{value} '
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#e6e6e6'
                }
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#808080'
                }
              },
              axisTick: {
                show: true,
              }
            },
            {
              type: 'value',
              name: '个数',
              // min: combinedRange[0],
              // max: combinedRange[1] + combinedInterval,
              // interval: combinedInterval,
              axisLabel: {
                formatter: '{value}'
              },
              splitLine: {
                show: false // 不显示x轴网格线
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#808080'
                }
              },
              axisTick: {
                show: true,
              }
            }
          ],
          series: [
            {
              name: '累计运砂量',
              type: 'line',
              tooltip: {
                valueFormatter: function (value) {
                  return value + this.provinceSandMapUnit;
                }
              },
              data: this.provinceSandMapTotalLoad
            },
            {
              name: '在运单数',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + '个';
                }
              },
              data: this.provinceSandMapTranit
            },
            {
              name: '核销单数',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 个';
                }
              },
              data: this.provinceSandMapCompleted
            },
            {
              name: '项目数量',
              type: 'bar',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value) {
                  return value + ' 个';
                }
              },
              data: this.provinceSandMapProject
            }
          ]
        };
        this.provinceSandMap.setOption(option)
        if (this.provinceSandMapXAxis.length > 11) {
          // 动态添加dataZoom
          this.provinceSandMap.setOption({
            dataZoom: [
              {
                type: 'inside',
                start: 0,
                end: 50,
                bottom: 5,
                height: 20,     // 组件的高度
                backgroundColor: 'rgba(47,69,84,0)', // 背景颜色
                fillerColor: 'rgba(167,183,204,0.4)', // 填充颜色
                handleColor: 'rgba(89,105,128,0.7)', // 滑块颜色
              },
              {
                start: 0,
                end: 50,
                bottom: 5,
                height: 20,
                backgroundColor: 'rgba(47,69,84,0)', // 背景颜色
                fillerColor: 'rgba(167,183,204,0.4)', // 填充颜色
                handleColor: 'rgba(89,105,128,0.7)', // 滑块颜色
              }
            ],
          });
        }
      })
    },

    //时间为x轴的采砂量累计对比图表
    getCityLoadByTimeAndCity() {
      var code = ''
      console.log("this.queryParams.areaCode:" + this.queryParams.areaCode)
      console.log("this.defaultCityCode:" + this.defaultCityCode)
      if (this.queryParams.areaCode == '') {
        code = this.defaultCityCode
      } else {
        code = this.queryParams.areaCode
      }

      getCityLoad(code, this.citySandStartTime, this.citySandEndTime, this.queryParams.projectId).then(res => {
        console.log(res)
        this.citySandMapXAxis = []
        this.citySandMapTotalLoad = []
        res.list.forEach(item => {
          this.citySandMapXAxis.push(item.billTime);
          this.citySandMapTotalLoad.push(item.totalLoad !== null ? item.totalLoad : 0);
        });

        //判断是否需要转变数据位数和单位
        if (this.citySandMapTotalLoad.length > 0) {
          if (this.citySandMapTotalLoad.some(value => value >= 10000000000000)) {
            this.citySandMapTotalLoad = this.citySandMapTotalLoad.map(value => (value / 1000000000000).toFixed(2)); //万亿吨
            this.citySandMapUnit = '万亿吨'
          } else if (this.citySandMapTotalLoad.some(value => value >= 1000000000 && value < 10000000000000)) {
            this.citySandMapTotalLoad = this.citySandMapTotalLoad.map(value => (value / 100000000).toFixed(2)); // 亿吨
            this.citySandMapUnit = '亿吨'
          } else if (this.citySandMapTotalLoad.some(value => value >= 100000 && value < 1000000000)) {
            this.citySandMapTotalLoad = this.citySandMapTotalLoad.map(value => (value / 10000).toFixed(2)); // 万吨
            this.citySandMapUnit = '万吨'
          } else if (this.provinceSandMapTotalLoad.every(value => value < 100000)) {
            this.citySandMapUnit = '吨'
          }
        }

        var option = {
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%'];
            }
          },
          // title: {
          //   left: 'left',
          //   text: this.citySandMapName + '采砂统计'
          // },
          legend: {
            data: ['累计运砂量']
          },
          // toolbox: {
          //   feature: {
          //     dataZoom: {
          //       yAxisIndex: 'none'
          //     },
          //     restore: {},
          //     // saveAsImage: {}
          //   }
          // },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.citySandMapXAxis,
            axisLine: {
              lineStyle: {
                color: '#808080'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#808080'
              },
              // rotate: 20,
              // interval: 40
            }
          },
          yAxis: {
            type: 'value',
            name: '累计运砂量(' + this.citySandMapUnit + ')',
            splitLine: {
              show: false,
              lineStyle: {
                color: '#e6e6e6'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#808080'
              }
            },
            axisTick: {
              show: true,
            }
          },
          series: [
            {
              name: '累计运砂量',
              data: this.citySandMapTotalLoad,
              type: 'line',
              // symbol: 'none',
              // sampling: 'lttb',
              itemStyle: {
                color: '#1890FF'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#1890FF'
                  },
                  {
                    offset: 1,
                    color: '#ffffff'
                  }
                ])
              },
            }
          ]
        };

        this.citySandMap.setOption(option)
      })
    },

    //项目级采砂信息统计图表
    getProjectLoadByTime() {
      getProjectLoad(this.projectSandStartTime, this.projectSandEndTime).then(res => {
        console.log("**************" + res)
        this.projectSandMapName = res.projectName
        this.projectSandMapXAxis = []
        this.projectSandMapTotalLoad = []
        this.projectSandMapCompleted = []
        this.projectSandMapTranit = []
        res.list.forEach(item => {
          this.projectSandMapXAxis.push(item.billTime);
          this.projectSandMapTotalLoad.push(item.totalLoad !== null ? item.totalLoad : 0);
          this.projectSandMapCompleted.push(item.completedSum !== null ? item.completedSum : 0);
          this.projectSandMapTranit.push(item.transitSum !== null ? item.transitSum : 0);
        });
        // 计算总和
        this.totalVehicleLoad = this.projectSandMapTotalLoad.reduce((acc, val) => acc + val, 0).toFixed(2);
        this.completedBillCount = this.projectSandMapCompleted.reduce((acc, val) => acc + val, 0);
        this.transitBillCount = this.projectSandMapTranit.reduce((acc, val) => acc + val, 0);
        //判断是否需要转变数据位数和单位
        if (this.projectSandMapTotalLoad.length > 0) {
          if (this.projectSandMapTotalLoad.some(value => value >= 10000000000000)) {
            this.projectSandMapTotalLoad = this.projectSandMapTotalLoad.map(value => (value / 1000000000000).toFixed(2)); //万亿吨
            this.projectSandMapUnit = '万亿吨'
          } else if (this.projectSandMapTotalLoad.some(value => value >= 1000000000 && value < 10000000000000)) {
            this.projectSandMapTotalLoad = this.projectSandMapTotalLoad.map(value => (value / 100000000).toFixed(2)); // 亿吨
            this.projectSandMapUnit = '亿吨'
          } else if (this.projectSandMapTotalLoad.some(value => value >= 100000 && value < 1000000000)) {
            this.projectSandMapTotalLoad = this.projectSandMapTotalLoad.map(value => (value / 10000).toFixed(2)); // 万吨
            this.projectSandMapUnit = '万吨'
          } else if (this.provinceSandMapTotalLoad.every(value => value < 100000)) {
            this.projectSandMapUnit = '吨'
          }
        }
        var option = {
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%'];
            }
          },
          legend: {
            data: ['累计运砂量']
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.projectSandMapXAxis,
            axisLine: {
              lineStyle: {
                color: '#808080'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#808080'
              },
              // rotate: 20,
              // interval: 40
            }
          },
          yAxis: {
            type: 'value',
            name: '累计运砂量(' + this.projectSandMapUnit + ')',
            splitLine: {
              show: false,
              lineStyle: {
                color: '#e6e6e6'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#808080'
              }
            },
            axisTick: {
              show: true,
            }
          },
          series: [
            {
              name: '累计运砂量',
              data: this.projectSandMapTotalLoad,
              type: 'line',
              // symbol: 'none',
              // sampling: 'lttb',
              itemStyle: {
                color: '#1890FF'
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#1890FF'
                  },
                  {
                    offset: 1,
                    color: '#ffffff'
                  }
                ])
              },
            }
          ]
        };
        this.projectSandMap.setOption(option)
      })
    },

    //获取检索开始日期
    getStartOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if (value == '本年') {
        return `${currentYear}-01-01`; // 返回格式为 yyyy-MM-dd
      }else if(value == '昨日'){
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return common.formatDate(yesterday, 'yyyy-MM-dd');
      }else if (value == '本月') {
        return `${currentYear}-${currentMonth}-01`;
      } else {
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    //获取检索结束日期
    getEndOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if (value == '本年') {
        return `${currentYear}-12-31`; // 返回格式为 yyyy-MM-dd
      } else if (value == '昨日') {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return common.formatDate(yesterday, 'yyyy-MM-dd');
      }else if (value == '本月') {
        return `${currentYear}-${currentMonth}-31`;
      } else {
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    //根据吨数换算采砂重量单位
    weightConversion(load) {
      if (load == null)
        return 0;
      if (load < 100000) {
        return (load)
      }
      else if (load >= 100000 && load < 1000000000) {
        return (load / 10000).toFixed(2)
      }
      else if (load >= 1000000000 && load < 10000000000000) {
        return (load / 100000000).toFixed(2)
      }
      else {
        return (load / 1000000000000).toFixed(2)
      }
    },
    //项目名称change后更新城市采砂信息图表
    getAreaName1(projectName) {
      console.log("项目地址更新执行方法1:" + projectName)

      this.getProvinceLoadByYear();
      this.getCityLoadByTimeAndCity()
      // this.getProjectList();
      //更新项目表格
      this.loadProjectTable();
      this.queryDriverCount();

    },
    //项目地址change后更新城市采砂信息图表
    getAreaName(areaName) {
      console.log("项目地址更新执行方法:" + areaName)
      this.queryParams.projectId='';
      this.queryParams.areaName = areaName
      this.provinceSandMapName = areaName
      this.citySandMapName = areaName
      if (this.queryParams.areaCode == '') {
        this.provinceSandMapName = '河北省';
        this.defaultCityCode = '000_013';
        this.citySandMapName = '河北省';
        // return
      }
      if(this.queryParams.areaCode.length==15){
        this.mydisabled=false;
      }else{
        this.mydisabled=true;
      }
      console.log("切换的地址是那个级别:" + this.queryParams.areaCode.length)
      this.getMeprojectList(this.queryParams.areaCode, this.provinecSandStartTime, this.provinecSandEndTime);
      this.getProvinceLoadByYear();
      this.getCityLoadByTimeAndCity()
      this.getProjectList();
      //更新项目表格
      this.loadProjectTable();
      this.queryDriverCount();

    },
    //时间选择器change后更新城市采砂信息图表
    handleDateChange() {
      const value = [this.provinecSandStartTime, this.provinecSandEndTime]
      console.log("时间选择器执行方法:" + value)
      this.selectedTime = '';

      if (this.projectSandFlag) {
        if (value) {
          this.projectSandStartTime = value[0];
          this.projectSandEndTime = value[1];
        } else {
          this.projectSandStartTime = this.formatDate(this.getLastYearStart());
          this.projectSandEndTime = this.formatDate(new Date());
        }
        this.getProjectLoadByTime();
      } else {
        if (value) {
          this.provinecSandStartTime = value[0];
          this.provinecSandEndTime = value[1];
          this.citySandStartTime = value[0];
          this.citySandEndTime = value[1];
        } else {
          this.provinecSandStartTime = '';
          this.provinecSandEndTime = '';
          this.citySandStartTime = this.formatDate(this.getLastYearStart());
          this.citySandEndTime = this.formatDate(new Date());
        }
        this.getMeprojectList(this.queryParams.areaCode, this.provinecSandStartTime, this.provinecSandEndTime);
        this.getProjectList();
        this.getProvinceLoadByYear();
        this.getCityLoadByTimeAndCity();
        this.queryDriverCount();
      }

    },
    // handleStartDateChange(){
    //   if (this.projectSandFlag) {
    //     this.projectSandStartTime = this.provinecSandStartTime
    //     this.projectSandEndTime = this.provinecSandEndTime
    //     this.getProjectLoadByTime();
    //   }else {
    //     this.citySandStartTime = this.provinecSandStartTime
    //     this.citySandEndTime = this.provinecSandEndTime
    //     this.getMeprojectList(this.queryParams.areaCode, '', '');
    //     this.getProjectList();
    //     this.getProvinceLoadByYear();
    //     this.getCityLoadByTimeAndCity();
    //     this.queryDriverCount();
    //   }
    // },

    //时间选择器默认选择最近一个月
    getLastMonthStart() {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
      return start;
    },
    //时间选择器默认选择最近一个月
    getLastYearStart() {
      const start = new Date();
      const year = start.getFullYear() - 1;
      return new Date(year, start.getMonth(), start.getDate());
    },
    //时间格式化
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // 格式化为 yyyy-MM-dd
    },
    //时间快捷切换
    handleTimeChange(value) {
      console.log(value)
      this.timeRange = [this.getStartOfTime(value), this.getEndOfTime(value)]
      if (this.projectSandFlag) {
        this.projectSandStartTime = this.getStartOfTime(value);
        this.projectSandEndTime = this.getEndOfTime(value);
        this.getProjectLoadByTime();
      } else {
        this.provinecSandStartTime = this.getStartOfTime(value);
        this.provinecSandEndTime = this.getEndOfTime(value);
        this.citySandStartTime = this.getStartOfTime(value);
        this.citySandEndTime =  this.getEndOfTime(value);
        this.getMeprojectList(this.queryParams.areaCode, this.provinecSandStartTime, this.provinecSandEndTime);
        this.getProvinceLoadByYear();
        this.getCityLoadByTimeAndCity();
      }

    },

    /** 项目列表-多选框选中数据 */
    handleSelectionChange() {
      this.deptIds = this.$refs.projectTable.getSelectRowKeys();
      this.single = this.deptIds.length !== 1;
      this.multiple = !this.deptIds.length;
    },
    handleSortChange(opt, obj) {
      this.queryParams.sidx = obj.sidx
      this.queryParams.order = obj.order
    },

    //获取采运量预警颜色
    getProgressColor(minedRate) {
      if (minedRate < 0.9) {
        return '#13CE66'; // 绿色
      } else if (minedRate >= 0.9 && minedRate < 1) {
        return '#FFBA00'; // 黄色
      } else {
        return '#ff4d4d'; // 红色
      }
    },

    //项目名称选择
    handleProjectNameSelect(value) {
      console.log("项目名称选择:" + value)
      this.queryProject.projectName = value;
      //更新项目表格
      this.loadProjectTable();
    },

    //项目标段选择
    handleProjectSectionNameSelect(value) {
      console.log("项目标段选择:" + value)
      this.queryProject.sectionName = value;
      //更新项目表格
      this.loadProjectTable();
    },

    //项目类型选择
    handleProjectTypeSelect(value) {
      console.log("项目类型选择:" + value)
      this.queryProject.type = value;
      //更新项目表格
      this.loadProjectTable();
    },

    //预警类型选择
    handleWarningTypeSelect(value) {
      console.log("项目类型选择:" + value)
      this.queryProject.warningType = value;
      //更新项目表格
      this.loadProjectTable();
    },

    // 项目表格查询
    loadProjectTable() {
      let params = {
        projectName: this.queryProject.projectName,
        sectionName: this.queryProject.sectionName,
        type: this.queryProject.type,
        areaCode: this.queryParams.areaCode,
        warningType: this.queryProject.warningType,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
      }
      this.$refs.projectTable.search(params, true);
      this.single = true;
      this.multiple = true;
    },

    //获取项目列表
    getProjectList() {
      getProjectList(this.queryParams).then(res => {
        console.log(res, '项目')
        this.queryProjectName = [];
        this.querySectionName = [];
        this.projectContent = res.projectList;
        // this.projectList=res.
        // 循环项目列表，将项目名称存储到queryProjectName中，再将项目标段存储到querySectionName中，只有当name和sectionName不为空时存入
        for (let i = 0; i < this.projectList.length; i++) {
          const projectName = this.projectList[i].name;
          const sectionName = this.projectList[i].sectionName;

          // 检查项目名称是否存在于queryProjectName中
          if (projectName != null && projectName !== "" && !this.queryProjectName.some(item => item.value === projectName)) {
            this.queryProjectName.push({ name: projectName, value: projectName });
          }

          // 检查标段名称是否存在于querySectionName中
          if (sectionName != null && sectionName !== "" && !this.querySectionName.some(item => item.value === sectionName)) {
            this.querySectionName.push({ name: sectionName, value: sectionName });
          }
        }
      })
    }

  }
};
</script>

<style scoped lang="scss">
* {
  margin: 0;
}

.card-box {
  /* padding-bottom: 20px; */
  cursor: pointer;
}

.mycard {
  padding: 10px 20px 0px 20px;
}

.cardOne {
  background-color: #1890FF;
  height: 120px;
  position: relative;
}

.content {
  color: #FFFFFF;
  font-size: 38px;
  font-weight: bold;
  padding-bottom: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.title {
  color: #FFFFFF;
  text-align: left;
}

.icon {
  font-size: 80px;
  position: absolute;
  top: 20px;
  right: 15px;
  color: rgba(0, 0, 0, 0.15);
  transition: all .3s linear;
}

.card-box:hover .icon {
  font-size: 90px;
  position: absolute;
  top: 15px;
  right: 15px;
}

.cardTwo {
  background-color: #13CE66;
  height: 120px;
  position: relative;

}

.cardFive {
  background-color: #FFBA00;
  height: 120px;
  position: relative;

}

.cardThree {
  background-color: #FFA4A4;
  height: 120px;
  position: relative;

}

.cardFour {
  background-color: #827CFA;
  height: 120px;
  position: relative;

}

.el-card ::v-deep .el-card__header {
  border-bottom: none;
  min-height: 0px;
  padding: 10px 20px 0px 20px;
}

.el-card ::v-deep .el-card__body {
  padding: 10px 20px 0px 20px;
}

@media (min-width: 992px) {

  /* 中等屏幕的断点 */
  .custom-md-col {
    width: 20%;
    /* 您自定义的中等屏幕宽度 */
  }
}

@media (min-width: 1200px) {

  /* 超大屏幕的断点 */
  .custom-xl-col {
    width: 20%;
    /* 您自定义的超大屏幕宽度 */
  }
}

@media (max-width: 768px) {
  .el-date-editor {
    max-width: 70%;
  }

  .el-form-item {
    min-width: 500px;
    margin-top: 10px;
  }

  .el-radio-group {
    padding-left: 68px;
  }
}

.my-progress ::v-deep .el-progress-bar__innerText {
  color: rgb(90, 89, 89) !important;
  /* 强制设置文本颜色为黑色 */
}

::v-deep .is-left .cell {
  display: flex !important;
  align-items: center !important;
}
</style>
