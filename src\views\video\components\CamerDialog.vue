<!-- 封装dialog组件，添加固定表头 -->
<template>
  <el-dialog v-if="open" v-bind="$attrs" v-on="$listeners" :fullscreen="dialogFullscreen" :show-close="false"
             :close-on-click-modal="closeOnClickModal"
             :visible.sync="open" @close="handleClose" :destroy-on-close="destroyOnClose" :close-on-press-escape="false"
             :z-index="zIndex" :style="!dialogFullscreen?'margin-top: '+top+'!important;':''">
    <template #default>
      <div class="videoHeadIcon">
        <a class="a-icon" @click="dialogFullscreen=false">
          <i  v-if="dialogFullscreen" class="el-icon-document-copy" style="color: white"></i>
        </a>
        <a class="a-icon" @click="dialogFullscreen=true">
          <i  v-if="!dialogFullscreen" class="el-icon-full-screen" style="color: white"></i>
        </a>
        <a class="a-icon" @click="handleClose">
          <i class="el-icon-close" style="color: white"></i>
        </a>
      </div>
      <div class="dialog-body" :style="'height:'+height">
        <slot></slot>
      </div>
    </template>
  </el-dialog>
</template>

<script>

export default {
  emits: ["close", "update:visible","screenChange"],
  name: "CamerDialog",
  props: {
    title: '',
    visible: null,
    height: {
      type: String,
      default: 'auto'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: { //关闭后销毁其中的元素
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    top: {
      type: String,
      default: '0vh'
    }
  },
  data() {
    return {
      dialogFullscreen: false,
      open: false,
    }
  },
  watch: {
    open(nval) {
      this.$emit("update:visible", nval);
    },
    dialogFullscreen(nval) {
      this.$emit("screenChange", nval);
    },
    visible(nval) {
      this.open = nval;
    },
  },
  methods: {
    handleClose() {
      this.open = false;
      this.dialogFullscreen=false;
      this.$emit('close')
    },
  },
  mounted() {
    if (this.visible !== null) {
      this.open = this.visible;
    }
  },
}
</script>

<style scoped>
.dialog-body {
  position: relative;
  overflow-y: auto;
  margin-bottom: -25px;
  margin-top: -50px;
}
.videoHeadIcon{
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 3000;
}

.a-icon:hover {
  color: #00a0e9;
}

.a-icon {
  margin: 0 5px;
}

::v-deep .el-dialog__body {
  margin: 0 -25px!important;
}


</style>
