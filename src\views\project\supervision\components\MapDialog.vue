<!-- 封装dialog组件，添加固定表头 -->
<template>
  <el-dialog v-if="open" v-bind="$attrs" v-on="$listeners" :fullscreen="dialogFullscreen" :show-close="false"
             :close-on-click-modal="closeOnClickModal"
             :visible.sync="open" @close="handleClose" :destroy-on-close="destroyOnClose" :close-on-press-escape="false"
             :z-index="zIndex" :style="!dialogFullscreen?'margin-top: '+top+'!important;':''">
    <template #title="{ close, titleId, titleClass }" >
      <div class="topBackground">
        <el-row style="padding: 10px 10px">
          <el-col :span="18">
            <h3 style="margin:0 0 0 25%;color: white;font-family: 'AlimamaShuHeiTi-Bold';" :id="titleId" :class="titleClass">{{ title }}</h3>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <a class="a-icon" @click="dialogFullscreen=false">
              <i  v-if="dialogFullscreen" class="el-icon-document-copy"></i>
            </a>
            <a class="a-icon" @click="dialogFullscreen=true">
              <i  v-if="!dialogFullscreen" class="el-icon-full-screen"></i>
            </a>
            <a class="a-icon" @click="handleClose">
              <i class="el-icon-close"></i>
            </a>
          </el-col>
        </el-row>
      </div>
    </template>
    <template #default>
      <div class="dialog-body" :style="'height:'+height">
        <slot></slot>
      </div>
    </template>
  </el-dialog>
</template>

<script>

export default {
  emits: ["close", "update:visible","screenChange"],
  name: "MapDialog",
  props: {
    title: '',
    visible: null,
    height: {
      type: String,
      default: 'auto'
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: { //关闭后销毁其中的元素
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    top: {
      type: String,
      default: '0vh'
    }
  },
  data() {
    return {
      dialogFullscreen: false,
      open: false,
    }
  },
  watch: {
    open(nval) {
      this.$emit("update:visible", nval);
    },
    dialogFullscreen(nval) {
      this.$emit("screenChange", nval);
    },
    visible(nval) {
      this.open = nval;
    },
  },
  methods: {
    handleClose() {
      this.open = false;
      this.dialogFullscreen=false;
      this.$emit('close')
    },
  },
  mounted() {
    if (this.visible !== null) {
      this.open = this.visible;
    }
  },
}
</script>

<style scoped>

@font-face {
  font-family: "AlimamaShuHeiTi-Bold";
  src: url('../../../../assets/onemap/font/AlimamaShuHeiTi-Bold.ttf');
}
.topBackground{
  /*background-color:rgba(2, 15, 34, 0);*/
  box-shadow: 0px 0px 18px 0px #0A5075, inset 0px 0px 35px 0px #4F91B3;
  background-image: url('../../../../assets/onemap/images/title.png');
  background-repeat: no-repeat;
  background-size: cover;
  border: 2px solid #0273AE;
  border-bottom: 0;
  border-radius: 4px 4px 0 0;
}

.dialog-body {
  border: 2px solid #0273AE;
  border-top: 0;
  box-shadow: 0px 0px 18px 0px #0A5075, inset 0px 0px 35px 0px #4F91B3;
  border-radius: 0 0 4px 4px;
  overflow-y: auto;
  /*position: relative;*/
  /*margin-bottom: -25px;*/
  /*margin-top: -10px;*/
}
.a-icon:hover {
  color: #00a0e9;
}

.a-icon {
  font-size: 20px;
  color: white;
  margin: 0 5px;
}
::v-deep .el-dialog {
  background: rgba(2, 15, 34, 0.62) !important;
}
::v-deep .el-dialog__header{
  padding: 0 !important;
}
::v-deep .el-dialog__body {
  padding: 0 0;
}
::v-deep #inlineFrameExample {
  margin-bottom: -4px;
}


</style>
