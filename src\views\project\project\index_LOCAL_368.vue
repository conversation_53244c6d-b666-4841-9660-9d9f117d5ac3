<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <my-input style="width: 205px" v-model.trim="queryParams.name" placeholder="项目名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input style="width: 205px" v-model.trim="queryParams.sectionName" placeholder="标段名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <my-select id="type" pvalue="projectType" v-model="queryParams.type" placeholder="项目类型" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['project:project:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single"
          @click="() => handleUpdate({}, 'project')" v-hasPermi="['project:project:update']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['project:project:delete']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload(null, 'project')"></right-toolbar>
    </el-row>

    <my-table url="/project/project/page" ref="projectTable" row-key="deptId" :fixed="true"
      cell-class-name="el-table-max-cell2" @my-selection-change="handleSelectionChange"
      @my-sort-change="handleSortChange">
      <el-table-column label="项目名称" fixed="left" header-align="center" align="left" min-width="250" prop="name"
        sortable="custom" column-key="project.NAME">
        <template #default="scope">
          <!-- <el-tooltip :content="scope.row.name" placement="top" effect="light">
            <div>{{ scope.row.name }}</div>
          </el-tooltip> -->
          <div @click="handleViewProject(scope.row)" style="cursor: pointer;">{{ scope.row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="标段名称" align="left" header-align="center" min-width="160" prop="sectionName"
        sortable="custom" fixed="left" column-key="project.SECTION_NAME">
      </el-table-column>
      <el-table-column label="项目位置" align="left" header-align="center" min-width="160" prop="areaName" sortable="custom"
        column-key="project.AREA_NAME">
      </el-table-column>
      <el-table-column label="项目类型" align="center" header-align="center" min-width="160" prop="type" sortable="custom"
        column-key="project.TYPE">
        <template slot-scope="scope">
          <my-view pvalue="projectType" :value="scope.row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="追加申请状态" header-align="center" align="center" min-width="150" prop="additionalStatus"
        sortable="custom" column-key="project.ADDITIONALSTATUS">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.additionalStatus=='pass'">审核通过</el-tag>
          <el-tag type="danger" v-else-if="scope.row.additionalStatus=='reject'">审核不通过</el-tag>
          <el-tag type="warning" v-else-if="scope.row.additionalStatus=='cityWaiting'">市级待审核</el-tag>
          <el-tag type="warning" v-else-if="scope.row.additionalStatus=='departmentWaiting'">厅级待审核</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="采砂人（供砂人）" header-align="center" align="center" min-width="150" prop="leaderName"
        sortable="custom" column-key="project.LEADER_NAME">
      </el-table-column>
      <el-table-column label="联系电话" align="center" header-align="center" min-width="120" prop="contact"
        sortable="custom" column-key="project.CONTACT">
      </el-table-column>
      <el-table-column label="监管部门" align="left" header-align="center" min-width="150" prop="deptName" sortable="custom"
        column-key="dept.NAME">
      </el-table-column>
      <el-table-column label="控制总量(万吨)" align="right" header-align="center" min-width="150" prop="totalYield"
        sortable="custom" column-key="project.TOTAL_YIELD">
        <template #default="scope">
          {{ common.toThousands(scope.row.totalYield, 2, ',') }}
        </template>
      </el-table-column>
      <el-table-column label="采砂许可证/弃砂审批文号" align="center" header-align="center" min-width="200" prop="licenseNo"
        sortable="custom" column-key="project.LICENSE_NO">
      </el-table-column>
      <el-table-column label="操作" min-width="280" header-align="center" column-key="caozuo" fixed="right"
        align="center">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-tickets"
            title="查看附件"
            @click="handleFileList(scope.row)"
            v-hasPermi="['project:project:viewFile']"
          ></el-button>-->
          <el-button size="mini" type="danger" v-hasPermi="['project:project:applyups']" class="btn-table-operate" title="追加运砂量" v-if="scope.row.additionalStatus=='reject'||scope.row.additionalStatus=='pass'" @click="openDialog(scope.row)">申请追加</el-button>
          <el-button size="mini" type="danger" class="btn-table-operate" title="查看追加申请" @click="showDialog(scope.row)"
            v-hasPermi="['project:project:toexamine']" v-if="scope.row.additionalStatus=='cityWaiting'&&statePk==1">查看申请</el-button>
            <el-button size="mini" type="danger" class="btn-table-operate" title="查看追加申请" @click="showDialog(scope.row)"
              v-hasPermi="['project:project:toexamine']" v-if="scope.row.additionalStatus=='departmentWaiting'&&statePk==2">查看申请</el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-document-copy" title="查看项目详情"
            @click="handleViewProject(scope.row)" v-hasPermi="['project:project:info']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-map-location" title="磅站管理 "
            @click="handleWeighingStation(scope.row)" v-hasPermi="['project:weighingStation:list']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-setting" title="项目配置 "
            @click="handleProjectConfig(scope.row)" v-hasPermi="['project:projectConfig:info']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-menu" title="项目二维码 "
            @click="handleProjectCode(scope.row)" v-hasPermi="['project:project:viewProjectCode']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-s-grid" title="项目小程序码"
            @click="handleProjectWxCode(scope.row)" v-hasPermi="['project:project:viewProjectWxCode']"></el-button>
          <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-download" title="下载二维码贴纸"
            @click="downloadWxCode(scope.row)" v-hasPermi="['project:project:downloadWxCode']"></el-button>
        </template>
      </el-table-column>
    </my-table>
    <el-dialog title="追加申请" :visible.sync="dialogVisible" width="500px" @close="handleClose">
      <div class="dialog-content">
        <el-form :model="formzj" ref="formzjRef" :rules="rulezj" label-width="80px">
          <el-form-item label="追加量" prop="supperAddYield">
            <el-input v-model="formzj.supperAddYield"
              @input="formzj.supperAddYield = $options.filters.number(formzj.supperAddYield)" placeholder="请输入追加量">
              <template slot="append">万吨</template></el-input>
          </el-form-item>
          <el-form-item label="申请原因" prop="supperAddCause">
            <el-input type="textarea" v-model="formzj.supperAddCause" maxlength="500" placeholder="请输入申请原因"
              :rows="4"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="申请记录" :visible.sync="dialogVisiblesh" width="70%" @close="handleClosesh">
      <div style="padding: 20px 0;min-height: 450px;">
        <el-table :data="tableDatash" height="450px" border style="width: 100%;">
          <el-table-column prop="id" label="ID" width="140" align="center"></el-table-column>
          <el-table-column prop="supperAddYield" label="追加数量(万吨)" align="center">
            <template slot-scope="scope">
                {{ scope.row.supperAddYield }}万吨
              </template>
          </el-table-column>
          <el-table-column prop="supperAddCause" label="追加原因" align="center"></el-table-column>
          <el-table-column prop="auditStatus" label="状态" align="center">
            <template slot-scope="scope">
                <el-tag type="success" v-if="scope.row.auditStatus=='pass'">审核通过</el-tag>
                <el-tag type="danger" v-else-if="scope.row.auditStatus=='reject'">审核不通过</el-tag>
                <el-tag type="warning" v-else-if="scope.row.auditStatus=='cityWaiting'">市级待审核</el-tag>
                <el-tag type="warning" v-else-if="scope.row.auditStatus=='departmentWaiting'">厅级待审核</el-tag>
              </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="success" v-hasPermi="['project:project:applyCityBtn']" v-if="scope.row.auditStatus=='cityWaiting'&&statePk==1" @click="handleApprove(scope.row)">通过</el-button>
              <el-button size="mini" type="danger" v-hasPermi="['project:project:applyCityBtn']" v-if="scope.row.auditStatus=='cityWaiting'&&statePk==1" @click="showRejectDialog(scope.row)">驳回</el-button>
              <el-button size="mini" type="success" v-hasPermi="['project:project:applyProvincialBtn']" v-if="scope.row.auditStatus=='departmentWaiting'&&statePk==2" @click="handleApprove(scope.row)">通过</el-button>
              <el-button size="mini" type="danger" v-hasPermi="['project:project:applyProvincialBtn']" v-if="scope.row.auditStatus=='departmentWaiting'&&statePk==2" @click="showRejectDialog(scope.row)">驳回</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClosesh">关闭</el-button>
        <!-- <el-button type="primary" @click="handleSubmit">确定</el-button> -->
      </div>
    </el-dialog>
    <!-- 驳回原因弹窗 -->
    <el-dialog title="填写驳回原因" :visible.sync="rejectDialogVisible" width="70%" append-to-body>
      <div style="height: 280px; padding: 20px;">
      <el-input type="textarea" :rows="10" placeholder="请输入驳回原因" v-model="rejectReason"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleReject">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加或修改项目/沙场信息表对话框 -->
    <my-dialog :zIndex="1500" :title="title" :visible.sync="open" width="900px" height="70vh" append-to-body>
      <el-form ref="project" :model="project" label-width="180px" :disabled="mode === 'view'">
        <my-form-item label="项目名称" ref="name" prop="name" :rules="[{ notNull: true, message: '请输入项目名称' }]">
          <my-input v-model.trim="project.name" maxlength="50" placeholder="请输入项目名称" />
        </my-form-item>
        <my-form-item label="标段名称" ref="sectionName" prop="sectionName">
          <my-input v-model.trim="project.sectionName" maxlength="30" placeholder="请输入标段名称" />
        </my-form-item>
        <my-form-item label="项目位置" ref="areaCode" prop="areaCode"
          :rules="[{ notNull: true, message: '请选择项目位置', trigger: 'change' }]">
          <my-area-select v-model="project.areaCode" :component-type="true" :disabled="mode == 'view'"
            @change="getAreaName" />
        </my-form-item>
        <div v-hasPermi="['project:project:viewArea']">
          <my-form-item label="可采区坐标" prop="coords" v-if="readOnly">
            <el-input v-model="project.coords" type="textarea" placeholder=""
              :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </my-form-item>
          <!--  -->
          <div v-for="(item, index) in formItems" :key="item.formItemsId">
            <my-form-item :label="item.label" :ref="item.ref" prop="coords" v-show="item.visible" v-if="!readOnly">
              <div v-for="(coord, coordIndex) in item.listCoord" :key="coord.id" v-show="coord.visible"
                style="display:flex;flex-direction:row;align-items:center;">
                <el-tooltip content="弃置当前拐点" placement="left">
                  <i style="font-size:20px;color:red;cursor:pointer;margin-right:12px;" class="el-icon-error"
                    @click="onClickMinusCoordInput(index, coordIndex)"></i>
                </el-tooltip>
                <input-coords @selected="onSelectCoordInput" :ref="coord.ref"></input-coords>
              </div>
              <el-tooltip content="添加拐点" placement="left">
                <i style="font-size:20px;color:green;cursor:pointer;" class="el-icon-circle-plus"
                  @click="onClickPlusCoordInput(index)"></i>
              </el-tooltip>
              <br><el-button size="mini" type="primary" @click="onClickPlot(index)">去地图上勾绘</el-button>
              <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>

              <el-tooltip content="删除当前可采区" placement="left">
                <i style="font-size:20px;color:red;cursor:pointer;margin-left:12px;" class="el-icon-delete"
                  @click="onClickDeleteFormItem(index)"></i>
              </el-tooltip>

            </my-form-item>
          </div>
          <my-form-item>
            <el-button type="success" size="mini" @click="onClickAddFormItem">添加可采区</el-button>
          </my-form-item>
        </div>
        <geo-plotter ref="geoPlotter" @plotted="onPlotted"></geo-plotter>
        <my-form-item label="项目类型" ref="type" prop="type"
          :rules="[{ notNull: true, message: '请选择项目类型', trigger: 'change' }]">
          <my-select pvalue="projectType" v-model="project.type" placeholder="请选择项目类型" />
        </my-form-item>
        <my-form-item v-if="mode === 'view'" label="监管部门" ref="deptName" prop="deptName">
          <my-input v-model.trim="project.deptName" disabled="disabled" />
        </my-form-item>
        <my-form-item v-else label="监管部门" ref="parentId" prop="parentId"
          :rules="[{ notNull: true, message: '请选择监管部门', trigger: 'change' }]">
          <my-cascader ref="deptCascader" :disabled="mode === 'update'" v-model="project.parentId"
            placeholder="请选择监管部门" />
        </my-form-item>

        <my-form-item label="控制总量(万吨)" ref="totalYield" prop="totalYield" :rules="[{ notNull: true, message: '请输入控制总量(万吨)' },
        { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
          <my-input v-model="project.totalYield" maxlength="20" placeholder="请输入控制总量(万吨)" />
        </my-form-item>
        <my-form-item v-if="project.type == 'abandonSand'" label="25年1月1日前利用量（万吨）" ref="historyYield"
          prop="historyYield" :rules="[{ notNull: true, message: '请输入25年1月1日前利用量（万吨）' },
          { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
          <my-input v-model="project.historyYield" maxlength="20" placeholder="请输入25年1月1日前利用量（万吨）" />
        </my-form-item>
        <my-form-item :label="project.type == 'abandonSand' ? '弃砂方案是否报备' : '治河采砂方案是否报备'" ref="reportedFlag"
          prop="reportedFlag"
          :rules="[{ notNull: true, message: project.type == 'abandonSand' ? '弃砂方案是否报备' : '治河采砂方案是否报备' }]">
          <my-radio :options="[{ name: '是', value: true }, { name: '否', value: false }]"
            v-model="project.reportedFlag"></my-radio>
        </my-form-item>
        <my-form-item :label="project.type == 'abandonSand' ? '弃砂方案批文及文号' : '采砂许可证号'" ref="licenseNo" prop="licenseNo"
          :rules="[{ notNull: true, message: project.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号' }]">
          <my-input v-model="project.licenseNo" maxlength="128"
            :placeholder="project.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号'" />
        </my-form-item>
        <my-form-item :label="project.type == 'abandonSand' ? '供砂人' : '采砂人'" ref="leaderName" prop="leaderName"
          :rules="[{ notNull: true, message: project.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人' }]">
          <my-input v-model="project.leaderName" maxlength="128"
            :placeholder="project.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人'" />
        </my-form-item>
        <my-form-item label="联系电话" ref="contact" prop="contact" :rules="[{ notNull: true, message: '请输入联系电话' },
        { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
          <my-input v-model="project.contact" placeholder="请输入联系电话" />
        </my-form-item>

        <my-form-item v-if="project.type == 'abandonSand'" label="是否国债" ref="nationalDebt" prop="nationalDebt"
          :rules="[{ notNull: true, message: '请选择是否国债' }]">
          <my-radio :options="[{ name: '是', value: true }, { name: '否', value: false }]"
            v-model="project.nationalDebt"></my-radio>
        </my-form-item>
        <my-form-item v-if="project.nationalDebt == true && project.type == 'abandonSand'" label="国债类型"
          ref="nationalDebtType" prop="nationalDebtType" :rules="[{ notNull: true, message: '请选择国债类型' }]">
          <my-select pvalue="nationalDebtType" v-model="project.nationalDebtType" placeholder="请选择国债类型" />
        </my-form-item>
        <my-form-item v-if="project.type == 'abandonSand'" label="处置方式" ref="disposalMethodJson"
          prop="disposalMethodJson"
          :rules="[{ validator: validate, trigger: ['blur', 'change'] }, { notNull: true, message: '请选择处置方式' }]">
          <div v-for="item in project.disposalMethodJson">
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <el-checkbox v-model="item.selected" @change="changeCheckbox(item)">
                  <template>
                    <my-view pvalue="disposalMethod" :value="item.method"></my-view>
                  </template>
                </el-checkbox>
                <my-input v-if="item.selected && item.method == 'qiTa'" style="margin-left: 20px;width: 200px"
                  v-model.trim="item.remark" :maxlength="16" placeholder="请输入其他处置方式 " />
              </el-col>
              <el-col :span="12">
                <div>
                  <my-form-item label="效益（万元）" v-if="item.selected" label-width="120px" class="asterisk">
                    <my-input @input="inputAmount" v-model.trim="item.amount" :maxlength="16" placeholder="请输入效益（万元）" />
                  </my-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
          <div style="display: flex">
            <div>总效益（万元）</div>
            <div>{{ common.toThousands(investmentTotal, '4', ',') }}</div>
          </div>
        </my-form-item>
        <my-form-item v-if="project.type != 'abandonSand'" label="项目总投资（万元）" ref="investment" prop="investment"
          :rules="[{ notNull: true, message: '请输入项目总投资（万元）' }]">
          <my-input v-model.trim="project.investment" :maxlength="16" placeholder="请输入项目总投资（万元）" />
        </my-form-item>
        <my-form-item v-if="project.type == 'riverSand' || project.type == ''" label="治理河长（km）" ref="reverLength"
          prop="reverLength" :rules="[{ notNull: true, message: '请输入治理河长（km）' }]">
          <my-input v-model.trim="project.reverLength" :maxlength="16" placeholder="请输入治理河长（km）" />
        </my-form-item>
        <my-form-item label="监督举报电话" ref="complaintMobile" prop="complaintMobile"
          :rules="[{ notNull: true, message: '请输入监督举报电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
          <my-input v-model.trim="project.complaintMobile" placeholder="请输入监督举报电话" :maxlength="32" />
        </my-form-item>
        <my-form-item label="查询电话" ref="queryMobile" prop="queryMobile"
          :rules="[{ notNull: true, message: '请输入查询电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
          <my-input v-model.trim="project.queryMobile" placeholder="请输入查询电话" :maxlength="32" />
        </my-form-item>
        <el-row>
          <el-col :span="10">
            <my-form-item :label="project.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" prop="imgList"
              :rules="[{ required: true, validator: validatorImg, trigger: ['blur', 'change'] }]">
              <my-pv-upload :label="project.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" :multiple="false"
                :uploadMode="'image'" :file-list="imgList" :pathFieldName="pathFieldName1" :fileType="fileType1"
                :disabled="mode == 'view'" @getFileList="getFileList1">
              </my-pv-upload>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="监管部门印章" prop="imgList2"
              :rules="[{ required: true, validator: validatorImg2, trigger: ['blur', 'change'] }]">
              <my-pv-upload label="监管部门印章" :multiple="false" :uploadMode="'image'" :file-list="parentImgList"
                pathFieldName="parent-dept-img" :fileType="fileType1" :disabled="mode == 'view'"
                @getFileList="getFileList2">
              </my-pv-upload>
            </my-form-item>
          </el-col>
        </el-row>

        <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="uploadFileList" prop="uploadFileList"
          :rules="[{ required: true, validator: validatorImg3, trigger: ['blur', 'change'] }]">
          <my-file-upload :file-list="project.uploadFileList" :pathFieldName="pathFieldName" :fileType="fileType"
            @getFileList="getFileList" :disabled="mode == 'view'" />
          <template #label>
            <span>{{ project.type == 'abandonSand' ? '附件（方案批文）' : '附件（采砂许可证）' }}</span>
          </template>
        </my-form-item>
        <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="paiMaiFileList" prop="paiMaiFileList"
          :rules="dynamicRules">
          <!-- v-show="project.type == 'abandonSand' && project.disposalMethodJson[0].selected == true" -->
          <my-file-upload :file-list="project.paiMaiFileList" :pathFieldName="pathFieldName8" :fileType="fileType2"
            @getFileList="getFileList8" :disabled="mode == 'view'" />
          <template #label>
            <span>{{ project.type == 'abandonSand' ? '附件（中拍文件）' : '' }}</span>
          </template>
        </my-form-item>
        <my-form-item label="上述信息填报人联系电话" ref="createUserMobile" prop="createUserMobile" :rules="[{ notNull: true, message: '请输入上述信息填报人联系电话' },
        { isMobile: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
          <my-input v-model="project.createUserMobile" placeholder="请输入上述信息填报人联系电话" />
        </my-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="mode != 'view'" type="primary" @click="submitForm('project')">确 定</el-button>
        <el-button @click="cancel('project')">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 上传坐标对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :disabled="upload.isUploading"
        :auto-upload="false" drag :action="upload.action" :on-change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resolveCoords">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看附件列表弹窗 -->
    <my-dialog title="查看附件列表" min :visible.sync="openFileList" width="800px" height="70vh" append-to-body>
      <my-table :url="'/project/project/viewFile/' + deptId" maxHeight="500px" :show-pager="false" :multiselect="false"
        ref="fileTable" row-key="id">
        <el-table-column label="文件名称" header-align="center" align="center" min-width="300" prop="uploadFileName"
          :sortable="false" column-key="uploadFileName">
        </el-table-column>
        <el-table-column label="操作" min-width="120" header-align="center" column-key="caozuo" fixed="right"
          align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-view" title="预览"
              v-if="isPreview(scope.row)" @click="handleView(scope.row)"></el-button>
            <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-download" title="下载"
              @click="handleDownloadFile(scope.row)"></el-button>
            <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-delete" title="删除"
              @click="handleDeleteFile(scope.row)"></el-button>
          </template>
        </el-table-column>
      </my-table>
    </my-dialog>

    <!-- 预览图片遮罩层 -->
    <el-image-viewer style="z-index: 2050" v-if="imageVisible" :url-list="[imageUrl]"
      :on-close="() => { imageVisible = false }" />

    <!-- 预览pdf对话框 -->
    <my-dialog title="预览PDF" :visible.sync="openViewPdf" width="800px" height="70vh" append-to-body>
      <my-pdf-reader v-if="openViewPdf" :pdfUrl="pdfUrl" />
    </my-dialog>

    <!-- 磅站管理列表 -->
    <my-dialog title="磅站管理" :visible.sync="openWeighingStation" width="1000px" height="70vh" append-to-body>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd('weighingStation')"
            v-hasPermi="['project:weighingStation:save']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single1"
            @click="() => handleUpdate({}, 'weighingStation')"
            v-hasPermi="['project:weighingStation:update']">修改</el-button>
        </el-col>
      </el-row>

      <my-table :url="'/project/weighingStation/page/' + deptId" row-key="id" ref="weighingStationTable"
        @my-selection-change="handleSelectionChange1" max-height="430px">
        <el-table-column label="磅站名称" header-align="center" align="center" min-width="130" prop="name"
          column-key="name">
        </el-table-column>
        <el-table-column label="经度" header-align="center" align="center" min-width="100" prop="longitude"
          column-key="longitude">
        </el-table-column>
        <el-table-column label="纬度" header-align="center" align="center" min-width="100" prop="latitude"
          column-key="latitude">
        </el-table-column>
        <el-table-column label="详细地址" header-align="center" align="left" min-width="270" prop="address"
          column-key="address">
        </el-table-column>
        <el-table-column label="操作" min-width="120" header-align="center" column-key="caozuo" fixed="right"
          align="center" v-hasPermi="['project:weighingStation:delete']">
          <template slot-scope="scope">
            <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-delete" title="删除"
              @click="handleDelWeighingStation(scope.row)"></el-button>
          </template>
        </el-table-column>
      </my-table>
    </my-dialog>

    <!-- 新增/修改磅站对话框 -->
    <my-dialog :title="weighingStationTitle" :visible.sync="openAddWeighingStation" width="1000px" height="70vh"
      append-to-body>
      <el-form ref="weighingStation" :model="weighingStation" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="12">
            <my-form-item label="磅站名称" ref="name" prop="name" :rules="[{ notNull: true, message: '请输入磅站名称' }]">
              <my-input v-model="weighingStation.name" placeholder="请输入磅站名称" />
            </my-form-item>
          </el-col>
          <el-col :span="6">
            <my-form-item label="经度" ref="longitude" prop="longitude" :rules="[
              { notNull: true, message: '请输入经度', trigger: ['change', 'blur'] },
              { isNumber: true, message: '只能是数字', trigger: ['change', 'blur'] },
              { fn: validateLng, message: '纬度取值不符合要求', trigger: ['change', 'blur'] },
            ]">
              <my-input v-model="weighingStation.longitude" placeholder="请输入经度" :clearable="true" />
            </my-form-item>
          </el-col>
          <el-col :span="6">
            <my-form-item label="纬度" ref="latitude" prop="latitude" :rules="[
              { notNull: true, message: '请输入纬度', trigger: ['change', 'blur'] },
              { isNumber: true, message: '只能是数字', trigger: ['change', 'blur'] },
              { fn: validateLat, message: '纬度取值不符合要求', trigger: ['change', 'blur'] },
            ]">
              <my-input v-model="weighingStation.latitude" :clearable="true" placeholder="请输入纬度" />
            </my-form-item>
          </el-col>
        </el-row>

        <!-- 腾讯地图组件 -->
        <my-tencent-map :center="{ lat: 38.041323, lng: 114.514686 }" :zoom="12" :switchType="true"
          :lat="Number(weighingStation.latitude)" :lng="Number(weighingStation.longitude)"
          @change="getSelectPosition" />
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('weighingStation')">确 定</el-button>
        <el-button @click="cancel('weighingStation')">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 项目配置弹出层 -->
    <my-dialog title="项目配置" :visible.sync="projectConfig" width="600px" append-to-body>
      <el-form ref="projectConfig" :model="projectConfigForm" label-width="210px">
        <my-form-item label="经办人签名有效时长(分钟)" ref="leaderAuthDuration" prop="leaderAuthDuration"
          :rules="[{ isDigit: true, message: '只能是整数', trigger: ['change', 'blur'] }]">
          <my-input v-model="projectConfigForm.leaderAuthDuration" placeholder="请输入经办人签名有效时长(分钟)" />
        </my-form-item>
        <my-form-item label="监理签名授权时长(分钟)" ref="supervisorAuthDuration" prop="supervisorAuthDuration"
          :rules="[{ isDigit: true, message: '只能是整数', trigger: ['change', 'blur'] }]">
          <my-input v-model="projectConfigForm.supervisorAuthDuration" placeholder="请输入监理签名授权时长(分钟)" />
        </my-form-item>
        <!--        <my-form-item-->
        <!--          label="水行政主管签名授权时长(分钟)"-->
        <!--          ref="authorityAuthDuration"-->
        <!--          prop="authorityAuthDuration"-->
        <!--          :rules="[{isDigit:true, message: '只能是整数', trigger:['change','blur']}]"-->
        <!--        >-->
        <!--          <my-input v-model="projectConfigForm.authorityAuthDuration" placeholder="请输入水行政主管签名授权时长(分钟)" />-->
        <!--        </my-form-item>-->
        <my-form-item label="是否开启终端" ref="useClient" prop="useClient">
          <el-radio-group v-model="projectConfigForm.useClient">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </my-form-item>
        <my-form-item label="是否启用系统对接" ref="useOpenApi" prop="useOpenApi">
          <el-radio-group v-model="projectConfigForm.useOpenApi">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </my-form-item>
        <my-form-item v-if="projectConfigForm.useOpenApi" label="系统名称" ref="partnerId" prop="partnerId"
          :rules="[{ notNull: true, message: '请选择系统名称', trigger: ['change', 'blur'] }]">
          <el-select v-model="projectConfigForm.partnerId" filterable placeholder="请选择系统名称">
            <el-option v-for="item in partnerList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </my-form-item>
      </el-form>

      <div v-hasPermi="['project:projectConfig:save']" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('config')">确 定</el-button>
        <el-button @click="cancel('config')">取 消</el-button>
      </div>
    </my-dialog>

    <!-- 项目二维码弹出层 -->
    <my-dialog title="项目二维码" :visible.sync="openQrCode" width="320px" append-to-body>
      <div class="qrcode-title">项目名称：{{ qrCodeProjectName }}</div>
      <div v-if="openQrCode" ref="qrCodeDiv"></div>
    </my-dialog>
    <my-dialog title="项目小程序码" :visible.sync="openWxQrCode" width="320px" append-to-body>
      <div class="qrcode-title">项目名称：{{ qrCodeProjectName }}</div>
      <div v-if="openWxQrCode">
        <el-image :src="wxQrCode" alt="" fit="cover" style="height: 280px;width: 280px" />
      </div>
    </my-dialog>
  </div>
</template>

<script>
  import {
    getProject,
    delProject,
    delProjectBatch,
    addProject,
    updateProject,
    delProjectFile,
    getProjectConfig,
    updateProjectConfig,
    addProjectConfig,
    getProjectEncryptedString,
    getProjectWxCode,
    setAppeal,
    getProjectList,
    upStatus
  } from '@/api/project/project'
  import {
    addWeighingStation,
    delWeighingStation,
    getWeighingStation,
    updateWeighingStation
  } from "@/api/project/weighingStation";
  import MyAreaSelect from "@/components/YB/MyAreaSelect";
  import MyCascader from "@/components/YB/MyCascader";
  import MyFileUpload from "@/components/YB/MyFileUpload";
  import MyPdfReader from "@/components/YB/MyPdfReader";
  import MyTencentMap from "@/components/YB/MyTencentMap";
  import QRCode from "qrcodejs2-fix";
  import MyPvUpload from '@/components/YB/MyPvUpload.vue'
  import {
    uploadFileList
  } from '@/api/system/common'
  import {
    getCar
  } from '@/api/project/car'
  import {
    getPartnerAllList
  } from '@/api/open/partner'

  import InputCoords from '@/views/components/input-coords/index.vue'
  import GeoPlotter from "@/views/components/plotter/index.vue"
  import * as XLSX from 'xlsx';

  import L from 'leaflet';
  import Template from "@/views/sms/template/index.vue";
  import common from '../../../utils/common'
  import {
    numberValidator
  } from '@/utils/validators'
  export default {
    filters: {
      number: (value) => numberValidator(value)
    },
    name: "ProjectManage",
    computed: {
      dynamicRules() {
        return [{
          required: this.zpRequired, // 控制是否必填
          validator: this.validatorImg4,
          trigger: ['blur', 'change']
        }]
      },
      zpRequired() {
        // 根据业务逻辑返回 true/false
        return this.project.type == 'abandonSand' && this.project.disposalMethodJson[0].selected == true;
      },
      common() {
        return common
      },
      investmentTotal() {
        if (this.project.disposalMethodJson) {
          return this.project.disposalMethodJson.reduce((total, item) => {
            if (this.project.type == 'abandonSand') {
              if (item.selected) {
                if (item.amount) {
                  total += Number(item.amount)
                } else {
                  total += 0
                }
              }
              return total
            }
          }, 0)
        } else {
          return 0
        }

      }
    },
    components: {
      Template,
      MyPvUpload,
      MyAreaSelect: MyAreaSelect,
      MyCascader: MyCascader,
      MyFileUpload: MyFileUpload,
      'el-image-viewer': () => import("element-ui/packages/image/src/image-viewer"),
      MyPdfReader: MyPdfReader,
      MyTencentMap: MyTencentMap,

      InputCoords,
      GeoPlotter,

    },
    data() {
      return {
        statePk:'',
        dialogVisiblesh: false,
        rejectDialogVisible: false,
        tableDatash: [],
        rejectReason: '',
        currentRow: null,
        // 项目列表-选中数组
        deptIds: [],
        // 项目列表-非单个禁用
        single: true,
        // 项目列表-非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          name: '',
          areaCode: '',
          type: '',
          sectionName: '',
        },
        // 表单参数
        project: {
          disposalMethodJson: [{
              method: 'paiMai',
              amount: '',
              selected: false
            },
            {
              method: 'shiZheng',
              amount: '',
              selected: false
            },
            {
              method: 'qiTa',
              amount: '',
              selected: false,
              remark: ''
            }
          ],
        },
        // 表单状态
        mode: '',
        // 文件类型  常用文件类型：.txt,.doc,.docx,.pdf,.xls,.xlsx,.bmp,.gif,.jpeg,.png,.jpg,.zip,.rar,.7z,.tar
        fileType: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
        fileType1: '.bmp,.gif,.jpg,.jpeg,.png', //印章图片类型
        fileType2: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
        //印章图片
        imgList: [],
        parentImgList: [],
        dialogVisible: false,
        dialogVisibleSh: false,
        formzj: {
          supperAddYield: null,
          supperAddCause: ''
        },
        rulezj: {
          supperAddYield: [{
            required: true,
            message: '请输入追加量',
            trigger: 'blur'
          }],
          supperAddCause: [{
              required: true,
              message: '请输入申请原因',
              trigger: 'blur'
            },
            {
              min: 10,
              message: '至少输入10个字符',
              trigger: 'blur'
            }
          ]
        },
        dialogImageUrl: "",
        // 上传类型
        pathFieldName: 'project-file',
        pathFieldName1: 'seal-image-img',
        pathFieldName8: 'projectPaiMai-file',
        // 是否显示附件列表弹出层
        openFileList: false,
        // 数据主键
        deptId: '',
        // 是否显示预览图片弹出层
        imageVisible: false,
        // 图片路径
        imageUrl: '',
        // 是否显示预览pdf弹出层
        openViewPdf: false,
        // pdf文件路径
        pdfUrl: '',
        // 是否显示磅站管理列表弹出层
        openWeighingStation: false,
        // 磅站列表-选中数组
        ids: [],
        // 磅站列表-非单个禁用
        single1: true,
        // 磅站列表-非多个禁用
        multiple1: true,
        // 是否显示新增磅站弹出层
        openAddWeighingStation: false,
        // 磅站表单参数
        weighingStation: {},
        // 新增/修改磅站弹出层标题
        weighingStationTitle: '',
        // 是否显示项目配置弹出层
        projectConfig: false,
        // 项目配置相关字段
        projectConfigForm: {
          deptId: '',
          leaderAuthDuration: null,
          supervisorAuthDuration: null,
          authorityAuthDuration: null,
          useOpenApi: null,
          partnerId: '',
        },
        // 项目配置表单状态
        configMode: '',
        // 项目二维码弹出层
        openQrCode: false,
        openWxQrCode: false,
        // 项目二维码弹出层-项目名称
        qrCodeProjectName: '',
        partnerList: '', //系统名称
        wxQrCode: '',


        //添加采区坐标范围参数
        // listCoord: [
        //   { ref: "coord_0", id: 0, visible: true },
        // ],

        // 存储 my-form-item 组件的数组
        formItems: [{
          formItemsId: `formItem_0`, // my-form-item唯一 ID
          labelNum: 0,
          label: `可采区K0`, // my-form-item标签
          ref: "formItem_0", // my-form-item ref
          listCoord: [{
            ref: "formItem_0_coord_0",
            id: 'formItem_0_0',
            visible: true
          }], // 当前my-form-item中的坐标列表
          visible: true
        }],
        trueID: 0, //formItem顺序ID
        showID: 0, //label 显示的ID


        choiceFromItemIndex: null, // 当前选中的 my-form-item 的索引

        mapDialogs: {
          "road": null,
          "pipe": null,
          "wire": null,
          "park": null,
        },
        readOnly: true,

        //上传坐标点参数
        // 用户导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          action: '',
        },
        selectedFile: null,
        coordinates: [], // 存储经纬度坐标
        projectId: '',
        rejectId:"",
        projectListId:''
      };
    },
    created() {
      if (this.$store.state.user.deptcode.length == 9) {
        this.statePk = 1; //市级
      } else if (this.$store.state.user.deptcode.length > 9) {
        this.statePk = 0; //县级
      } else if (this.$store.state.user.deptcode.length < 9) {
        this.statePk = 2; //省级
      }
    },
    mounted() {

    },
    watch: {

    },
    activated() {
      //组件被激活时重绘表格
      this.$refs.projectTable.changeTableHeight();
    },

    methods: {
      getPro(projectId){
        getProjectList({projectId:projectId}).then(res => {
          if(res.code=='0'){
            this.tableDatash=res.projectAddtives;
          }
        });
      },
      myupStatus(id,status,opinion){
        let datas={
          id:id,
          status:status,
          opinion:opinion
        };
        upStatus(datas).then(res => {
          this.getPro(this.projectListId);
          if(res.code=='0'){
            if(status=='pass'){
              this.$message.success('操作成功');
            }else if(status=='reject'){
              this.rejectDialogVisible = false
              this.$message.success('已驳回申请')
            }
          }
          this.dialogVisiblesh = false;
           this.dialogVisible = false;
           this.handleQuery();
        });
      },
      handleClosesh(){
        this.dialogVisiblesh = false;
      },
      showDialog(row) {
        this.dialogVisiblesh = true;
        this.projectListId=row.deptId;
        this.getPro(row.deptId);
      },
      handleDialogClose() {
        this.dialogVisiblesh = false
      },
      handleApprove(row) {
        this.$confirm('确定通过该申请吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.myupStatus(row.id,'pass',null);
        })
      },
      showRejectDialog(row) {
        this.currentRow = row;
        this.rejectReason = ''
        this.rejectDialogVisible = true
      },
      handleReject() {
        if (!this.rejectReason.trim()) {
          this.$message.warning('请填写驳回原因')
          return
        }
        this.myupStatus(this.currentRow.id,'reject',this.rejectReason);
        // this.currentRow.status = '已驳回'
        // this.rejectDialogVisible = false
        // this.$message.success('已驳回申请')
      },
      openShDialog() {
        this.dialogVisibleSh = true
      },
      openDialog(row) {
        console.log(row.deptId, '更新')
        this.projectId = row.deptId;
        this.dialogVisible = true
      },
      handleClose() {
        this.dialogVisible = false
        this.$refs.formzjRef.resetFields()
      },
      handleCancel() {
        this.dialogVisible = false
      },
      handleSubmit() {
        this.$refs.formzjRef.validate(valid => {
          if (valid) {
            this.formzj.projectId = this.projectId;
            // this.dialogVisible = false
            setAppeal(this.formzj).then(res => {
              this.$modal.msgSuccess("申请成功");
              this.dialogVisible = false;
              this.reload(false, 'project');
            });
          }
        })
      },
      inputAmount() {
        this.$refs['project'].validateField('disposalMethodJson')
      },
      changeCheckbox(val) {
        this.$refs['project'].validateField('disposalMethodJson')
        if (!val.selected) {
          this.project.disposalMethodJson.map(i => {
            if (val.method == i.method) {
              i.amount = ''
            }
            if (val.method == 'qiTa') {
              i.remark = ''
            }
            return i
          })
        }

      },
      validate(rule, value, callback) {
        const selectArray = []
        value.map(i => {
          selectArray.push(i.selected)
        })
        if (selectArray.includes(true)) {
          for (let index = 0; index < value.length; index++) {
            let item = value[index];
            let method = ''
            if (value[index].method == 'paiMai') {
              method = '拍卖'
            } else if (value[index].method == 'shiZheng') {
              method = '用于市政民生工程'
            } else if (value[index].method == 'qiTa') {
              method = '其他'
            }
            if (item.selected) {
              if (!value[index].amount) {
                callback(new Error('请输入' + method + '效益（万元）'))
                return
              }
              if (value[index].amount) {
                if (!/^[1-9]\d*(\.\d{1,4})?$|^0(\.\d{1,4})?$/.test(value[index].amount)) {
                  callback(new Error(method + '效益（万元）必须大于0且必须为数字'))
                  return
                }
              }
              if (value[index].method == 'qiTa' && !value[index].remark) {
                callback(new Error('请输入其他处置方式'))
                return
              }
            }
          }
          callback()
        } else {
          callback(new Error('请选择处置方式'))
        }

      },
      validatorImg(rule, value, callback) {
        if (this.project.fileList == null || this.project.fileList.length <= 0) {
          callback(new Error("请上传印章图片"));
        } else {
          callback();
        }
      },
      validatorImg2(rule, value, callback) {
        if (this.project.parentImgList == null || this.project.parentImgList.length <= 0) {
          callback(new Error("请上传印章图片"));
        } else {
          callback();
        }
      },
      validatorImg3(rule, value, callback) {
        if (this.project.uploadFileList == null || this.project.uploadFileList.length <= 0) {
          callback(new Error("请上传附件"));
        } else {
          callback();
        }
      },
      validatorImg4(rule, value, callback) {
        if (this.project.type == 'abandonSand' && this.project.disposalMethodJson[0].selected == true) {
          if (this.project.paiMaiFileList == null || this.project.paiMaiFileList.length <= 0) {
            callback(new Error("请上传附件"));
          } else {
            callback();
          }
          console.log(1);
        } else {
          callback();
        }

      },
      /** 查询项目列表 */
      reload(restart, params) {
        if (params === 'project') {
          this.$refs.projectTable.search(this.queryParams, restart);
        } else {
          this.$refs.weighingStationTable.search({}, restart);
        }
        this.single = true;
        this.multiple = true;
      },
      /** 取消按钮 */
      cancel(params) {
        if (params === 'project') {
          this.open = false;
        } else if (params === 'weighingStation') {
          this.openAddWeighingStation = false;
        } else {
          this.projectConfig = false;
        }
        this.reset(params);
      },
      /** 表单重置 */
      reset(params) {
        if (params === 'project') {
          this.project = {
            deptId: '',
            deptCode: '',
            name: '',
            sectionName: '',
            areaName: '',
            areaCode: '',
            type: '',
            parentId: '',
            totalYield: '',
            licenseNo: '',
            leaderName: '',
            investment: '',
            contact: '',
            uploadFileList: [],
            fileList: [],
            disposalMethodJson: [{
                method: 'paiMai',
                amount: '',
                selected: false
              },
              {
                method: 'shiZheng',
                amount: '',
                selected: false
              },
              {
                method: 'qiTa',
                amount: '',
                selected: false,
                remark: ''
              }
            ],
            nationalDebt: '',
            nationalDebtType: '',
            historyYield: ''
          };
          this.listCoord = [{
            ref: "coord_0",
            id: 0,
            visible: true
          }, ];
        } else if (params === 'weighingStation') {
          this.weighingStation = {
            id: '',
            projectId: '',
            name: '',
            longitude: '',
            latitude: '',
            address: '',
            searchValue: '',
          };
        } else {
          this.projectConfigForm = {
            deptId: '',
            leaderAuthDuration: null,
            supervisorAuthDuration: null,
            authorityAuthDuration: null,
            useClient: false
          }
        }
        this.resetForm(params);
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.reload(true, 'project');
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /** 项目列表-多选框选中数据 */
      handleSelectionChange() {
        this.deptIds = this.$refs.projectTable.getSelectRowKeys();
        this.single = this.deptIds.length !== 1;
        this.multiple = !this.deptIds.length;
      },

      /** 新增按钮操作 */
      handleAdd(params) {
        this.readOnly = false;
        this.reset(params);
        if (params === 'project') {
          this.open = true;
          this.mode = 'add';
          this.title = "添加项目";
          this.imgList = [];
          this.parentImgList = [];
        } else {
          this.openAddWeighingStation = true;
          this.weighingStation.projectId = this.deptId;
          this.weighingStationTitle = '添加磅站';
        }
      },

      /** 修改按钮操作 */
      handleUpdate(row, params) {
        console.log("this.deptIds[0] ++++++++++++++++: " + this.deptIds[0])
        this.readOnly = false;
        this.reset(params);
        if (params === 'project') {
          const deptId = row.deptId || this.deptIds[0];
          getProject(deptId).then(r => {
            if (!r.project.disposalMethodJson) {
              r.project.disposalMethodJson = [{
                  method: 'paiMai',
                  amount: '',
                  selected: false
                },
                {
                  method: 'shiZheng',
                  amount: '',
                  selected: false
                },
                {
                  method: 'qiTa',
                  amount: '',
                  selected: false,
                  remark: ''
                }
              ]
            }
            this.project = r.project;
            this.open = true;
            this.mode = 'update';
            this.title = "修改项目";
            this.fromCoords(this.project.coords);
            this.handelFileList(r)
          });
        } else {
          const id = row.id || this.ids[0];
          getWeighingStation(id).then(r => {
            this.weighingStation = r.weighingStation;
            this.openAddWeighingStation = true;
            this.weighingStationTitle = '修改磅站';
          });
        }
      },
      /** 查看按钮操作 */
      handleViewProject(row) {
        this.readOnly = true;
        this.listCoord = [{
          ref: "coord_0",
          id: 0,
          visible: true
        }];
        const deptId = row.deptId || this.deptIds[0];
        this.title = "查看项目详情";
        getProject(deptId).then(r => {
          if (!r.project.disposalMethodJson) {
            r.project.disposalMethodJson = [{
                method: 'paiMai',
                amount: '',
                selected: false
              },
              {
                method: 'shiZheng',
                amount: '',
                selected: false
              },
              {
                method: 'qiTa',
                amount: '',
                selected: false,
                remark: ''
              }
            ]
          }
          this.project = r.project;
          this.project.reportedFlag = r.project.reportedFlag == '1' ? true : false;
          this.open = true;
          this.mode = 'view';
          this.handelFileList(r)
        });
      },

      /** 提交按钮 */
      submitForm(params) {
        if (params === 'project') {
          this.$refs["project"].validate((valid, errorObj) => {
            if (valid) {
              //采区范围坐标合法判断
              let coordsCheckMsg = this.checkCoords(-1);
              if (coordsCheckMsg != "") {
                this.$modal.msgError(coordsCheckMsg);
                return;
              }
              let coordsCurrent = this.toCoords(-1);
              console.log("submitForm++++++++++++++:coordsCurrent:" + coordsCurrent.coordsGeojson)
              this.project.coords = coordsCurrent.coordsString;
              this.project.geoJson = coordsCurrent.coordsGeojson;
              if (this.project.type == 'abandonSand') {
                this.project.investment = this.investmentTotal
              }
              if (this.project.deptId) {
                updateProject(this.project).then(res => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.reload(false, 'project');
                });
              } else {
                addProject(this.project).then(res => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.reload(false, 'project');
                });
              }
            } else {
              this.$scrollView(errorObj);
            }
          });
        } else if (params === 'weighingStation') {
          this.$refs["weighingStation"].validate((valid, errorObj) => {
            if (valid) {
              if (this.weighingStation.id) {
                updateWeighingStation(this.weighingStation).then(res => {
                  this.$modal.msgSuccess("修改成功");
                  this.openAddWeighingStation = false;
                  this.reload(false, 'weighingStation');
                });
              } else {
                addWeighingStation(this.weighingStation).then(res => {
                  this.$modal.msgSuccess("新增成功");
                  this.openAddWeighingStation = false;
                  this.reload(false, 'weighingStation');
                });
              }
            } else {
              this.$scrollView(errorObj);
            }
          });
        } else {
          if (this.configMode === 'update') {
            if (!this.projectConfigForm.useOpenApi) {
              this.projectConfigForm.partnerId = ''
            }
            updateProjectConfig(this.projectConfigForm).then(res => {
              this.$modal.msgSuccess("修改成功");
              this.projectConfig = false;
            });
          } else {
            if (!this.projectConfigForm.useOpenApi) {
              this.projectConfigForm.partnerId = ''
            }
            addProjectConfig(this.projectConfigForm).then(res => {
              this.$modal.msgSuccess("新增成功");
              this.projectConfig = false;
            });
          }
        }
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        this.$modal.confirm('是否确认删除选中的数据吗？').then(() => {
          if (row.deptId) {
            return delProject(row.deptId);
          } else {
            return delProjectBatch(this.deptIds);
          }
        }).then(() => {
          this.reload(false, 'project');
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      handleSortChange(opt, obj) {
        this.queryParams.sidx = obj.sidx
        this.queryParams.order = obj.order
      },

      /** 导出按钮操作 */
      handleExport() {
        this.download('/project/project/export', {
            ...this.queryParams
          },
          `项目管理_${new Date().getTime()}.xlsx`, 'application/json');
      },

      /** 项目位置change事件 获取areaName */
      getAreaName(areaName) {
        this.project.areaName = areaName;
        if (this.mode === 'add') {
          if (this.project.areaCode.length >= 15) {
            const deptObj = this.$refs.deptCascader.deptList.find(item => {
              return item.areaCode === this.project.areaCode;
            });
            this.project.parentId = deptObj ? deptObj.deptId : '';
          } else {
            this.project.parentId = '';
          }
        }
      },

      /** 获取fileList */
      getFileList(fileList) {
        this.project.uploadFileList = fileList;
        this.$refs['project'].validateField("uploadFileList")
      },
      getFileList1(fileList) {
        this.project.fileList = fileList
        this.$refs['project'].validateField("imgList")
      },
      getFileList2(fileList) {
        this.project.parentImgList = fileList
        this.$refs['project'].validateField("imgList2")
      },
      getFileList8(fileList) {
        this.project.paiMaiFileList = fileList
        this.$refs['project'].validateField("paiMaiFileList")
      },
      /** 查看附件列表 */
      handleFileList(row) {
        this.deptId = row.deptId;
        this.openFileList = true;
      },

      /** 预览附件操作 */
      handleView(row) {
        if (row.uploadContentType.indexOf('image') >= 0) {
          this.imageVisible = true;
          this.imageUrl = row.uploadFilePath;
        } else if (row.uploadContentType.indexOf("pdf") >= 0) {
          this.openViewPdf = true;
          this.pdfUrl = row.uploadFilePath;
        }
      },

      /** 下载附件操作 */
      handleDownloadFile(row) {
        this.download("/com/uploadfile/download/" + row.id, {}, row.uploadFileName);
      },

      /** 删除附件操作 */
      handleDeleteFile(row) {
        this.$modal.confirm('是否确认删除此附件？').then(() => {
          return delProjectFile(row.id);
        }).then(() => {
          this.$refs.fileTable.search();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 判断文件格式 是否可以预览 */
      isPreview(row) {
        return row.uploadContentType.indexOf('image') >= 0 || row.uploadContentType.indexOf("pdf") >= 0;
      },

      /** 磅站管理操作 */
      handleWeighingStation(row) {
        this.deptId = row.deptId;
        this.openWeighingStation = true;
      },

      /** 正则验证经度输入 */
      validateLng(rule, value, callback) {
        var that = this;
        return new Promise((resolve, reject) => {
          if (that.weighingStation.longitude) {
            if ((Number(that.weighingStation.longitude) >= 112) && (Number(that.weighingStation.longitude) <=
                121)) {
              resolve();
            } else {
              reject();
            }
          } else {
            resolve();
          }
        });
      },

      /** 正则验证纬度输入 */
      validateLat(rule, value, callback) {
        var that = this;
        return new Promise((resolve, reject) => {
          if (that.weighingStation.latitude) {
            if ((Number(that.weighingStation.latitude) >= 35) && (Number(that.weighingStation.latitude) <= 44)) {
              resolve();
            } else {
              reject();
            }
          } else {
            resolve();
          }
        });
      },

      /** 获取选中点地址信息 */
      getSelectPosition(data) {
        this.weighingStation.longitude = data.longitude;
        this.weighingStation.latitude = data.latitude;
        this.weighingStation.address = data.address;
      },

      /** 磅站列表-多选框选中数据 */
      handleSelectionChange1() {
        this.ids = this.$refs.weighingStationTable.getSelectRowKeys();
        this.single1 = this.ids.length !== 1;
        this.multiple1 = !this.ids.length;
      },

      /** 删除磅站操作 */
      handleDelWeighingStation(row) {
        this.$modal.confirm('是否确认删除选中的数据吗？').then(() => {
          return delWeighingStation(row.id);
        }).then(() => {
          this.reload(false, 'weighingStation');
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },

      /** 项目配置按钮操作 */
      handleProjectConfig(row) {
        this.projectConfig = true;
        getProjectConfig(row.deptId).then(r => {
          if (r.projectConfig) {
            this.projectConfigForm = r.projectConfig;
            this.configMode = 'update';
          } else {
            this.projectConfigForm.deptId = row.deptId;
            this.configMode = 'add';
          }
        });
        getPartnerAllList().then(res => {
          this.partnerList = res.list
        })
      },

      /** 项目二维码按钮操作 */
      handleProjectCode(row) {
        getProjectEncryptedString(row.deptId).then(r => {
          if (r.value) {
            this.openQrCode = true;
            this.qrCodeProjectName = row.name;
            this.$nextTick(() => {
              new QRCode(this.$refs.qrCodeDiv, {
                text: r.value,
                width: 280,
                height: 280,
                colorDark: '#333333', //二维码颜色
                colorLight: '#ffffff', //二维码背景色
                correctLevel: QRCode.CorrectLevel.L //容错率 L/M/H
              });
            });
          }
        });
      },
      handleProjectWxCode(row) {
        getProjectWxCode(row.deptId).then(r => {
          if (row.sectionName) {
            this.qrCodeProjectName = row.name + '(' + row.sectionName + ')'
          } else {
            this.qrCodeProjectName = row.name;
          }
          this.openWxQrCode = true;
          this.wxQrCode = r.uploadFile
        })
      },
      downloadWxCode(row) {
        if (row.sectionName) {
          this.qrCodeProjectName = row.name + '(' + row.sectionName + ')'
        } else {
          this.qrCodeProjectName = row.name;
        }
        this.download("/project/project/downloadWxCode/" + row.deptId, null, `二维码贴纸_${this.qrCodeProjectName}.jpg`);
      },
      handelFileList(r) {
        console.log(r.project.fileList)
        if (r.project.fileList && r.project.fileList.length > 0) {
          this.imgList = r.project.fileList.map(item => {
            return {
              name: item.uploadFileName,
              url: item.uploadFilePath,
              uploadFile: item,
            }
          });
        } else {
          this.imgList = [];
        }
        if (r.project.parentImgList && r.project.parentImgList.length > 0) {
          this.parentImgList = r.project.parentImgList.map(item => {
            return {
              name: item.uploadFileName,
              url: item.uploadFilePath,
              uploadFile: item,
            }
          });
        } else {
          this.parentImgList = [];
        }
      },

      //添加可采区组件方法
      onClickAddFormItem() {
        console.log("新增前this.trueID:------- " + this.trueID)
        this.trueID++
        this.showID++
        // 添加新的 my-form-item 组件
        this.formItems.push({
          formItemsId: `formItem_${this.trueID}`, // 生成唯一 ID
          labelNum: this.showID,
          label: `可采区K${this.showID}`, // 可以自定义标签
          ref: `formItem_${this.trueID}`,
          listCoord: [{
            ref: `formItem_${this.trueID}_coord_0`,
            id: `formItem_${this.trueID}_0`,
            visible: true
          }], // 初始化坐标列表
          visible: true
        });
        //nextId接续
        console.log("新增后this.trueID:------- " + this.trueID)
      },

      // 添加删除可采区组件方法
      onClickDeleteFormItem(index) {
        let num = this.formItems[index].labelNum;
        // 删除指定的formItem
        this.formItems[index].visible = false;
        // 更新所有的formItem的label标签值
        for (let i = index + 1; i < this.formItems.length; i++) {
          if (this.formItems[i].visible) {
            this.formItems[i].labelNum = num;
            this.formItems[i].label = "可采区K" + num;
            num++;
          }
        }
        this.showID--
        console.log("Remaining form items: ", this.formItems);
      },

      //添加采区坐标范围方法
      onClickMinusCoordInput(index, coordIndex) {
        this.formItems[index].listCoord[coordIndex].visible = false;
        // this.listCoord[k].visible = false;
      },
      onClickPlusCoordInput(formItemIndex) {
        let n = this.formItems[formItemIndex].listCoord.length;
        // let n = this.listCoord.length;
        this.formItems[formItemIndex].listCoord.push({
          ref: "formItem_" + formItemIndex + "_coord_" + n,
          id: "formItem_" + formItemIndex + "_" + n,
          visible: true,
        });
        this.$nextTick(() => {
          // let el0 = this.$refs[this.listCoord[0].ref][0];
          // let el = this.$refs[this.listCoord[n].ref][0];
          let el0 = this.$refs[this.formItems[formItemIndex].listCoord[0].ref][0];
          let el = this.$refs[this.formItems[formItemIndex].listCoord[n].ref][0];
          el.mode = el0.mode;
          el.mc.centralMeridian = el0.mc.centralMeridian;
        });
      },
      onSelectCoordInput(m, cm) {
        for (let i = 0; i < this.listCoord.length; i++) {
          let el = this.$refs[this.listCoord[i].ref][0];
          el.mode = m;
          el.mc.centralMeridian = cm;
        }
      },
      onClickPlot(formItemIndex) {
        this.choiceFromItemIndex = formItemIndex;
        let caller = {};
        let coordsCheckMsg = this.checkCoords(formItemIndex);
        console.log("onClickPlot-coordsCheckMsg:" + coordsCheckMsg)
        if (coordsCheckMsg != "") {
          caller.msg = coordsCheckMsg + "；从头开始绘制";
          caller.geojson = null;
          caller.typeAllowed = 30;
          this.onClickMap(caller);
          return;
        }
        let coordsCurrent = this.toCoords(formItemIndex);
        console.log("onClickPlot-coordsCurrent:" + coordsCurrent)
        caller.typeAllowed = 30;
        caller.geojson = coordsCurrent.coordsGeojson;
        caller.msg = "检查到已有坐标信息，可修改或重新绘制";
        this.onClickMap(caller);
      },
      checkCoords(formItemIndex) {
        if (formItemIndex != -1) {
          //单独验证一个formItem(打开地图绘制时)
          let n = this.formItems[formItemIndex].listCoord.length;
          let vv = [];
          for (let i = 0; i < n; i++) {
            let visible = this.formItems[formItemIndex].listCoord[i].visible;
            if (visible) {
              let el = this.$refs[this.formItems[formItemIndex].listCoord[i].ref][0];
              let v = el.getValue();
              if (v.msg != "OK") {
                return ("第" + (vv.length + 1) + "个拐点坐标：" + v.msg)
              } else {
                vv.push(v);
              }
            }
          }
          let k = vv.length;
          if (k < 1) {
            return (this.formItems[formItemIndex].label + "尚未填写坐标");
          }
        } else {
          //多个formItem情况下每个listCoord都需要验证(提交结果时)
          this.formItems.forEach(item => {
            console.log("+++++++进入到坐标验证循环++++++++++")
            if (item.visible) {
              console.log("验证列表：" + item.label)
              let n = item.listCoord.length;
              let vv = [];
              for (let i = 0; i < n; i++) {
                let visible = item.listCoord[i].visible;
                if (visible) {
                  console.log("checkCoords,每一个coords组件引用：" + this.$refs[item.listCoord[i].ref])
                  let el = this.$refs[item.listCoord[i].ref][0];
                  let v = el.getValue();
                  if (v.msg != "OK") {
                    return ("第" + (vv.length + 1) + "个拐点坐标：" + v.msg);
                  } else {
                    vv.push(v);
                  }
                }
              }
              let k = vv.length;
              if (k < 1) {
                return (item.label + "尚未填写坐标");
              }
            }
          })
        }
        return "";
      },
      toCoords(formItemIndex) {
        console.log("toCoords-formItemIndex:" + formItemIndex)
        if (formItemIndex != -1) {
          // 单独一个formItem中的coord坐标转换(地图绘制)
          let temp = this.formItems[formItemIndex].listCoord
          console.log("toCoords-temp:" + temp)
          let n = temp.length;
          let vv = [];
          for (let i = 0; i < n; i++) {
            let visible = temp[i].visible;
            if (visible) {
              let el = this.$refs[temp[i].ref][0];
              let v = el.getValue();
              if (v.msg != "OK") {
                return null;
              } else {
                vv.push(v);
              }
            }
          }
          let k = vv.length;
          if (k < 1) {
            return null;
          }
          return this.generateCoordsAndGeojson(k, vv);
        } else {
          // 多个formItem中的listCoord转换(提交)
          let multiLatLngsArray = []; //多区域坐标组
          let result = {}; //存储全部区域的coordsString和coordsGeojson
          this.formItems.forEach(item => {
            if (item.visible) {
              let n = item.listCoord.length;
              let vv = [];
              //循环读取当前formItem中的coord
              for (let i = 0; i < n; i++) {
                let visible = item.listCoord[i].visible;
                if (visible) {
                  let el = this.$refs[item.listCoord[i].ref][0];
                  let v = el.getValue();
                  if (v.msg != "OK") {
                    return null;
                  } else {
                    vv.push(v);
                  }
                }
              }
              let k = vv.length;
              if (k < 1) {
                return null;
              }
              let latLngsArray = []; //单区域坐标组
              for (let i = 0; i < k; i++) {
                latLngsArray.push(L.latLng(vv[i].lat, vv[i].lng));
              }
              latLngsArray.push(L.latLng(vv[0].lat, vv[0].lng));
              multiLatLngsArray.push(latLngsArray)
            }
          })
          result.coordsString = '';
          result.coordsGeojson = this.convertToGeoJSON(multiLatLngsArray);
          return result;
        }
      },

      // 使用 Leaflet 的 latLngsToCoords 方法转换为 GeoJSON 格式
      convertToGeoJSON(multiLatLngsArray) {
        const multiPolygon = {
          type: "MultiPolygon",
          // coordinates: multiLatLngsArray.map(latLngs => {
          //   // 使用 latLngsToCoords 方法进行转换
          //   return L.latLngsToCoords(latLngs, 0, true); // levelsDeep=0, closed=true
          // })
          coordinates: multiLatLngsArray.map(latLngs => [
            latLngs.map(latLng => [latLng.lng, latLng.lat]) // 转换为 [lng, lat] 格式
          ])
        };
        return JSON.stringify(multiPolygon);;
      },

      //根据坐标构件coordsString和coordsGeojson
      generateCoordsAndGeojson(k, vv) {
        let result = {};
        // 构建coord字段的值，以用户填写的内容为准
        let s = "";
        for (let i = 0; i < k; i++) {
          s += vv[i].xx + "," + vv[i].yy + ";";
        }
        result.coordsString = s;

        // 根据点数构建geojson
        let geo = {};
        if (k == 1) { // 一个点
          geo.type = "Point";
          geo.coordinates = [vv[0].lng, vv[0].lat];
        }
        if (k == 2) { // 一条线
          geo.type = "LineString";
          geo.coordinates = [
            [vv[0].lng, vv[0].lat],
            [vv[1].lng, vv[1].lat]
          ];
        }
        if (k > 2) {
          geo.type = "Polygon";
          let lls = [];
          for (let i = 0; i < k; i++) {
            let v = vv[i];
            lls.push([v.lng, v.lat]);
          }
          lls.push([vv[0].lng, vv[0].lat])
          geo.coordinates = [lls];
        }
        result.coordsGeojson = JSON.stringify(geo);
        return result;
      },

      onClickMap(caller) {
        this.$refs.geoPlotter.msgFromCaller = caller.msg;
        this.$refs.geoPlotter.geojson = caller.geojson;
        this.$refs.geoPlotter.typeAllowed = caller.typeAllowed;
        this.$refs.geoPlotter.visible = true;
      },
      // onPlotted(s){
      //   this.mapDialogs["road"].onPlotted(s);
      //   this.mapDialogs["pipe"].onPlotted(s);
      //   this.mapDialogs["wire"].onPlotted(s);
      //   this.mapDialogs["park"].onPlotted(s);
      // },
      onPlotted(coordsString) {
        // if (!this.visible) {
        //   return;
        // }
        console.log("onPlotted-coordsString:" + coordsString)
        this.fromCoords(coordsString);
      },
      fromCoords(coordsString) {
        if (!coordsString) {
          return;
        }
        let ss = coordsString.split(";");
        if (ss.length == 0) {
          return;
        }
        console.log("fromCoords-ss.length:" + ss.length)
        //当前选中的formItem中的listCoord
        this.formItems[this.choiceFromItemIndex].listCoord = [];
        for (let i = 0; i < ss.length; i++) {
          if (!ss[i]) {
            continue;
          }
          this.formItems[this.choiceFromItemIndex].listCoord.push({
            ref: "formItem_" + this.choiceFromItemIndex + "_coord_" + i, // 例如 formItem_0_coord_0
            id: "formItem_" + this.choiceFromItemIndex + "_" + i, // 例如 formItem_0_0
            visible: true,
            // ref: "coord_" + i, id: i, visible: true,
          });
          let ll = ss[i].split(",");
          if (ll.length < 2) {
            continue;
          }
          this.$nextTick(() => {
            let el = this.$refs[this.formItems[this.choiceFromItemIndex].listCoord[i].ref][0];
            el.setValue(ll[0], ll[1]);
          });
        }
      },
      fromGeojson(geojson) {
        if (!geojson) {
          return;
        }
        this.listCoord = [];
        let geo = JSON.parse(geojson);
        if (geo.type == "Point") {
          if (this.readOnly) {
            this.formData.aLng = geo.coordinates[0];
            this.formData.aLat = geo.coordinates[1];
            this.formData.bLng = geo.coordinates[0];
            this.formData.bLat = geo.coordinates[1];
          } else {
            this.listCoord.push({
              ref: "coord_0",
              id: 0,
              visible: true,
            });
            this.$nextTick(() => {
              let el = this.$refs["coord_0"][0];
              el.setValue(geo.coordinates[0], geo.coordinates[1]);
            });
          }
        }
        if (geo.type == "LineString") {

          if (this.readOnly) {
            this.formData.aLng = geo.coordinates[0][0];
            this.formData.aLat = geo.coordinates[0][1];
            this.formData.bLng = geo.coordinates[geo.coordinates.length - 1][0];
            this.formData.bLat = geo.coordinates[geo.coordinates.length - 1][1];
          } else {
            for (let i = 0; i < geo.coordinates.length; i++) {
              this.listCoord.push({
                ref: "coord_" + i,
                id: i,
                visible: true,
              });
              let ll = geo.coordinates[i];
              this.$nextTick(() => {
                let el = this.$refs[this.listCoord[i].ref][0];
                el.setValue(ll[0], ll[1]);
              });
            }
          }

        }
        if (geo.type == "MultiLineString") {
          if (this.readOnly) {
            this.formData.aLng = geo.coordinates[0][0][0];
            this.formData.aLat = geo.coordinates[0][0][1];
            this.formData.bLng = geo.coordinates[geo.coordinates.length - 1][geo.coordinates[geo.coordinates.length - 1]
              .length - 1
            ][0];
            this.formData.bLat = geo.coordinates[geo.coordinates.length - 1][geo.coordinates[geo.coordinates.length - 1]
              .length - 1
            ][1];
          } else {
            let k = 0;
            for (let r = 0; r < geo.coordinates.length; r++) {
              for (let i = 0; i < geo.coordinates[r].length; i++) {
                this.listCoord.push({
                  ref: "coord_" + k,
                  id: k,
                  visible: true,
                });
                let ll = geo.coordinates[r][i];
                this.$nextTick(() => {
                  let el = this.$refs[this.listCoord[k].ref][0];
                  el.setValue(ll[0], ll[1]);
                });
                k = k + 1;
              }
            }
          }
        }
      },
      //以下为采区坐标上传和解析方法
      handleImport() {
        this.upload.open = true;
        this.upload.title = "采区坐标导入"
      },
      beforeUpload(file) {
        console.log("beforeUpload:" + file)
        return false;
      },
      //文件上传时触发
      handleFileChange(file) {
        // 存储选择的文件
        console.log("handleFileChange:" + file)
        this.selectedFile = file.raw; // 获取文件对象
      },
      //读取上传的excle文件
      resolveCoords() {
        if (!this.selectedFile) {
          this.$message.error('请先选择一个文件');
          return;
        }
        const reader = new FileReader();
        reader.readAsArrayBuffer(this.selectedFile);
        reader.onload = (e) => {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, {
            type: 'array'
          });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          // 将工作表转换为 JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1
          });
          console.log("jsonData:" + jsonData)
          // 假设经纬度在第一列和第二列
          this.coordinates = jsonData.slice(1).map(row => ({
            longitude: row[0], // 经度
            latitude: row[1], // 纬度
          }));
          this.handleFileSuccess();
        };
      },
      // 文件上传成功处理
      handleFileSuccess() {
        // 拼接成字符串
        const coordinatesString = this.coordinates
          .map(coord => `${coord.longitude},${coord.latitude}`) // 将每个坐标格式化为 "经度,纬度;经度,纬度"
          .join(';'); // 用分号连接每个坐标
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        console.log("this.coordinates:" + coordinatesString)
        this.fromCoords(coordinatesString)
        // this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      },
    },
  };
</script>

<style scoped lang="scss">
  ::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin:0 !important;
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    max-height:calc(100% - 30px);
    max-width:calc(100% - 30px);
  }
  ::v-deep .el-dialog .el-dialog__body {
    flex:1;
    overflow:auto;
  }

  .dialog-content {
    padding: 20px;
  }

  .dialog-footer {
    text-align: right;
  }

  .qrcode-title {
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 16px;
  }

  ::v-deep.el-form-item.is-required .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }

  ::v-deep .el-dialog__body {
    padding: 0 20px !important;
  }

  ::v-deep .asterisk .el-form-item__label:before {
    content: "*" !important;
    color: #ff4949 !important;
    margin-right: 4px !important;
  }
</style>
