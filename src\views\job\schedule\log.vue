<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      @submit.native.prevent
    >
      <el-form-item label="任务Id" prop="jobId">
        <el-input
          v-model="queryParams.jobId"
          placeholder="请输入任务Id"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button icon="el-icon-back" size="mini" @click="back">返回</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        class="rightToolbar"
        :search.sync="search"
        :showSearch.sync="showSearch"
        @queryTable="getList">
      </right-toolbar>
    </el-row>
    <!-- table表格 -->
    <my-table url="sys/scheduleLog/list" ref="logTable" row-key="logId">
      <el-table-column label="日志ID" prop="logId" align="center" :show-overflow-tooltip="true" min-width="150"/>
      <el-table-column label="任务ID" prop="jobId" align="center" :show-overflow-tooltip="true" min-width="150"/>
      <el-table-column label="bean名称" align="center" prop="beanName" min-width="120"/>
      <el-table-column label="方法名称" align="center" prop="methodName" min-width="120"/>
      <el-table-column label="参数" align="center" prop="params" min-width="150"/>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template scope="scope">
          <span v-if="scope.row.status===0" class="table-label label-success">成功</span>
          <span v-if="scope.row.status===1" class="table-label label-danger" @click="showError(scope.row)">失败</span>
        </template>
      </el-table-column>
      <el-table-column label="耗时(单位：毫秒)" align="center" prop="times" min-width="120"></el-table-column>
      <el-table-column label="执行时间" align="center" prop="createTime" min-width="150"></el-table-column>
    </my-table>
  </div>
</template>
<script>
import { getLogInfo } from '@/api/job/log'
export default {
  name: 'log',
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      search: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobId: undefined
      }
    }
  },
  methods: {
    /** 查询定时任务列表 */
    getList(restart) {
      this.$refs.logTable.search(this.queryParams, restart)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList(true)
    },
    /** 重置操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 返回按钮操作 */
    back() {
      this.$router.go(-1)
    },
    /** 点击失败操作 */
    showError(row) {
      const logId = row.logId
      getLogInfo(logId).then(response => {
        this.$modal.alert(response.log.error, '失败信息')
      })
    }
  }
}
</script>
