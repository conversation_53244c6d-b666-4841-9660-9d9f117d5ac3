import request from '@/utils/request'

// 登录方法
export function login(loginParams) {
  return request({
    url: '/login/login',
    headers: {
      isToken: false
    },
    method: 'post',
    params: loginParams,
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: 'sys/user/userInfo',
    method: 'post'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/login/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg(sessionId) {
  return request({
    url: '/login/captcha',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000,
    params: {sessionId: sessionId},
  })
}

//获取加密秘钥
export function getSecret() {
  return request({
    url: '/login/secret',
    headers: {
      isToken: false
    },
    method: 'post',
    timeout: 20000
  });
}

//获取登陆页配置信息
export function getConfig() {
  return request({
    url:'/login/config',
    headers: {
      isToken: false
    },
    method:'post',
  })
}

//获取ticket二维码
export function getTicketQrCode(sessionId) {
  return request({
    url:'sys/qrcode/url',
    headers: {
      isToken: false
    },
    params: {sessionId: sessionId},
    method:'post',
  })
}

//发起轮询请求
export function polling(sessionId) {
  return request({
    url:'sys/qrcode/poll',
    headers: {
      isToken: false
    },
    params: {sessionId: sessionId},
    method:'post',
  })
}

//获取sessionId
export function getSessionId() {
  return request({
    url:'/login/sessionId',
    headers: {
      isToken: false
    },
    method:'post',
  })
}

//注册并绑定
export function registerAndBind(user,captcha,uuid,sessionId) {
  return request({
    url:"/sys/qrcode/registerAndBind",
    headers: {
      isToken: false
    },
    method: 'post',
    data: user,
    params: {
      captcha, uuid, sessionId,
    }
  })
}

//登陆并绑定
export function doBind(username, password, captcha, uuid, sessionId) {
  return request({
    url: "/sys/qrcode/doBind",
    method: 'post',
    headers: {
      isToken: false
    },
    params: {
      username: username,
      password: password,
      captcha: captcha,
      uuid: uuid,
      sessionId: sessionId,
    }
  });
}
//用户名验证重复
export function checkUserName(username) {
  return request({
    url:"/sys/qrcode/checkUserName",
    headers: {
      isToken: false
    },
    params:{
      username: username,
    },
    method: 'post',
  })
}
//验证邮箱是否可用
export function checkEmail(email) {
  return request({
    url:"/sys/qrcode/checkEmail",
    headers: {
      isToken: false
    },
    params:{
      email,
    },
    method: 'post',
  })
}
//验证手机号是否可用
export function checkMobile(mobile) {
  return request({
    url:"/sys/qrcode/checkMobile",
    headers: {
      isToken: false
    },
    params:{
      mobile,
    },
    method: 'post',
  })
}
