<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="河道名称" prop="reverName">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.reverName"
          placeholder="请输入河道名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属地区" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="数据状态" prop="status">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择数据状态"
          pvalue="dataStatus"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reports:responsiblePersion:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reports:responsiblePersion:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/reports/responsiblePersion/page" :fixed="true" ref="responsiblePersionTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column  label="河道名称" min-width="110" fixed="left" prop="reverName" sortable="custom" header-align="center" align="center" column-key="REVER_NAME"></el-table-column>
      <el-table-column  label="所在市" min-width="120" prop="cityName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="所在县/区" min-width="120" prop="districtName" sortable="custom" header-align="center" align="center" column-key="AREA_CODE"></el-table-column>
      <el-table-column  label="起始位置" min-width="160" prop="startAddr" sortable="custom" header-align="center" align="center" column-key="START_ADDR"></el-table-column>
      <el-table-column  label="终止位置" min-width="160" prop="endAddr" sortable="custom" header-align="center" align="center" column-key="END_ADDR"></el-table-column>
      <el-table-column  label="县级责任人" min-width="130" prop="responsibleXian" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIAN"></el-table-column>
      <el-table-column  label="县名称及职务" min-width="170sss" prop="positionXian" sortable="custom" header-align="center" align="center" column-key="POSITION_XIAN"></el-table-column>
      <el-table-column  label="县级责任河段起始位置" min-width="180" prop="startAddrXian" sortable="custom" header-align="center" align="center" column-key="START_ADDR_XIAN"></el-table-column>
      <el-table-column  label="县级责任河段终止位置" min-width="180" prop="endAddrXian" sortable="custom" header-align="center" align="center" column-key="END_ADDR_XIAN"></el-table-column>
      <el-table-column  label="乡级责任人" min-width="130" prop="responsibleXiang" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_XIANG"></el-table-column>
      <el-table-column  label="乡镇名称及职务" min-width="170" prop="positionXiang" sortable="custom" header-align="center" align="center" column-key="POSITION_XIANG"></el-table-column>
      <el-table-column  label="乡级责任河段起始位置" min-width="180" prop="startAddrXiang" sortable="custom" header-align="center" align="center" column-key="START_ADDR_XIANG"></el-table-column>
      <el-table-column  label="乡级责任河段终止位置" min-width="180" prop="endAddrXiang" sortable="custom" header-align="center" align="center" column-key="END_ADDR_XIANG"></el-table-column>
      <el-table-column  label="村级责任人" min-width="110" prop="responsibleCun" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_CUN"></el-table-column>
      <el-table-column  label="村名称及职务" min-width="170" prop="positionCun" sortable="custom" header-align="center" align="center" column-key="POSITION_CUN"></el-table-column>
      <el-table-column  label="村级责任河段起始位置" min-width="180" prop="startAddrCun" sortable="custom" header-align="center" align="center" column-key="START_ADDR_CUN"></el-table-column>
      <el-table-column  label="村级责任河段终止位置" min-width="180" prop="endAddrCun" sortable="custom" header-align="center" align="center" column-key="END_ADDR_CUN"></el-table-column>
      <el-table-column  label="县级水行政主管部门责任人" min-width="210" prop="responsibleCompetentDept" header-align="center" align="center" sortable="custom" column-key="RESPONSIBLE_COMPETENT_DEPT"></el-table-column>
      <el-table-column  label="县级水行政主管部门单位及职务" min-width="230" prop="positionCompetentDept" header-align="center" align="center" sortable="custom" column-key="POSITION_COMPETENT_DEPT"></el-table-column>
      <el-table-column  label="县级水行政主管部门责任河段起始位置" min-width="270" prop="startAddrCompetentDept" header-align="center" align="center" sortable="custom" column-key="START_ADDR_COMPETENT_DEPT"></el-table-column>
      <el-table-column  label="县级水行政主管部门责任河段终止位置" min-width="270" prop="endAddrCompetentDept" header-align="center" align="center" sortable="custom" column-key="END_ADDR_COMPETENT_DEPT"></el-table-column>
      <el-table-column  label="现场监管责任人" min-width="150" prop="responsibleSupervise" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_SUPERVISE"></el-table-column>
      <el-table-column  label="现场监管单位及职务" min-width="190" prop="positionSupervise" sortable="custom" header-align="center" align="center" column-key="POSITION_SUPERVISE"></el-table-column>
      <el-table-column  label="现场监管责任河段起始位置" min-width="210" prop="startAddrSupervise" sortable="custom" header-align="center" align="center" column-key="START_ADDR_SUPERVISE"></el-table-column>
      <el-table-column  label="现场监管责任河段终止位置" min-width="210" prop="endAddrSupervise" sortable="custom" header-align="center" align="center" column-key="END_ADDR_SUPERVISE"></el-table-column>
      <el-table-column  label="行政执法责任人" min-width="150" prop="responsibleEnforce" sortable="custom" header-align="center" align="center" column-key="RESPONSIBLE_ENFORCE"></el-table-column>
      <el-table-column  label="行政执法单位及职务" min-width="170" prop="positionEnforce" sortable="custom" header-align="center" align="center" column-key="POSITION_ENFORCE"></el-table-column>
      <el-table-column  label="行政执法责任河段起始位置" min-width="210" prop="startAddrEnforce" sortable="custom" header-align="center" align="center" column-key="START_ADDR_ENFORCE"></el-table-column>
      <el-table-column  label="行政执法责任河段终止位置" min-width="210" prop="endAddrEnforce" sortable="custom" header-align="center" align="center" column-key="END_ADDR_ENFORCE"></el-table-column>
      <el-table-column  label="数据状态" fixed="right" min-width="110" prop="status" sortable="custom" header-align="center" align="center" column-key="STATUS">
        <template #default="scope">
          <my-view pvalue="dataStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" min-width="160" header-align="center" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            title="修改"
            @click="handleUpdate(scope.row)"
            v-if="scope.row.status == 'daiXiuGai' || scope.row.status == 'daiXianShenHe'|| scope.row.status == 'yiTongGuo'"
            v-hasPermi="['reports:responsiblePersion:update']"
          ></el-button>
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-document-copy"
            title="查看详情"
            @click="handleView(scope.row)"
          ></el-button>
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            title="删除"
            @click="handleDelete(scope.row)"
            v-if="scope.row.status == 'daiXiuGai'|| scope.row.status == 'daiXianShenHe'"
            v-hasPermi="['reports:responsiblePersion:delete']"
          ></el-button>
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            title="删除"
            @click="handleDelete(scope.row)"
            v-hasPermi="['reports:responsiblePersion:shengDelete']"
          ></el-button>

        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改采砂监管责任人表对话框 -->
    <my-dialog :title="title" :visible.sync="open" append-to-body width="1000px"  height="70vh">
      <el-form ref="form" :model="responsiblePersion" label-width="200px">
        <el-divider content-position="center">
          <i class="el-icon-info"></i>基本信息
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="cityAreaCode" :rules="[{notNull:true,message:'请输入所在市'}]" label="所在市" >
              <my-select
                v-model="responsiblePersion.cityAreaCode"
                disabled
                :options="cityAreaList"
                itemValue ="areaCode"
                itemName="areaName">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12" >
            <my-form-item  ref="districtAreaCode"  label="所在县/区" prop="districtAreaCode">
              <my-select
                v-model="responsiblePersion.districtAreaCode"
                :disabled="isShow"
                :options="areaList"
                itemValue ="areaCode"
                itemName="areaName">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="reverName" :rules="[{notNull:true,message:'请输入河道名称'}]" label="河道名称" prop="reverName">
              <my-input v-model.trim="responsiblePersion.reverName" :maxlength="128" placeholder="请输入河道名称"/>
            </my-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <my-form-item ref="areaCode" :rules="[{notNull:true,message:'请输入所属地区'}]" label="所属地区" prop="areaCode">-->
<!--              <my-input v-model.trim="responsiblePersion.areaCode" :maxlength="128" placeholder="请输入所属地区"/>-->
<!--            </my-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <my-form-item ref="startAddr" :rules="[{notNull:true,message:'请输入起始位置'}]" label="起始位置" prop="startAddr">
              <my-input v-model.trim="responsiblePersion.startAddr" :maxlength="128" placeholder="请输入起始位置"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddr" :rules="[{notNull:true,message:'请输入终止位置'}]" label="终止位置" prop="endAddr">
              <my-input v-model.trim="responsiblePersion.endAddr" :maxlength="128" placeholder="请输入终止位置"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>县级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleXian" :rules="[{notNull:true,message:'请输入县级责任人'}]" label="县级责任人" prop="responsibleXian" >
              <my-input v-model.trim="responsiblePersion.responsibleXian" :maxlength="128" placeholder="请输入县级责任人"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="positionXian" :rules="[{notNull:true,message:'请输入县名称及职务'}]" label="县名称及职务" prop="positionXian" >
              <my-input v-model.trim="responsiblePersion.positionXian" :maxlength="128" placeholder="请输入县名称及职务"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="startAddrXian" :rules="[{notNull:true,message:'请输入县级责任河段起始位置'}]" label="河段起始位置" prop="startAddrXian">
              <my-input v-model.trim="responsiblePersion.startAddrXian" :maxlength="128" placeholder="请输入县级责任河段起始位置"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item ref="endAddrXian" :rules="[{notNull:true,message:'请输入县级责任河段终止位置'}]" label="河段终止位置"
                          prop="endAddrXian"
            >
              <my-input v-model.trim="responsiblePersion.endAddrXian" :maxlength="128"
                        placeholder="请输入县级责任河段终止位置"
              />
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>乡镇河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="responsibleXiang" :rules="[{notNull:true,message:'请输入乡级责任人'}]" label="乡级责任人"
                          prop="responsibleXiang"
            >
              <my-input v-model.trim="responsiblePersion.responsibleXiang" :maxlength="128" placeholder="请输入乡级责任人"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="positionXiang" :rules="[{notNull:true,message:'请输入乡镇名称及职务'}]" label="乡镇名称及职务"
                        prop="positionXiang"
          >
            <my-input v-model.trim="responsiblePersion.positionXiang" :maxlength="128"
                      placeholder="请输入乡镇名称及职务"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="startAddrXiang" :rules="[{notNull:true,message:'请输入乡级责任河段起始位置'}]" label="河段起始位置"
                        prop="startAddrXiang"
          >
            <my-input v-model.trim="responsiblePersion.startAddrXiang" :maxlength="128"
                      placeholder="请输入乡级责任河段起始位置"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="endAddrXiang" :rules="[{notNull:true,message:'请输入乡级责任河段终止位置'}]" label="河段终止位置"
                        prop="endAddrXiang"
          >
            <my-input v-model.trim="responsiblePersion.endAddrXiang" :maxlength="128"
                      placeholder="请输入乡级责任河段终止位置"
            />
          </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>村级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
          <my-form-item ref="responsibleCun" :rules="[{notNull:true,message:'请输入村级责任人'}]" label="村级责任人"
                        prop="responsibleCun"
          >
            <my-input v-model.trim="responsiblePersion.responsibleCun" :maxlength="128" placeholder="请输入村级责任人"/>
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="positionCun" :rules="[{notNull:true,message:'请输入村名称及职务'}]" label="村名称及职务"
                        prop="positionCun"
          >
            <my-input v-model.trim="responsiblePersion.positionCun" :maxlength="128" placeholder="请输入村名称及职务"/>
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="startAddrCun" :rules="[{notNull:true,message:'请输入河段起始位置'}]" label="河段起始位置"
                        prop="startAddrCun"
          >
            <my-input v-model.trim="responsiblePersion.startAddrCun" :maxlength="128"
                      placeholder="请输入村级责任河段起始位置"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="endAddrCun" :rules="[{notNull:true,message:'请输入河段终止位置'}]" label="河段终止位置"
                        prop="endAddrCun"
          >
            <my-input v-model.trim="responsiblePersion.endAddrCun" :maxlength="128"
                      placeholder="请输入村级责任河段终止位置"
            />
          </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>县级水行政主管部门责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
          <my-form-item ref="responsibleCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任人'}]" label="水行政主管部门责任人"
                        prop="responsibleCompetentDept"
          >
            <my-input v-model.trim="responsiblePersion.responsibleCompetentDept"
                      :maxlength="128" placeholder="请输入县级水行政主管部门责任人"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="positionCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门单位及职务'}]" label="水行政主管部门单位及职务"
                        prop="positionCompetentDept"
          >
            <my-input v-model.trim="responsiblePersion.positionCompetentDept"
                      :maxlength="128" placeholder="请输入县级水行政主管部门单位及职务"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="startAddrCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任河段起始位置'}]"
                        label="河段起始位置"
                        prop="startAddrCompetentDept"
          >
            <my-input v-model.trim="responsiblePersion.startAddrCompetentDept"
                      :maxlength="128" placeholder="请输入县级水行政主管部门责任河段起始位置"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="endAddrCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任河段终止位置'}]" label="河段终止位置"
                        prop="endAddrCompetentDept"
          >
            <my-input v-model.trim="responsiblePersion.endAddrCompetentDept"
                      :maxlength="128" placeholder="请输入县级水行政主管部门责任河段终止位置"
            />
          </my-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">
          <i class="el-icon-info"></i>现场监管责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
          <my-form-item ref="responsibleSupervise" :rules="[{notNull:true,message:'请输入现场监管责任人'}]" label="现场监管责任人"
                        prop="responsibleSupervise"
          >
            <my-input v-model.trim="responsiblePersion.responsibleSupervise" :maxlength="128"
                      placeholder="请输入现场监管责任人"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="positionSupervise" :rules="[{notNull:true,message:'请输入现场监管单位及职务'}]" label="现场监管单位及职务"
                        prop="positionSupervise"
          >
            <my-input v-model.trim="responsiblePersion.positionSupervise" :maxlength="128"
                      placeholder="请输入现场监管单位及职务"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="startAddrSupervise" :rules="[{notNull:true,message:'请输入现场监管责任河段起始位置'}]" label="河段起始位置"
                        prop="startAddrSupervise"
          >
            <my-input v-model.trim="responsiblePersion.startAddrSupervise" :maxlength="128"
                      placeholder="请输入现场监管责任河段起始位置"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="endAddrSupervise" :rules="[{notNull:true,message:'请输入现场监管责任河段终止位置'}]" label="河段终止位置"
                        prop="endAddrSupervise"
          >
            <my-input v-model.trim="responsiblePersion.endAddrSupervise" :maxlength="128"
                      placeholder="请输入现场监管责任河段终止位置"
            />
          </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>行政执法责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
          <my-form-item ref="responsibleEnforce" :rules="[{notNull:true,message:'请输入行政执法责任人'}]" label="行政执法责任人"
                        prop="responsibleEnforce"
          >
            <my-input v-model.trim="responsiblePersion.responsibleEnforce" :maxlength="128"
                      placeholder="请输入行政执法责任人"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="positionEnforce" :rules="[{notNull:true,message:'请输入行政执法单位及职务'}]" label="行政执法单位及职务"
                        prop="positionEnforce"
          >
            <my-input v-model.trim="responsiblePersion.positionEnforce" :maxlength="128"
                      placeholder="请输入行政执法单位及职务"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="startAddrEnforce" :rules="[{notNull:true,message:'请输入行政执法责任河段起始位置'}]" label="河段起始位置"
                        prop="startAddrEnforce"
          >
            <my-input v-model.trim="responsiblePersion.startAddrEnforce" :maxlength="128"
                      placeholder="请输入行政执法责任河段起始位置"
            />
          </my-form-item>
          </el-col>
          <el-col :span="12">
          <my-form-item ref="endAddrEnforce" :rules="[{notNull:true,message:'请输入行政执法责任河段终止位置'}]" label="河段终止位置"
                        prop="endAddrEnforce"
          >
            <my-input v-model.trim="responsiblePersion.endAddrEnforce" :maxlength="128"
                      placeholder="请输入行政执法责任河段终止位置"
            />
          </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>备注
        </el-divider>
        <el-row>
            <el-col :span="24">
              <my-form-item ref="remark"  label="备注" prop="remark">
                <my-input type="textarea" v-model.trim="responsiblePersion.remark" placeholder="请输入备注" :maxlength="255"/>
              </my-form-item>
            </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>审核流程
        </el-divider>
        <el-row>
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item,index) in processList" :key="item.id">
              <td style="width: 30%">{{item.createTime}}</td>
              <td >{{item.createUser}}</td>
              <td >
                <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>
              </td>
              <td >{{item.opinion}}</td>
            </tr>
          </table>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>

    <my-responsible-report-details
      :detailsOpen="detailsOpen"
      :cityName="rowCityName"
      :districtName="rowDistrictName"
      :id="id"
      @close="detailsOpen=false"
    />
  </div>
</template>

<script>
import {
  getResponsiblePersion,
  delResponsiblePersion,
  delResponsiblePersionBatch,
  addResponsiblePersion,
  updateResponsiblePersion,
  getProcessList
} from '@/api/reports/responsiblePersion'
import { getUser } from '@/api/statistics/projectStatistics'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import MyResponsibleReportDetails from '@/components/YB/MyResponsibleReportDetails.vue'
import { getSubList } from '@/api/system/area'

export default {
  name: "ResponsiblePersion",
  components: { MyResponsibleReportDetails, MyAreaSelect },
  data() {
    return {
      detailsOpen: false,
      id:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        reverName:'',
        areaCode:'',
      },
      // 表单参数
      responsiblePersion: {},
      cityName:'',
      districtName:'',
      rowCityName:'',
      rowDistrictName:'',
      row:null,
      processList:'',
      areaList:[],
      isShow:false,
      cityAreaList:[]
    };
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      const r = await getUser()
      const res = await getSubList('000_013')
      this.cityAreaList = res.areaList
      if(r.cityAreaCode){
        this.responsiblePersion.cityAreaCode = r.cityAreaCode
      }
      if(r.districtAreaCode){
        this.isShow = true
        this.responsiblePersion.districtAreaCode = r.districtAreaCode
        const response = await getSubList(r.cityAreaCode)
        this.areaList = response.areaList
      }else {
        this.isShow = false
        const response = await getSubList(r.cityAreaCode)
        this.areaList = response.areaList
      }
    },
    /** 查询采砂监管责任人表列表 */
    reload(restart) {
      this.$refs.responsiblePersionTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.responsiblePersion = {
        id: '',
        cityName: '',
        districtName: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        reverName: '',
        areaCode: '',
        status: '',
        startAddr: '',
        endAddr: '',
        responsibleXian: '',
        positionXian: '',
        startAddrXian: '',
        endAddrXian: '',
        responsibleXiang: '',
        positionXiang: '',
        startAddrXiang: '',
        endAddrXiang: '',
        responsibleCun: '',
        positionCun: '',
        startAddrCun: '',
        endAddrCun: '',
        responsibleCompetentDept: '',
        positionCompetentDept: '',
        startAddrCompetentDept: '',
        endAddrCompetentDept: '',
        responsibleSupervise: '',
        positionSupervise: '',
        startAddrSupervise: '',
        endAddrSupervise: '',
        responsibleEnforce: '',
        positionEnforce: '',
        startAddrEnforce: '',
        endAddrEnforce: '',
        cityAreaCode:'',
        districtAreaCode:''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection[0])
      this.ids = this.$refs.responsiblePersionTable.getSelectRowKeys()
      if(selection[0]){
        if(this.ids.length ==1&&(selection[0].status == 'daiXiuGai'||selection[0].status == 'daiXianShenHe')){
          this.single = false
        }else {
          this.single = true
        }
      }else {
        this.single = true
      }
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getUserInfo()
      this.processList = []
      this.responsiblePersion.cityName = this.cityName
      this.responsiblePersion.districtName = this.districtName
      this.open = true;
      this.title = "添加采砂监管责任人";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.row = row
      this.getUserInfo()
      const id = row.id || this.ids[0];
      getResponsiblePersion(id).then(r => {
        this.responsiblePersion = r.responsiblePersion;
        this.responsiblePersion.cityName = row.cityName
        this.responsiblePersion.districtName = row.districtName
        if(r.responsiblePersion.areaCode.length>11){
          this.responsiblePersion.districtAreaCode = r.responsiblePersion.areaCode
        }else {
          this.responsiblePersion.cityAreaCode = r.responsiblePersion.areaCode
          this.responsiblePersion.districtAreaCode = ''
        }
        this.open = true;
        this.title = "修改采砂监管责任人";

      });
      getProcessList(id).then(res=>{
        this.processList = res.page.list
      })

    },
    setAreaCode(){
      if(this.responsiblePersion.districtAreaCode){
        this.responsiblePersion.areaCode = this.responsiblePersion.districtAreaCode
      }else {
        this.responsiblePersion.areaCode = this.responsiblePersion.cityAreaCode
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          this.setAreaCode()
          if (this.responsiblePersion.id) {
            this.responsiblePersion.version = this.row.version;
            updateResponsiblePersion(this.responsiblePersion).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addResponsiblePersion(this.responsiblePersion).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delResponsiblePersion(row.id);
        }else{
          return delResponsiblePersionBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExport(){
      this.download('/reports/responsiblePersion/export', {
        ...this.queryParams
      }, `河道监管负责人_${new Date().getTime()}.xlsx`, 'application/json');
    },
    handleView(row){
      this.id = row.id
      this.detailsOpen = true
      this.rowCityName = row.cityName
      this.rowDistrictName = row.districtName
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.responsiblePersionTable.changeTableHeight();
  },
};
</script>

<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
