<template>
  <BaiduMap
    class="bm-view"
    :ak="baiduMapAk"
    :center="center"
    :zoom="zoom"
    :scroll-wheel-zoom="true"
    @ready="handler"
    @click="mapClick">
    <BmMarker
      :position="markerPoint"
      :dragging="true">
    </BmMarker>
  </BaiduMap>
</template>
<script>
//VUE BAIDU MAP——API文档——https://dafrok.github.io/vue-baidu-map/#/zh/index
import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
import BmMarker from 'vue-baidu-map/components/overlays/Marker.vue'
export default {
  name: 'BMap',
  components: {
    BaiduMap,
    BmMarker
  },
  props: {
    baiduMapAk: {
      type: String,
      default: ''
    },
    defaultCenter: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      center: {},
      markerPoint: {},
      zoom: 11
    }
  },
  methods: {
    handler() {
      var that = this
      var defaultCenter
      if (!this.defaultCenter.lng || !this.defaultCenter.lat) {
        defaultCenter = {
          lng: 114.520957,
          lat: 38.049789
        }
      } else {
        defaultCenter = this.defaultCenter
      }
      that.center = {
        lng: Number(defaultCenter.lng),
        lat: Number(defaultCenter.lat)
      }
      that.markerPoint = {
        lng: Number(defaultCenter.lng),
        lat: Number(defaultCenter.lat)
      }
    },
    mapClick(event) {
      this.markerPoint = {
        lng: event.point.lng,
        lat: event.point.lat
      }
    }
  }
}
</script>
<style scoped>
.bm-view {
  height: 400px;
  width: 100%;
}
</style>
