<template>
  <div class="wrapper-page">
    <div>
      <el-tooltip content="选择合适的坐标表达方式" placement="top"><i class="el-icon-info"></i></el-tooltip>
      <el-select v-model="mode" style="width:101px" @change="onChangeMode">
        <el-option label="度°" value="a"></el-option>
        <el-option label="度°分′秒″" value="b"></el-option>
        <el-option label="米m" value="c"></el-option>
      </el-select>
    </div>
    <div>
      <div class="wrapper-row" v-show="mode=='a'">
        <span style="font-weight:bold;width:43px;margin-left:12px;">经度：</span>
        <el-input-number v-model="ma.lng" size="small" :controls="false" style="width:160px"></el-input-number>
        <span style="font-weight:bold;width:43px;margin-left:12px;">纬度：</span>
        <el-input-number v-model="ma.lat" size="small" :controls="false" style="width:160px"></el-input-number>
      </div>
      <div class="wrapper-row" v-show="mode=='b'">
        <span style="font-weight:bold;width:43px;margin-left:12px;">经度：</span>
        <el-input-number v-model="mb.lngD" size="small" :controls="false" style="width:60px"></el-input-number>
        <span>度</span>
        <el-input-number v-model="mb.lngM" size="small" :controls="false" style="width:60px"></el-input-number>
        <span>分</span>
        <el-input-number v-model="mb.lngS" size="small" :controls="false" style="width:160px;"></el-input-number>
        <span>秒</span>
        <span style="font-weight:bold;width:43px;margin-left:12px;">纬度：</span>
        <el-input-number v-model="mb.latD" size="small" :controls="false" style="width:60px"></el-input-number>
        <span>度</span>
        <el-input-number v-model="mb.latM" size="small" :controls="false" style="width:60px"></el-input-number>
        <span>分</span>
        <el-input-number v-model="mb.latS" size="small" :controls="false" style="width:160px;"></el-input-number>
        <span>秒</span>
      </div>
      <div class="wrapper-row" v-show="mode=='c'">
        <span style="font-weight:bold;width:84px;margin-left:12px;">中央子午线：</span>
        <el-select v-model="mc.centralMeridian" style="width:160px;margin-left:12px;"
          filterable allow-create default-first-option
          @change="onChangeCentralMeridian">
          <el-option label="114度（38号带）" value="114"></el-option>
          <el-option label="117度（39号带）" value="117"></el-option>
          <el-option label="120度（40号带）" value="120"></el-option>
        </el-select>
        <span style="font-weight:bold;width:56px;margin-left:12px;">东坐标：</span>
        <el-input-number v-model="mc.east" size="small" :controls="false"></el-input-number>
        <span style="font-weight:bold;width:56px;margin-left:12px;">北坐标：</span>
        <el-input-number v-model="mc.north" size="small" :controls="false"></el-input-number>
      </div>
    </div>
    <el-button v-show="false" @click="onClickTest">测试</el-button>
  </div>
</template>

<script>
  import { fromGauss, toGauss } from "./math.js";

  export default {
    components:{
    },
    data() {
      return {
        mode: "a",
        ma: {
          lng: undefined,
          lat: undefined,
        },
        mb: {
          lngD: undefined,
          lngM: undefined,
          lngS: undefined,
          latD: undefined,
          latM: undefined,
          latS: undefined,
        },
        mc: {
          centralMeridian: undefined,
          east: undefined,
          north: undefined,
        }
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {
      onChangeMode(e) {
        console.log(5555, e)
        this.$emit("selected", e, this.mc.centralMeridian);
      },
      onChangeCentralMeridian(e) {
        this.$emit("selected", this.mode, e);
      },
  		onClickTest() {
        console.log(this.getValue());
  		},
      clear(){
        this.mode = "a";
        this.ma = {
          lng: undefined,
          lat: undefined,
        };
        this.mb = {
          lngD: undefined,
          lngM: undefined,
          lngS: undefined,
          latD: undefined,
          latM: undefined,
          latS: undefined,
        };
        this.mc = {
          centralMeridian: undefined,
          east: undefined,
          north: undefined,
        };
      },
      setValue(xx, yy) {
        this.mode = "a";
        this.ma.lng = xx;
        this.ma.lat = yy;
        xx = xx + "";
        yy = yy + "";
        if (xx.indexOf("度") != -1) {
          this.mode = "b";
          this.mb.lngD = xx.split("度")[0];
          this.mb.lngM = xx.split("度")[1].split("分")[0];
          this.mb.lngS = xx.split("度")[1].split("分")[1].split("秒")[0];
          this.mb.latD = yy.split("度")[0];
          this.mb.latM = yy.split("度")[1].split("分")[0];
          this.mb.latS = yy.split("度")[1].split("分")[1].split("秒")[0];
        }
        if (xx.indexOf("（中央子午线") != -1) {
          this.mode = "c";
          this.mc.east = xx.split("（中央子午线")[0];
          this.mc.north = yy.split("（中央子午线")[0];
          this.mc.centralMeridian = xx.split("（中央子午线")[1].split("）")[0];
        }
      },
      getValue() {
        let result = {};
        let lng = null;
        let lat = null;
        let xx = "";
        let yy = "";
        if (this.mode == "a") {
          if (!this.ma.lng || !this.ma.lat) {
            result.msg = "尚未完成输入";
            return result;
          }
          lng = this.ma.lng;
          lat = this.ma.lat;
          xx = this.ma.lng;
          yy = this.ma.lat;
        }
        if (this.mode == "b") {
          if (!this.mb.lngD || !this.mb.latD) {
            result.msg = "尚未完成输入";
            return result;
          }
          lng = this.mb.lngD + (this.mb.lngM||0)/60 + (this.mb.lngS||0)/3600;
          lat = this.mb.latD + (this.mb.latM||0)/60 + (this.mb.latS||0)/3600;
          xx = this.mb.lngD + "度" + this.mb.lngM + "分" + this.mb.lngS + "秒";
          yy = this.mb.latD + "度" + this.mb.latM + "分" + this.mb.latS + "秒";
        }
        if (this.mode == "c") {
          if (!this.mc.east || !this.mc.north || !this.mc.centralMeridian) {
            result.msg = "尚未完成输入";
            return result;
          }
          if (isNaN(this.mc.centralMeridian)){
            result.msg = "中央子午线必须是数字";
            return result;
          }
          let east = this.mc.east;
          if (east > 30 * 1000 * 1000) { //带了代号的
            east = east - Math.floor(east/1000/1000)*1000*1000;
          }
          let gauss = fromGauss(east, this.mc.north, parseFloat(this.mc.centralMeridian));
          lng = gauss[0];
          lat = gauss[1];
          xx = this.mc.east + "（中央子午线" + this.mc.centralMeridian + "）";
          yy = this.mc.north + "（中央子午线" + this.mc.centralMeridian + "）";
        }

        result.lng = lng;
        result.lat = lat;
        result.xx = xx;
        result.yy = yy;

        if (lng < 113 || lng > 120) {
          result.msg = "经度超出河北省范围";
          return result;
        }
        if (lat < 36 || lat > 43) {
          result.msg = "纬度超出河北省范围";
          return result;
        }
        result.msg = "OK";
        return result;

      },
    },
  }
</script>

<style scoped>
  .wrapper-row{
    display: flex;
    flex-direction: row;
  }
  .wrapper-page{
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
  }
</style>
