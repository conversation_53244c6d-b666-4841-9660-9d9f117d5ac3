<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="75px">

      <el-form-item label="项目名称" prop="projectName">
        <el-input
          clearable
          v-model.trim="queryParams.projectName"
          placeholder="请输入项目名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.sectionName"
          placeholder="请输入标段名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关键字" prop="keyWord">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.keyWord"
          placeholder="请输入姓名/车牌号码/手机号码"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <my-select
          v-model="queryParams.status"
          pvalue="billingStatus"
          laceholder="请选择状态">
        </my-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="createTime">
<!--        <el-date-picker
          v-model="createTime"
          :clearable="false"
          type="datetimerange"
          range-separator="-"
          :default-time="['00:00:00','23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="年/月/日 时:分:秒"
          end-placeholder="年/月/日 时:分:秒"
          @change="handleSignTime"
        >
        </el-date-picker>-->
        <div style="display: flex;align-items: center;justify-content: center">
          <el-date-picker
            v-model="queryParams.createStartDateTime "
            type="date"
            :picker-options="pickerStartDate"
            value-format="yyyy-MM-dd"
            :clearable="false"
            placeholder="开始日期"
            style="width: 130px"
          >
          </el-date-picker>
          <div style="padding: 0px 10px">-</div>
          <el-date-picker
            v-model="queryParams.createEndDateTime "
            type="date"
            :clearable="false"
            :picker-options="pickerEndDate"
            value-format="yyyy-MM-dd"
            placeholder="结束日期"
            style="width: 130px"
          >
          </el-date-picker>
        </div>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-show="false"
          v-hasPermi="['bill:apply:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-show="false"
          v-hasPermi="['bill:apply:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-show="false"
          v-hasPermi="['bill:apply:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/bill/apply/page" ref="applyTable" row-key="id" :fixed="true" :auto-request="false" @my-selection-change="handleSelectionChange">
      <el-table-column label="项目名称" header-align="center" fixed="left"  align="left" min-width="200" prop="name" sortable="custom"
                       column-key="project.NAME">
        <template #default="scope">
          <el-tooltip :content="scope.row.projectName" placement="top" effect="light">
            <div v-if="scope.row.sectionName ===''">{{ scope.row.projectName }}</div>
            <div v-else>{{ scope.row.projectName+'('+scope.row.sectionName+')' }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column  label="车牌号" fixed="left" min-width="120" prop="carNumber" align="center" header-align="center" sortable="custom" column-key="CAR_NUMBER"></el-table-column>
      <el-table-column  label="运砂人" min-width="100" align="center" prop="driverName" sortable="custom" column-key="user.SHOW_NAME"></el-table-column>
      <el-table-column  label="联系电话" min-width="120" align="center" prop="driverMobile" sortable="custom" column-key="user.MOBILE"></el-table-column>
      <el-table-column  label="自重(吨)" min-width="100" header-align="center" align="left" prop="carKerbWeight" sortable="custom" column-key="CAR_KERB_WEIGHT">
        <template #default="scope">
          {{common.toThousands(scope.row.carKerbWeight,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="最大载重(吨)" min-width="120" header-align="center" align="left" prop="carMaxPayload" sortable="custom" column-key="CAR_MAX_PAYLOAD">
        <template #default="scope">
          {{common.toThousands(scope.row.carMaxPayload,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="卸砂地点" min-width="170" header-align="center" align="left"  prop="destination" sortable="custom" column-key="DESTINATION"></el-table-column>
      <el-table-column  label="预计到达时间" min-width="140" align="center" prop="estimatedArrivalTime" sortable="custom" column-key="ESTIMATED_ARRIVAL_TIME"></el-table-column>
      <el-table-column  label="开单状态" align="center" min-width="100" sortable="custom" column-key="STATUS">
        <template #default="scope">
          <my-view pvalue="billingStatus" :value="scope.row.status"></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="申请时间" align="center" min-width="180" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center" min-width="100">
    <template slot-scope="scope">
      <el-button
        v-show="scope.row.status==='notBilled'"
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        title="修改"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['bill:apply:update']"
      ></el-button>
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-document-copy"
        title="查看"
        @click="handleView(scope.row)"
      ></el-button>
<!--      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['bill:apply:delete']"
      ></el-button>-->
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改运砂申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :zIndex="4000">
      <el-form ref="form" :model="apply"  label-width="110px">
        <el-row>
          <el-col :span="12">
            <my-form-item
              label="项目名称"
              ref="projectId"
              prop="projectId"
              :rules="[{notNull:true,message:'请选择项目', trigger:['blur','change']}]"
            >
              <my-select
                :disabled="view === 'detail'"
                v-model="apply.projectId"
                :options="projectList"
                placeholder="请选择项目"
                @change="projectSelectChange"
              >
                <el-option v-for="item in projectList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="车牌号"  ref="carNumber" prop="carNumber" :rules="[{notNull:true,message:'请选择车牌号'}]">
              <my-select
                :disabled="view === 'detail'"
                v-model="apply.carNumber"
                placeholder="请选择车牌号"
                @change="changeCarNumber"
              >
                <el-option v-for="item in carList"
                           :key="item.id"
                           :value="item.number"
                           :label="item.number"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="apply.carCompany!==''">
            <my-form-item label="车辆所属" ref="carCompany" prop="carCompany">
              <my-input disabled="disabled"  v-model="apply.carCompany" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="品牌型号" ref="carBrand" prop="carBrand">
              <my-input disabled="disabled"  v-model="apply.carBrand"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="车主姓名" ref="carOwnerName" prop="carOwnerName" >
              <my-input disabled="disabled" v-model="apply.carOwnerName"  />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="自重(吨)" ref="carKerbWeight" prop="carKerbWeight">
              <my-input disabled="disabled" v-model="apply.carKerbWeight" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>

          <el-col :span="12">
            <my-form-item label="最大载重(吨)" ref="carMaxPayload" prop="carMaxPayload">
              <my-input disabled="disabled" v-model="apply.carMaxPayload" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="预计到达时间" ref="estimatedArrivalTime" prop="estimatedArrivalTime" :rules="[{notNull:true,message:'请输入预计到达时间'}]">
              <el-date-picker
                :disabled="view === 'detail'"
                v-model="apply.estimatedArrivalTime"
                type="datetime"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm"
                placeholder="预计到达时间">
              </el-date-picker>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-show="view ==='detail'">
            <my-form-item label="开单状态" ref="status" prop="status">
              <my-select
                id="status"
                :disabled="view ==='detail'"
                pvalue="billingStatus"
                v-model="apply.status"
                placeholder="开单状态"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12" v-show="view ==='detail'">
            <my-form-item label="创建时间" ref="createTime" prop="createTime">
              <my-input :disabled="view ==='detail'" v-model="apply.createTime" placeholder="请输入创建时间" />
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="卸砂地点" ref="destination" prop="destination" :rules="[{notNull:true,message:'请选择卸砂地点'}]">
              <my-input v-if="view==='detail'" disabled="disabled"  v-model="apply.destination"  placeholder="请选择卸砂地点" />
              <my-input v-else readonly="readonly"  v-model="apply.destination" @click.native="changeMap"  placeholder="请选择卸砂地点" />
            </my-form-item>
          </el-col>
          <el-col :span="12">
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item
              label="经度"
              ref="destLongitude"
              prop="destLongitude"
              >
              <my-input
                disabled="disabled"
                v-model="apply.destLongitude"
                placeholder="请输入经度"
                :clearable="true"
              />
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item
              label="纬度"
              ref="destLatitude"
              prop="destLatitude"
              >
              <my-input
                disabled="disabled"
                v-model="apply.destLatitude"
                :clearable="true"
                placeholder="请输入纬度"
              />
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="view !== 'detail'" type="primary" @click="submitForm">确 定</el-button>
        <el-button v-show="view !== 'detail'" @click="cancel">取 消</el-button>
        <el-button v-show="view === 'detail'" @click="cancel">关闭</el-button>
      </div>
    </el-dialog>

    <my-dialog :title="mapTitle" :visible.sync="openMap" width="1000px" height="70vh" append-to-body :zIndex="5000">
      <el-form ref="apply" :model="apply" label-width="80px">
        <!-- 腾讯地图组件 -->
        <my-tencent-map
          :center="{lat: 38.041323, lng: 114.514686}"
          :zoom="12"
          :switchType="true"
          :lat="Number(apply.destLatitude)"
          :lng="Number(apply.destLongitude)"
          @change="getSelectPosition"
        />
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMap">确 定</el-button>
        <el-button @click="cancelMap">取 消</el-button>
      </div>
    </my-dialog>

  </div>
</template>

<script>
import {
  getApply,
  delApply,
  delApplyBatch,
  addApply,
  updateApply,
  getMyCarList
} from "@/api/bill/apply";
import Template
  from "@/views/sms/template/index.vue";
import {
  listProjectNames,
  listStationByProjectId
} from "@/api/client/client";
import MyTencentMap
  from "@/components/YB/MyTencentMap.vue";
import common
  from "@/utils/common";

export default {
  name: "Apply",
  computed: {
    common() {
      return common
    },
  },
  components: {
    MyTencentMap,
    Template},
  data() {
    return {
      //地图相关
      mapTitle:'选择运砂地点',
      openMap:false,
      mapData:{

      },
      carList:[],
      projectList:[],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      view:'',
      // 查询参数
      queryParams: {
        projectName:'',
        sectionName:'',
        status: '',
        keyWord:'',
        createStartDateTime: common.formatDate(new Date(),'yyyy-MM-01'),
        createEndDateTime: common.formatDate(new Date(),'yyyy-MM-dd')
      },
      // 查询参数-申请时间
      createTime:[common.formatDate(new Date(),'yyyy-MM-01 00:00:00'),common.formatDate(new Date(),'yyyy-MM-dd 23:59:59')],
      // 表单参数
      apply: {},
      oneDayMsec: 1 * 24 * 3600 * 1000,
      pickerStartDate:this.pickerStartDate1(),
      pickerEndDate:this.pickerEndDate1(),
    };
  },
  mounted() {
    this.reload()
  },
  methods: {
    pickerStartDate1(){
      let that = this;
      return {
        disabledDate(time) {
          return  Date.parse(new Date(common.formatDate(time, 'yyyy-MM-dd')))  > Date.parse(new Date(that.queryParams.createEndDateTime))
        }
      }
    },
    pickerEndDate1() {
      let that = this;
      return {
        disabledDate(time) {
          return  Date.parse(new Date(common.formatDate(time, 'yyyy-MM-dd')))  < Date.parse(new Date(that.queryParams.createStartDateTime))

        }
      }
    },
    //获取项目列表
    getProjectName(){
      listProjectNames().then(res => {
        if (res.list && res.list.length > 0) {
          this.projectList = res.list.map(item => {
            if(item.sectionName){
              return { 'name': item.name, 'value': item.deptId, 'sectionName':item.sectionName}
            }else {
              return { 'name': item.name, 'value': item.deptId }
            }
          });
        }
      })
    },
    //更改车牌号
    changeCarNumber(val){
      if (val){
        const obj = this.carList.find(item => {
          return item.number === val;
        });
        this.apply.carBrand = obj.brand
        this.apply.carOwnerName = obj.ownerName
        this.apply.carKerbWeight = obj.kerbWeight
        this.apply.carMaxPayload = obj.maxPayload
        console.log(obj)
      }
    },
    //获取我的车辆列表
    getMyCarLists(userId){
      getMyCarList(userId).then(res =>{
        if (res.carList && res.carList.length > 0) {
          this.carList = res.carList
        }
      })
    },
    /** 查询运砂申请列表 */
    reload(restart) {
      this.$refs.applyTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.apply = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        projectId: '',
        carNumber: '',
        carCompany: '',
        carBrand: '',
        carOwnerName: '',
        carKerbWeight: null ,
        carMaxPayload: null ,
        destination: '',
        destLongitude: null ,
        destLatitude: null ,
        estimatedArrivalTime: '',
        destLeader: '',
        destLeaderMobile: '',
        status: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createStartDateTime = common.formatDate(new Date(),'yyyy-MM-01')
      this.queryParams.createEndDateTime = common.formatDate(new Date(),'yyyy-MM-dd')
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.applyTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.view = 'add'
      this.title = "添加运砂申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.getProjectName();
      this.getMyCarLists(row.createUserId);
      this.reset();
      const id = row.id || this.ids[0];
      getApply(id).then(r => {
        this.apply = r.apply;
        this.open = true;
        this.view = 'update'
        this.title = "修改运砂申请";
      });
    },
    handleView(row){
      this.getProjectName();
      this.reset();
      const id = row.id || this.ids[0];
      getApply(id).then(r => {
        this.apply = r.apply;
        this.open = true;
        this.view = 'detail'
        this.title = "查看运砂申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.apply.id) {
            updateApply(this.apply).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addApply(this.apply).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delApply(row.id);
        }else{
          return delApplyBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 项目选择change事件 */
    projectSelectChange(val) {
      this.stationList = [];
      this.apply.stationId = '';
      this.apply.stationName = '';
      if (val) {
        const obj = this.projectList.find(item => {
          return item.value === val;
        });
        console.log(obj)
        this.apply.projectName = obj.name ? obj.name : '';
        this.apply.sectionName = obj.sectionName? obj.sectionName : '';
        listStationByProjectId(val).then(res => {
          if (res.list && res.list.length > 0) {
            this.stationList = res.list.map(item => {
              return {'name': item.name, 'value': item.id}
            });
          }
        });
      } else {
        this.apply.projectName = '';
      }
    },
    /** 正则验证经度输入 */
    validateLng(rule, value, callback) {
      var that = this;
      return new Promise((resolve, reject) => {
        if (that.apply.destLongitude) {
          if ((Number(that.apply.destLongitude) >= 112) && (Number(that.apply.destLongitude) <= 121)) {
            resolve();
          } else {
            reject();
          }
        } else {
          resolve();
        }
      });
    },

    /** 正则验证纬度输入 */
    validateLat(rule, value, callback) {
      var that = this;
      return new Promise((resolve, reject) => {
        if (that.apply.destLatitude) {
          if ((Number(that.apply.destLatitude) >= 35) && (Number(that.apply.destLatitude) <= 44)) {
            resolve();
          } else {
            reject();
          }
        } else {
          resolve();
        }
      });
    },
    changeMap(){
      this.openMap = true

    },
    /** 获取选中点地址信息 */
    getSelectPosition(data) {
      console.log(data);
      this.apply.destLongitude = data.longitude;
      this.apply.destLatitude = data.latitude;
      this.apply.destination = data.address;
    },
    /** 获取选中点地址信息 */
    submitMap() {
      if (this.apply.address ===''){
        this.$modal.msgError("请选择卸砂地点")
      }
      this.openMap = false
    },
    cancelMap(){
      this.openMap = false
    }


  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.applyTable.changeTableHeight();
  },
};
</script>
<style scoped lang="scss">


::v-deep .el-switch__label *{
  width: 50px!important;
}
</style>
