<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="月度" prop="startMonth" v-if="!permission">
        <el-date-picker
          v-model="queryParams.startMonth"
          type="month"
          clearable
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="月度" prop="startMonth"  v-if="permission">
        <el-date-picker
          v-model="createMonth"
          type="monthrange"
          align="right"
          :clearable="false"
          unlink-panels
          value-format="yyyy-MM"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否国债" prop="nationalDebt">
        <my-select v-model="queryParams.nationalDebt" pvalue="boolean" placeholder="请选择是否国债"></my-select>
      </el-form-item>
<!--      <el-form-item label="区域" prop="areaCode">-->
<!--        <my-area-select v-model="queryParams.areaCode" placeholder="请选择区域" size="small"></my-area-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reports:riverAbandonReport:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reports:riverAbandonReport:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reports:riverAbandonReport:delete']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-has-permi="['reports:riverAbandonReport:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              :cell-style="{padding: '0px'}"
              show-summary
              :summary-method="getSummaries"
              @query-success="handleQuerySuccess"
              url="/reports/riverSandReport/abandonReportPage" tooltip-effect="light" :show-radio="false" :multiselect="false" :show-pager="false" :tree-props="{children: 'children'}"  :default-query-params="queryParams" :fixed="true" ref="areaStatTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column class-name="isleft" label-class-name="islabel"  show-overflow-tooltip label="所属区县" min-width="200" fixed="left" prop="areaName" header-align="center" >
        <template #default="{row}">
          <div class="rowStyle">{{row.areaName}}</div>
        </template>
      </el-table-column>
      <el-table-column  label="年月" min-width="120" prop="yearMonth"  header-align="center" align="center" ></el-table-column>
      <el-table-column  label="弃砂项目数" min-width="70" prop="abandonProjectCount"  header-align="center"  align="right" ></el-table-column>
      <el-table-column  label="计划利用砂石量（万吨）" min-width="95" prop="abandonTotalYield"  header-align="center"  align="right">
        <template #default="{row}">
          {{row.abandonTotalYield?common.toThousands(row.abandonTotalYield, 2, ','):common.toThousands(0, 2, ',')}}
        </template>
      </el-table-column>
      <el-table-column  label="利用量（万吨）" min-width="65" prop="abandonTotalVehicleLoad"  header-align="center"  align="right">
        <template #default="{row}">
          {{row.abandonTotalVehicleLoad?common.toThousands(row.abandonTotalVehicleLoad,2,','):common.toThousands(0,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="累计利用量（万吨）" min-width="80" prop="abandonTotalVehicleLoadSum"  header-align="center"  align="right">
        <template #default="{row}">
          {{row.abandonTotalVehicleLoadSum?common.toThousands(row.abandonTotalVehicleLoadSum,2,','):common.toThousands(0,2,',')}}
        </template>
      </el-table-column>
      <el-table-column  label="纸质开单利用量（万吨）" min-width="90" prop="historyYield"  header-align="center"  align="right">
        <template #default="{row}">
          {{row.historyYield?common.toThousands(row.historyYield,2,','):common.toThousands(0,2,',')}}
        </template>
      </el-table-column>
    </my-table>
    <div style="font-size: 14px;color: red;font-weight: 600">
      备注：利用量等于当月（期）采运单累计载运量，
      25年前利用量等于项目中录入的25年前利用量之和，
      累计利用量等于项目所有采运单累计载运量加25年前利用量。
    </div>

    <!-- 添加或修改区域项目统计表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="riverAbandonReport"  label-width="80px">
                              <my-form-item label="创建时间" ref="createTime" prop="createTime" :rules="[{notNull:true,message:'请输入创建时间'}]">
        <my-input v-model.trim="riverAbandonReport.createTime" placeholder="请输入创建时间" :maxlength="20"/>
      </my-form-item>
                              <my-form-item label="主管部门ID" ref="deptId" prop="deptId" :rules="[{notNull:true,message:'请输入主管部门ID'}]">
        <my-input v-model.trim="riverAbandonReport.deptId" placeholder="请输入主管部门ID" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="主管部门编码" ref="deptCode" prop="deptCode" :rules="[{notNull:true,message:'请输入主管部门编码'}]">
        <my-input v-model.trim="riverAbandonReport.deptCode" placeholder="请输入主管部门编码" :maxlength="50"/>
      </my-form-item>
                              <my-form-item label="区域编码" ref="areaCode" prop="areaCode" :rules="[{notNull:true,message:'请输入区域编码'}]">
        <my-input v-model.trim="riverAbandonReport.areaCode" placeholder="请输入区域编码" :maxlength="50"/>
      </my-form-item>
                              <my-form-item label="年月;格式：yyyy-MM" ref="yearMonth" prop="yearMonth" :rules="[{notNull:true,message:'请输入年月;格式：yyyy-MM'}]">
        <my-input v-model.trim="riverAbandonReport.yearMonth" placeholder="请输入年月;格式：yyyy-MM" :maxlength="7"/>
      </my-form-item>
                              <my-form-item label="年;格式：yyyy" ref="year" prop="year" :rules="[{notNull:true,message:'请输入年;格式：yyyy'}]">
        <my-input v-model.trim="riverAbandonReport.year" placeholder="请输入年;格式：yyyy" :maxlength="4"/>
      </my-form-item>
                              <my-form-item label="月;格式：MM" ref="month" prop="month" :rules="[{notNull:true,message:'请输入月;格式：MM'}]">
        <my-input v-model.trim="riverAbandonReport.month" placeholder="请输入月;格式：MM" :maxlength="2"/>
      </my-form-item>
                              <my-form-item label="采砂项目个数" ref="sandProjectCount" prop="sandProjectCount" :rules="[{notNull:true,message:'请输入采砂项目个数'}]">
        <my-input v-model.trim="riverAbandonReport.sandProjectCount" placeholder="请输入采砂项目个数" :maxlength="11"/>
      </my-form-item>
                              <my-form-item label="弃砂项目数" ref="abandonProjectCount" prop="abandonProjectCount" :rules="[{notNull:true,message:'请输入弃砂项目数'}]">
        <my-input v-model.trim="riverAbandonReport.abandonProjectCount" placeholder="请输入弃砂项目数" :maxlength="11"/>
      </my-form-item>
                      <my-form-item label="总控制总量（万吨）" ref="totalYield" prop="totalYield" :rules="[{notNull:true,message:'请输入总控制总量（万吨）'}]">
        <my-input v-model.trim="riverAbandonReport.totalYield" placeholder="请输入总控制总量（万吨）" />
      </my-form-item>
                      <my-form-item label="总治理河长（km）" ref="totalReverLength" prop="totalReverLength" :rules="[{notNull:true,message:'请输入总治理河长（km）'}]">
        <my-input v-model.trim="riverAbandonReport.totalReverLength" placeholder="请输入总治理河长（km）" />
      </my-form-item>
                              <my-form-item label="采砂项目单据数量" ref="sandBillCount" prop="sandBillCount" :rules="[{notNull:true,message:'请输入采砂项目单据数量'}]">
        <my-input v-model.trim="riverAbandonReport.sandBillCount" placeholder="请输入采砂项目单据数量" :maxlength="11"/>
      </my-form-item>
                              <my-form-item label="弃砂项目单据数量" ref="abandonBillCount" prop="abandonBillCount" :rules="[{notNull:true,message:'请输入弃砂项目单据数量'}]">
        <my-input v-model.trim="riverAbandonReport.abandonBillCount" placeholder="请输入弃砂项目单据数量" :maxlength="11"/>
      </my-form-item>
                      <my-form-item label="采砂累计载运量" ref="sandTotalVehicleLoad" prop="sandTotalVehicleLoad" :rules="[{notNull:true,message:'请输入采砂累计载运量'}]">
        <my-input v-model.trim="riverAbandonReport.sandTotalVehicleLoad" placeholder="请输入采砂累计载运量" />
      </my-form-item>
                      <my-form-item label="弃砂累计载运量" ref="abandonTotalVehicleLoad" prop="abandonTotalVehicleLoad" :rules="[{notNull:true,message:'请输入弃砂累计载运量'}]">
        <my-input v-model.trim="riverAbandonReport.abandonTotalVehicleLoad" placeholder="请输入弃砂累计载运量" />
      </my-form-item>
          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getAreaStat, delAreaStat, delAreaStatBatch,addAreaStat, updateAreaStat } from "@/api/reports/riverAbandonReport";
import MyAreaSelect from '@/components/YB/MyAreaSelect_back.vue'
import common from '@/utils/common'

export default {
  name: "RiverAbandonReport",
  computed: {
    common() {
      return common
    },
    permission() {
      return this.$checkPermi(['reports:riverAbandonReport:provincialMonthly'])
    }
  },
  components: { MyAreaSelect },
  watch: {
    "createMonth": {
      handler(val) {
        if(val){
          if(!this.permission){
            this.queryParams.startMonth = common.formatDate(new Date(), "yyyy-MM");
            this.queryParams.endMonth = ''
          }else {
            if(val[0] == val[1]){
              this.queryParams.startMonth = val[0];
              this.queryParams.endMonth = ''
            }else {
              this.queryParams.startMonth = val[0];
              this.queryParams.endMonth = val[1]
            }
          }
        }
      },
      immediate: true,
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      createMonth:[ common.formatDate(new Date(), "yyyy-01"), common.formatDate(new Date(), "yyyy-MM")],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        startMonth: '',
        endMonth: '',
        nationalDebt: '',
      },
      // 表单参数
      riverAbandonReport: {},
      totalList:[]
    };
  },
  mounted() {
  },
  methods: {
    handleQuerySuccess(data){
      this.totalList = data.total
    },
    getSummaries(params) {
      let showTotal = this.totalList;
      const { columns } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          sums[3] = showTotal.abandonProjectCount
          sums[4] = common.toThousands(showTotal.abandonTotalYield, 2, ',')
          sums[5] = common.toThousands(showTotal.abandonTotalVehicleLoad, 2, ',')
          sums[6] = common.toThousands(showTotal.abandonTotalVehicleLoadSum, 2, ',')
          sums[7] = common.toThousands(showTotal.historyYield, 2, ',')
          return;
        }
      })
      return sums;
    },
    /** 查询区域项目统计表列表 */
    reload(restart) {
      this.$refs.areaStatTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.riverAbandonReport = {
        id: '',
        createTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        yearMonth: '',
        year: '',
        month: '',
        sandProjectCount: null ,
        abandonProjectCount: null ,
        totalYield: null ,
        totalReverLength: null ,
        sandBillCount: null ,
        abandonBillCount: null ,
        sandTotalVehicleLoad: null ,
        abandonTotalVehicleLoad: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      if(this.permission){
        this.queryParams.startMonth =  common.formatDate(new Date(), "yyyy-01")
        this.queryParams.endMonth =  common.formatDate(new Date(), "yyyy-MM")
        this.createMonth = [common.formatDate(new Date(), "yyyy-01"), common.formatDate(new Date(), "yyyy-MM")]
      }
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.areaStatTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加区域项目统计表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getAreaStat(id).then(r => {
        this.riverAbandonReport = r.riverAbandonReport;
        this.open = true;
        this.title = "修改区域项目统计表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.riverAbandonReport.id) {
            updateAreaStat(this.riverAbandonReport).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addAreaStat(this.riverAbandonReport).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delAreaStat(row.id);
        }else{
          return delAreaStatBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExport(){
      this.download('/reports/riverSandReport/exportAbandonReport', {
        ...this.queryParams
      }, `砂石统计量_弃砂_${new Date().getTime()}.xlsx`, 'application/json');
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.areaStatTable&&this.$refs.areaStatTable.changeTableHeight();
  },
};
</script>

<style scoped>
.rowStyle {
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  overflow: hidden !important;
}
::v-deep  .isleft .cell {
  display: flex !important;
}
::v-deep .el-table__placeholder {
  width: 0 !important;
  padding-left: 20px !important;
}
::v-deep .islabel .cell {
  display: flex !important;
  justify-content: center!important;
}
</style>
