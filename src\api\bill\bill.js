import request from '@/utils/request'

// 查询运砂单据表详细
export function getBill(id) {
  return request({
    url: '/bill/bill/info/' + id,
    method: 'post'
  })
}
//获取运砂量
export function getAllNum(data) {
  return request({
    url: '/bill/bill/getTotalVehicleLoad',
    method: 'post',
    data: data,
  })
}
// 新增运砂单据表
export function addBill(data) {
  return request({
    url: '/bill/bill/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改运砂单据表
export function updateBill(data) {
  return request({
    url: '/bill/bill/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除运砂单据表
export function delBill(id) {
  return request({
    url: '/bill/bill/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除运砂单据表
export function delBillBatch(ids) {
  return request({
    url: '/bill/bill/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}
// 采运单申诉审批
export function appealBill(ids) {
  return request({
    url: '/bill/bill/documentAppeal',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}
// 核销地申诉信息查询
export function getAppealBillData(data) {
  return request({
    url: '/bill/appeal/listByBillId',
    method: 'get',
    params: data,
  })
}
// 核销地申诉通过
export function appealBillSucess(data) {
  return request({
    url: `/bill/appeal/approve?billId=${data.billId}`,
    method: 'get',
  })
}

// 核销地申诉驳回
export function appealBillReject(data) {
  return request({
    url: `/bill/appeal/reject?billId=${data.billId}`,
    method: 'get',
  })
}

//获取项目标段列表
export function getProjectSection(data) {
  return request({
    url: '/project/project/queryProjectSection',
    method: 'post',
    data: data,
  })
}
