<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="月度" prop="startMonth" v-if="!permission">

        <el-date-picker
          v-model="queryParams.startMonth"
          type="month"
          :clearable="false"
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="月度" prop="createMonth"  v-if="permission">
        <el-date-picker
          v-model="createMonth"
          type="monthrange"
          align="right"
          :clearable="false"
          unlink-panels
          value-format="yyyy-MM"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>

<!--      <el-form-item label="区域" prop="areaCode">-->
<!--        <my-area-select v-model="queryParams.areaCode" placeholder="请选择区域" size="small"></my-area-select>-->
<!--      </el-form-item>-->
      <el-form-item label="填报状态" prop="status"  v-if="!permission">
        <my-select
          v-model="queryParams.status"
          placeholder="请选择填报状态"
          size="small"
          pvalue="fillStatus"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['reports:riverSandReport:save']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['reports:riverSandReport:update']"-->
<!--        >填报</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['reports:riverSandReport:delete']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-has-permi="['reports:riverSandReport:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table :row-style="{height: '35px'}"
              :cell-style="columnStyle"
              :header-cell-style="headerStyle"
              show-summary
              row-key="id"
              :expand-row-keys="expandedRows"
              :summary-method="getSummaries"
              @query-success="handleQuerySuccess"
              @expand-change="handleExpandChange"
              url="/reports/riverSandReport/sandReportPage" tooltip-effect="light" :show-radio="false" :multiselect="false" :show-pager="false" :tree-props="{children: 'children'}" :default-query-params="queryParams" :fixed="true" ref="riverSandReportTable" @my-selection-change="handleSelectionChange">
      <el-table-column class-name="isleft"   label-class-name="islabel" :show-overflow-tooltip="true" label="区域" fixed="left" min-width="250" prop="areaName" header-align="center" align="left" >
        <template #default="{row}">
          <div class="rowStyle">{{row.areaName}}</div>
        </template>
      </el-table-column>
      <el-table-column  label="年月" min-width="80" prop="yearMonth"  header-align="center" align="center"></el-table-column>
      <el-table-column  v-if="!permission" label="填报状态" min-width="80" prop="status"  header-align="center" align="center" >
        <template #default="{ row }">
          <my-view pvalue="fillStatus" :value="row.status" ></my-view>
        </template>
      </el-table-column>
      <el-table-column  label="采砂项目数" min-width="70" prop="sandProjectCount"  header-align="center" align="right" ></el-table-column>
      <el-table-column  label="年度计划总投资（万元）" min-width="100"  header-align="center" align="right" prop="plandedTotalInvestment" >
        <template #default="{row}">
          {{toThousands(row.plandedTotalInvestment)}}
        </template>
      </el-table-column>
      <el-table-column  label="当月（期）完成投资（万元）" min-width="120"  header-align="center" align="right" prop="monthInvestment">
        <template #default="{row}">
          {{toThousands(row.monthInvestment)}}
        </template>
      </el-table-column>
      <el-table-column  label="年度计划治理河长（km）" min-width="100"  header-align="center" align="right" prop="totalReverLength" >
        <template #default="{row}">
          {{toThousands1(row.totalReverLength)}}
        </template>
      </el-table-column>
      <el-table-column  label="当月（期）治理河长（km）" min-width="120"  header-align="center" align="right" prop="monthRiverLength" >
        <template #default="{row}">
          {{toThousands1(row.monthRiverLength)}}
        </template>
      </el-table-column>
      <el-table-column  label="计划利用砂石量（万吨）" min-width="100"  header-align="center" align="right" prop="sandTotalYield" >
        <template #default="{row}">
          {{toThousands(row.sandTotalYield)}}
        </template>
      </el-table-column>
      <el-table-column  label="当月（期）利用量（万吨）" min-width="120"  header-align="center" align="right" prop="sandTotalVehicleLoad" >
        <template #default="{row}">
          {{toThousands(row.sandTotalVehicleLoad/10000)}}
        </template>
      </el-table-column>
      <el-table-column v-if="!permission" label="操作" column-key="caozuo"min-width="80"   fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="(scope.row.areaLevel=='项目'&&!scope.row.children)||scope.row.areaLevel=='标段'"
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            title="填报"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['reports:riverSandReport:update']"
          >填报</el-button>
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['reports:riverSandReport:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>


    <!-- 添加或修改砂石量统计-采砂报表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="riverSandReport"  label-width="200px">
        <my-form-item label="区域" ref="areaCode" prop="areaCode" >
          <my-area-select v-model="riverSandReport.areaCode" :disabled="true" placeholder="请选择区域" size="small"></my-area-select>
        </my-form-item>
        <my-form-item label="当月完成投资（万元）" ref="monthInvestment" prop="monthInvestment" :rules="[{notNull:true,message:'请输入当月完成投资（万元）'},{regExp:/^(0|\d+)(\.\d+)?$/,message:'当月完成投资（万元）只能为数字且不能小于0', trigger:['blur','change']}]">
          <my-input v-model.trim="riverSandReport.monthInvestment" :maxlength="16" placeholder="请输入当月完成投资（万元）" />
        </my-form-item>
        <my-form-item label="当月治理河长（km）" ref="monthRiverLength" prop="monthRiverLength" :rules="[{notNull:true,message:'请输入当月治理河长（km）'},{regExp:/^(0|\d+)(\.\d+)?$/,message:'当月治理河长（km）只能为数字且不能小于0', trigger:['blur','change']}]">
          <my-input v-model.trim="riverSandReport.monthRiverLength" :maxlength="16" placeholder="请输入当月治理河长（km）" />
        </my-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getRiverSandReport, delRiverSandReport, delRiverSandReportBatch,addRiverSandReport, updateRiverSandReport } from "@/api/reports/riverSandReport";
import common from '@/utils/common'
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'

export default {
  name: "RiverSandReport",
  computed: {
    common() {
      return common
    },
    toThousands(){
      return function(data) {
        if(data){
          return  common.toThousands(data, 2, ',')
        }else{
          return  common.toThousands(0, 2, ',')
        }
      }
    },
    toThousands1(){
      return function(data) {
        if(data){
          return  common.toThousands(data, 3, ',')
        }else{
          const a = 0
          return  a.toFixed(3)
        }
      }
    },
    permission() {
      return this.$checkPermi(['reports:riverSandReport:provincialMonthly'])
    }
  },
  components: { MyAreaSelect },
  watch: {
    "createMonth": {
      handler(val) {
        if(val){
          if(!this.permission){
            this.queryParams.startMonth = common.formatDate(new Date(), "yyyy-MM");
            this.queryParams.endMonth = ''
          }else {
            if(val[0] == val[1]){
              this.queryParams.startMonth = val[0];
              this.queryParams.endMonth = ''
            }else {
              this.queryParams.startMonth = val[0];
              this.queryParams.endMonth = val[1]
            }
          }
        }
      },
      immediate: true,
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      createMonth:[ common.formatDate(new Date(), "yyyy-01"), common.formatDate(new Date(), "yyyy-MM")],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        startMonth: '',
        endMonth: '',
        status: '',
      },
      // 表单参数
      riverSandReport: {},
      totalList: [],
      expandedRows: [],
      tableData: [],
    };
  },
  mounted() {
  },
  methods: {
    handleExpandChange(row, expanded) {
      if (expanded) {
        this.expandedRows.push(row.id);
      } else {
        const index = this.expandedRows.indexOf(row.id);
        if (index !== -1) {
          this.expandedRows.splice(index, 1);
        }
      }
    },
    handleQuerySuccess(data){
      this.tableData = data.list;
      this.totalList = data.total
    },
    columnStyle({ row, column, rowIndex, columnIndex }) {

      if(this.$checkPermi(['reports:riverSandReport:provincialMonthly'])){
        if (columnIndex == 5||columnIndex == 7||columnIndex == 9) {
          return {background:'#FFF8DC'}
        } else{
          return {padding:'0'}
        }
      }else {
        if (columnIndex == 6||columnIndex == 8||columnIndex == 10) {
          return {background:'#FFF8DC'}
        } else{
          return {padding:'0'}
        }
      }
    },
    headerStyle({ row, column, rowIndex, columnIndex }) {
      if(this.$checkPermi(['reports:riverSandReport:provincialMonthly'])){
        if (columnIndex == 5||columnIndex == 7||columnIndex == 9) {
          return {background:'#FFF8DC'}
        }
      }else {
        if (columnIndex == 6||columnIndex == 8||columnIndex == 10) {
          return {background:'#FFF8DC'}
        }
      }
    },
    getSummaries(params){
      let showTotal = this.totalList;
      const { columns } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          if(this.$checkPermi(['reports:riverSandReport:provincialMonthly'])){
            sums[index] = '合计';
            sums[3] = showTotal.sandProjectCount
            sums[4] = common.toThousands(showTotal.plandedTotalInvestment, 2, ',')
            sums[5] = common.toThousands(showTotal.monthInvestment, 2, ',')
            sums[6] = common.toThousands(showTotal.totalReverLength, 3, ',')
            sums[7] = common.toThousands(showTotal.monthRiverLength, 3, ',')
            sums[8] = common.toThousands(showTotal.sandTotalYield, 2, ',')
            sums[9] =common.toThousands(showTotal.sandTotalVehicleLoad, 2, ',')
            return;
          }else {
            sums[index] = '合计';
            sums[4] = showTotal.sandProjectCount
            sums[5] = common.toThousands(showTotal.plandedTotalInvestment, 2, ',')
            sums[6] = common.toThousands(showTotal.monthInvestment, 2, ',')
            sums[7] = common.toThousands(showTotal.totalReverLength, 3, ',')
            sums[8] = common.toThousands(showTotal.monthRiverLength, 3, ',')
            sums[9] = common.toThousands(showTotal.sandTotalYield, 2, ',')
            sums[10] =common.toThousands(showTotal.sandTotalVehicleLoad, 2, ',')
            return;
          }
        }
      })
      return sums;
    },
    /** 查询砂石量统计-采砂报表列表 */
    reload(restart) {
      this.$refs.riverSandReportTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.riverSandReport = {
        id: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        yearMonth: '',
        plandedTotalInvestment: null ,
        monthInvestment: null ,
        monthRiverLength: null ,
        status: '',
        reportUserId: '',
        reportTime: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
       if(this.permission){
         this.queryParams.startMonth =  common.formatDate(new Date(), "yyyy-01")
         this.queryParams.endMonth =  common.formatDate(new Date(), "yyyy-MM")
         this.createMonth = [common.formatDate(new Date(), "yyyy-01"), common.formatDate(new Date(), "yyyy-MM")]
       }
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.riverSandReportTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采砂报表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getRiverSandReport(id).then(r => {
        this.riverSandReport = r.riverSandReport;
        this.open = true;
        this.title = "修改采砂报表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          const savedExpandedRows = [...this.expandedRows];
          if (this.riverSandReport.id) {
            updateRiverSandReport(this.riverSandReport).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
              this.expandedRows = savedExpandedRows;
            })
          } else {
            addRiverSandReport(this.riverSandReport).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
              this.expandedRows = savedExpandedRows;
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delRiverSandReport(row.id);
        }else{
          return delRiverSandReportBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExport(){
      this.download('/reports/riverSandReport/exportSandReport', {
        ...this.queryParams
      }, `砂石统计量_采砂_${new Date().getTime()}.xlsx`, 'application/json');
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.riverSandReportTable.changeTableHeight();
  },
};
</script>

<style lang="scss" scoped>
.rowStyle {
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  overflow: hidden !important;
}
::v-deep .isleft .cell {
  display: flex !important;
}
::v-deep .el-table__placeholder {
  width: 0 !important;
  padding-left: 20px !important;
}
::v-deep .islabel .cell {
  display: flex !important;
  justify-content: center!important;
}
</style>


