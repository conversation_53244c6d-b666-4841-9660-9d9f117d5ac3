<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      @submit.native.prevent
    >
      <el-form-item label="bean名称" prop="beanName">
        <el-input
          v-model="queryParams.beanName"
          placeholder="请输入bean名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sys:schedule:save']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sys:schedule:update']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="batchDelete"
          v-hasPermi="['sys:schedule:delete']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-refresh-right"
          size="mini"
          :disabled="multiple"
          @click="handleResume"
          v-hasPermi="['sys:schedule:resume']"
        >恢复
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-video-pause"
          size="mini"
          :disabled="multiple"
          @click="handlePause"
          v-hasPermi="['sys:schedule:pause']"
        >暂停
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-video-play"
          size="mini"
          :disabled="multiple"
          @click="handleRun"
          v-hasPermi="['sys:schedule:run']"
        >立即执行
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-s-operation"
          size="mini"
          @click="handleJobLog"
          v-hasPermi="['sys:schedule:log']"
        >日志列表
        </el-button>
      </el-col>
      <right-toolbar
        class="rightToolbar"
        :search.sync="search"
        :showSearch.sync="showSearch"
        @queryTable="getList">
      </right-toolbar>
    </el-row>
    <!-- table表格 -->
    <my-table url="sys/schedule/list" ref="scheduleTable" row-key="jobId" @my-selection-change="handleSelectionChange">
      <el-table-column label="任务ID" prop="jobId" align="center" :show-overflow-tooltip="true" min-width="160"/>
      <el-table-column label="bean名称" prop="beanName" align="center" min-width="120"/>
      <el-table-column label="方法名称" align="center" prop="methodName" min-width="120"/>
      <el-table-column label="参数" align="center" prop="params" min-width="120"/>
      <el-table-column label="cron表达式" align="center" prop="cronExpression" min-width="120"/>
      <el-table-column label="备注" align="center" prop="remark" min-width="120"/>
      <el-table-column label="状态" align="center" prop="status" min-width="100">
        <template scope="scope">
          <span v-if="scope.row.status===0" class="table-label label-success">正常</span>
          <span v-if="scope.row.status===1" class="table-label label-danger">暂停</span>
        </template>
      </el-table-column>
    </my-table>
    <!-- 添加或修改定时任务对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="bean名称" prop="beanName">
          <el-input v-model="form.beanName" placeholder="spring bean名称，如：testTask"/>
        </el-form-item>
        <el-form-item label="方法名称" prop="methodName">
          <el-input v-model="form.methodName" placeholder="方法名称"/>
        </el-form-item>
        <el-form-item label="参数" prop="params">
          <el-input v-model="form.params" placeholder="参数"/>
        </el-form-item>
        <el-form-item label="cron表达式" prop="cronExpression">
          <el-input v-model="form.cronExpression" placeholder="请输入cron执行表达式">
            <template slot="append">
              <el-button type="primary" @click="handleShowCron">
                生成表达式
                <i class="el-icon-time el-icon--right"></i>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
    <my-dialog title="Cron表达式生成器" :visible.sync="openCron" append-to-body destroy-on-close class="scrollbar">
      <crontab @hide="openCron=false" @fill="crontabFill" :expression="expression"></crontab>
    </my-dialog>
  </div>
</template>
<script>
import { addTasks, delTasks, getTasks, pauseTasks, resumeTasks, runTasks, updateTasks } from '@/api/job/schedule'
import Crontab from '@/components/Crontab'
export default {
  name: 'Schedule',
  components: { Crontab },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      search: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示Cron表达式弹出层
      openCron: false,
      // 传入的表达式
      expression: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        beanName: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        beanName: [{ required: true, message: 'bean名称不能为空', trigger: 'blur' }],
        methodName: [{ required: true, message: '方法名称不能为空', trigger: 'blur' }],
        cronExpression: [{ required: true, message: 'cron表达式不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    /** 查询定时任务列表 */
    getList(restart) {
      this.$refs.scheduleTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple=true;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList(true)
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange() {
      this.ids = this.$refs.scheduleTable.getSelectRowKeys()
      this.single = this.ids.length !== 1
      this.multiple = !this.ids.length
    },
    /** cron表达式按钮操作 */
    handleShowCron() {
      this.expression = this.form.cronExpression
      this.openCron = true
    },
    /** 确定后回传值 */
    crontabFill(value) {
      this.form.cronExpression = value
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加定时任务'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const jobId = row.jobId || this.ids
      getTasks(jobId).then(response => {
        this.form = response.schedule
        this.open = true
        this.title = '修改定时任务'
      })
    },
    /** 删除按钮操作 */
    batchDelete() {
      const jobIds = this.$refs.scheduleTable.getSelectRowKeys()
      this.$modal.confirm('确定要删除选中的记录？').then(() => {
        this.$modal.loading('加载中')
        return delTasks(jobIds)
      }).then(() => {
        this.$modal.closeLoading();
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 暂停按钮操作 */
    handlePause() {
      const jobIds = this.$refs.scheduleTable.getSelectRowKeys()
      this.$modal.confirm('确定要暂停选中的记录？').then(() => {
        this.$modal.loading('加载中')
        return pauseTasks(jobIds)
      }).then(() => {
        this.$modal.closeLoading();
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 恢复按钮操作 */
    handleResume() {
      const jobIds = this.$refs.scheduleTable.getSelectRowKeys()
      this.$modal.confirm('确定要恢复选中的记录？').then(() => {
        this.$modal.loading('加载中')
        return resumeTasks(jobIds)
      }).then(() => {
        this.$modal.closeLoading();
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 立即执行按钮操作 */
    handleRun() {
      const jobIds = this.$refs.scheduleTable.getSelectRowKeys()
      this.$modal.confirm('确定要立即执行选中的记录？').then(() => {
        this.$modal.loading('加载中')
        return runTasks(jobIds)
      }).then(() => {
        this.$modal.closeLoading();
        this.getList()
        this.$modal.msgSuccess('操作成功')
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 任务日志列表查询 */
    handleJobLog() {
      this.$router.push({ path: '/job/schedule-log/index' })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中')
          if (this.form.jobId !== undefined) {
            updateTasks(this.form).then(response => {
              this.$modal.closeLoading()
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            }).catch(this.$modal.closeLoading);
          } else {
            addTasks(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        jobId: undefined,
        beanName: undefined,
        methodName: undefined,
        params: undefined,
        cronExpression: undefined,
        remark: undefined
      }
      this.resetForm('form')
    }
  }
}
</script>
<style>

</style>
