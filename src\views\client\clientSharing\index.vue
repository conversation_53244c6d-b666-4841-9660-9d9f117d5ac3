<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="项目名称" prop="projectName" >
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="共享项目名称" prop="sharingProjectName" label-width="100px">
        <el-input
          v-model.trim="queryParams.sharingProjectName"
          placeholder="请输入共享项目名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['client:clientSharing:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['client:clientSharing:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['client:clientSharing:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/client/clientSharing/page" ref="clientSharingTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column
        header-align="center"
        align="left"
        label="项目名称"
        min-width="80"
        prop="projectName"
        sortable="custom"
        column-key="client.PROJECT_NAME">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="left"
        label="标段名称"
        min-width="80"
        prop="sectionName"
        sortable="custom"
        column-key="client.SECTION_NAME">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="left"
        label="磅站名称"
        min-width="80"
        prop="stationName"
        sortable="custom"
        column-key="client.STATION_NAME">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="left"
        label="共享项目名称"
        min-width="80"
        prop="sharingProjectName"
        sortable="custom"
        column-key="clientSharing.SHARING_PROJECT_NAME"></el-table-column>
      <el-table-column
        header-align="center"
        align="left"
        label="共享标段名称"
        min-width="80"
        prop="sharingSectionName"
        sortable="custom"
        column-key="clientSharing.SHARING_SECTION_NAME"></el-table-column>
      <el-table-column
        header-align="center"
        align="left"
        label="共享磅站名称"
        min-width="80"
        prop="sharingStationName"
        sortable="custom"
        column-key="clientSharing.SHARING_STATION_NAME"></el-table-column>
      <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['client:clientSharing:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['client:clientSharing:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改客户端共享信息表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="clientSharing"  label-width="80px">
        <el-row>
          <el-col :span="12">
            <my-form-item label-width="120px"  label="客户端" ref="clientId" prop="clientId" :rules="[{notNull:true,message:'请选择客户端',trigger:['blur','change']}]">
              <my-select
                v-model="clientSharing.clientId"
                :options="clientArrayList"
                placeholder="请选择客户端"
                @change="clientSelectChange"
               >
                <el-option v-for="item in clientArrayList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item
              label-width="120px"
              label="共享项目名称"
              ref="sharingProjectId"
              prop="sharingProjectId"
              :rules="[{notNull:true,message:'请选择共享项目', trigger:['blur','change']}]"
            >
              <my-select
                v-model="clientSharing.sharingProjectId"
                :options="projectList"
                placeholder="请选择共享项目"
                @change="projectSelectChange"
              >
                <el-option v-for="item in projectList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item
              label-width="120px"
              label="共享磅站名称"
              ref="sharingStationId"
              prop="sharingStationId"
              :rules="[{notNull:true,message:'请选择共享磅站', trigger:['blur','change'] }]"
            >
              <my-select
                v-model="clientSharing.sharingStationId"
                :options="stationList"
                placeholder="请选择共享磅站"
                @change="stationSelectChange"
              ></my-select>
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import {
  getClientSharing,
  delClientSharing,
  delClientSharingBatch,
  addClientSharing,
  updateClientSharing,
  getProjectName
} from '@/api/client/clientSharing'
import { listClient, listStationByProjectId } from '@/api/client/client'

export default {
  name: "ClientSharing",
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        projectName:'',
        sharingProjectName:''
      },
      // 表单参数
      clientSharing: {},
      clientList:[],
      projectList:[],
      stationList:[]
    };
  },
  mounted() {
    listClient().then(res=>{
      this.clientList = res.list
    });
  },
  computed: {
    clientArrayList: function() {
      if (this.clientList && this.clientList.length > 0) {
        return this.clientList.map(item => {
          if(item.sectionName){
            return { 'name': item.projectName+"("+item.sectionName+")" + '_' + item.stationName, 'value': item.id,'projectId':item.projectId}
          }else {
            return { 'name': item.projectName + '_' + item.stationName, 'value': item.id ,'projectId':item.projectId}
          }
        });
      }
    }
  },
  methods: {
    /** 查询客户端共享信息表列表 */
    reload(restart) {
      this.$refs.clientSharingTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.clientSharing = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        clientId: '',
        projectId: '',
        sharingProjectId: '',
        sharingProjectName: '',
        sharingSectionName: '',
        sharingStationId: '',
        sharingStationName: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.clientSharingTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户端共享信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getClientSharing(id).then(r => {
        this.clientSharing = r.clientSharing;
        this.open = true;
        this.title = "修改客户端共享信息";
      });
    },
    clientSelectChange(val){
      this.projectList = [];
      this.clientSharing.sharingProjectId = '';
      if (val) {
        console.log(111)
        const obj = this.clientArrayList.find(item => {
          return item.value === val;
        });
        this.clientSharing.projectId = obj.projectId ? obj.projectId : '';
        getProjectName(obj.value,obj.projectId).then(res=>{
          if (res.list && res.list.length > 0) {
            this.projectList = res.list.map(item => {
              if(item.sectionName){
                return { 'name': item.name, 'value': item.deptId, 'sectionName':item.sectionName}
              }else {
                return { 'name': item.name, 'value': item.deptId }
              }
            });
          }
        })
      }else {
        this.clientSharing.sharingProjectId = '';
      }
    },
    projectSelectChange(val){
      this.stationList = [];
      this.clientSharing.sharingStationId = '';
      this.clientSharing.sharingStationName = '';
      if (val) {
        const obj = this.projectList.find(item => {
          return item.value === val;
        });
        this.clientSharing.sharingProjectName = obj.name ? obj.name : '';
        this.clientSharing.sharingSectionName = obj.sectionName? obj.sectionName : '';
        listStationByProjectId(val).then(res => {
          if (res.list && res.list.length > 0) {
            this.stationList = res.list.map(item => {
              return {'name': item.name, 'value': item.id}
            });
          }
        });
      } else {
        this.clientSharing.sharingProjectName = '';
      }
    },
    stationSelectChange(val){
      if (val) {
        const obj = this.stationList.find(item => {
          return item.value === val;
        });
        this.clientSharing.sharingStationName = obj.name ? obj.name : '';
      } else {
        this.clientSharing.sharingStationName= '';
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.clientSharing.id) {
            updateClientSharing(this.clientSharing).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addClientSharing(this.clientSharing).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delClientSharing(row.id);
        }else{
          return delClientSharingBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.clientSharingTable.changeTableHeight();
  },
};
</script>
