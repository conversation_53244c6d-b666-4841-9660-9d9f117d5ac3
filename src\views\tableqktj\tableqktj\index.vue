<template>
  <div class="app-container" :gutter="10">
    <!--  模糊搜索部分-->
    <template>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="全年情况统计表" name="two"></el-tab-pane>
        <el-tab-pane label="打击河道非法采砂情况统计表" name="one"></el-tab-pane>
      </el-tabs>
    </template>
    <div v-show="activeName=='one'">
      <el-row>
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="月报填报时间">
            <!-- <el-date-picker class="custom-width" v-model="yearTime" type="month" placeholder="请选择"
              value-format="yyyy-MM" >
            </el-date-picker> -->
            <el-date-picker value-format="yyyy-MM" format="yyyy 年 MM 月" v-model="timeData" type="monthrange"
              range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button @click="onReset">重置</el-button>
            <el-button type="warning" icon="el-icon-download" size="" @click="handleExport">导出
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <!--  表格部分-->
      <el-row>
        <el-table :data="tableData" :expand-row-keys="expandedKeys" :row-key="generateRowKey" border style="width: 100%"
          size="mini">
          <el-table-column label="打击河道非法采砂情况统计表" width="180" align="center">
            <el-table-column label="市" align="center" min-width="80" prop="areaName">
              <template slot-scope="scope">
                {{!scope.row.areaName?'河北省':scope.row.areaName}}
              </template>
            </el-table-column>

            <!-- 原市级列定义（从第一个 el-table-column 开始） -->
            <el-table-column prop="jointLawEnforcement" label="联合执法(次)" align="center" min-width="70">
            </el-table-column>
            <el-table-column prop="name" label="出动执法力量(人次)" min-width="180" align="center">
              <el-table-column prop="waterEnforcementPerson" label="水行政执法人员" min-width="70" align="center">
              </el-table-column>
              <el-table-column prop="policePerson" label="警务人员" min-width="80" align="center"></el-table-column>
              <el-table-column prop="otherPerson" min-width="60" align="center">
                <template slot="header">
                  <div>
                    <el-tooltip class="item" effect="dark" content="自然资源/交通运输" placement="top">
                      <span>其它行政执法人员</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table-column>

            <el-table-column prop="publicityMaterials" label="印发宣传材料(册)" min-width="75" align="center">
            </el-table-column>


            <el-table-column prop="mediaReports" label="媒体宣传报道(次)" min-width="75" align="center"></el-table-column>
            <el-table-column prop="illegalSandMining" label="查处非法采砂(处)" min-width="75" align="center">
            </el-table-column>
            <el-table-column prop="illegalExtractionVolume" label="盗采方量(m³)" min-width="60" align="center">
            </el-table-column>
            <el-table-column prop="confiscationIllegalGains" label="没收违法所得(万元)" min-width="70">
            <template slot-scope="scope">
              {{scope.row.confiscationIllegalGains?scope.row.confiscationIllegalGains.toFixed(4):''}}
            </template>
            </el-table-column>
            <el-table-column prop="fineAmount" label="罚款(万元)" min-width="60" align="center">
              <template slot-scope="scope">
                {{scope.row.fineAmount?scope.row.fineAmount.toFixed(4):''}}
              </template>
            </el-table-column>
            <el-table-column label="行政立案" align="center">
              <el-table-column prop="administrativeCase" label="数量(起)" min-width="65" align="center"> </el-table-column>
              <el-table-column prop="administrativeCasePerson" label="涉案人数" min-width="50" align="center">
              </el-table-column>
            </el-table-column>
            <el-table-column prop="involvedInCrimeAndEvil" label="移交涉黑涉恶线索（条）" min-width="75" align="center">
            </el-table-column>
            <el-table-column width="180" label="移送公安机关案件" align="center">
              <el-table-column prop="transferredToPolice" label="数量(起)" min-width="60" align="center"></el-table-column>
              <el-table-column prop="transferredToPolicePerson" label="涉案人数" min-width="50" align="center">
              </el-table-column>
              <el-table-column prop="criminalFiling" label="刑事立案数" min-width="70" align="center"></el-table-column>
            </el-table-column>
            <el-table-column label="非法采砂问题线索来源(个)" min-width="180" align="center">
              <el-table-column prop="sourcePolice" label="日常巡查" min-width="75" align="center"></el-table-column>
              <el-table-column prop="sourceNaturalResources" label="群众举报" min-width="65" align="center">
              </el-table-column>
              <el-table-column prop="sourceTransport" label="其它部门移交" min-width="65" align="center"></el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-row>
      <div class="li-tips">
        注：行政立案的涉案人数和案件数量是按月报填报的案件数来统计的，并非查询时段发生的实际案件数，例如：今年1月填报了去年12月的案件，那就是今年1月填报了1起案件，但今年1月实际发生案件数是0件
      </div>
    </div>
    <div v-show="activeName=='two'">
      <!--  表格部分-->
      <el-row>
        <el-form :inline="true" :model="formInline1" class="demo-form-inline">
          <el-form-item>
            <el-date-picker class="custom-width" v-model="yearTime1" type="year" placeholder="请选择" value-format="yyyy">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <my-area-select v-model="formInline1.areaCode" :external-parent-value="parentAreaCode"
              :external-children-value="childrenAreaCode" :read-only-parent="isReadOnlyParent"
              :read-only-children="isReadOnlyChildren" :is-clear="isClear" style="font-size: medium" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit1">查询</el-button>
            <el-button @click="onReset1">重置</el-button>
            <el-button type="warning" icon="el-icon-download" size="" @click="handleExport1">导出
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData1" border style="width: 100%" size="mini" align="center">
          <el-table-column label="全年采砂情况统计表" width="180" align="center">
            <!-- <el-table-column label="月份" min-width="80" align="center">
              <template slot-scope="scope">
                <div>{{scope.$index+1==13?'总计':scope.$index+1}}
    </div>
</template>
</el-table-column> -->
<el-table-column prop="areaName" label="月份" align="center" min-width="80">
  <template slot-scope="scope">
    <div>{{scope.$index+1==13?'总计':scope.$index+1}}</div>
  </template>
</el-table-column>
<el-table-column prop="jointLawEnforcement" label="联合执法(次)" min-width="70" align="center">
</el-table-column>
<el-table-column label="出动执法力量(人次)" align="center">
  <el-table-column prop="waterEnforcementPerson" label="水行政执法人员" min-width="70" align="center"></el-table-column>
  <el-table-column prop="policePerson" label="警务人员" min-width="80" align="center"></el-table-column>
  <el-table-column prop="otherPerson" min-width="60" align="center">
    <template slot="header">
      <div>
        <el-tooltip class="item" effect="dark" content="自然资源/交通运输" placement="top">
          <span>其它行政执法人员</span>
        </el-tooltip>
      </div>
    </template>
  </el-table-column>
</el-table-column>
<el-table-column prop="publicityMaterials" label="印发宣传材料(册)" min-width="75" align="center">
</el-table-column>


<el-table-column prop="mediaReports" label="媒体宣传报道(次)" min-width="75" align="center"></el-table-column>
<el-table-column prop="illegalSandMining" label="查处非法采砂(处)" min-width="75" align="center">
</el-table-column>
<el-table-column prop="illegalExtractionVolume" label="盗采方量(m³)" min-width="60" align="center">
</el-table-column>
<el-table-column prop="confiscationIllegalGains" label="没收违法所得(万元)" min-width="70">
  <template slot-scope="scope">
    {{scope.row.confiscationIllegalGains?scope.row.confiscationIllegalGains.toFixed(4):''}}
  </template>
          </el-table-column>
<el-table-column prop="fineAmount" label="罚款(万元)" min-width="60" align="center">
  <template slot-scope="scope">
    {{scope.row.fineAmount?scope.row.fineAmount.toFixed(4):''}}
  </template>
</el-table-column>
<el-table-column label="行政立案" align="center">
  <el-table-column prop="administrativeCase" label="数量(起)" min-width="67" align="center"> </el-table-column>
  <el-table-column prop="administrativeCasePerson" label="涉案人数" min-width="75" align="center">
  </el-table-column>
</el-table-column>
<el-table-column prop="involvedInCrimeAndEvil" label="涉黑涉恶线索(条)" min-width="75" align="center">
</el-table-column>
<el-table-column label="移送公安机关案件" align="center">
  <el-table-column prop="transferredToPolice" label="数量(起)" min-width="75" align="center"></el-table-column>
  <el-table-column prop="transferredToPolicePerson" label="涉案人数" min-width="75" align="center">
  </el-table-column>
  <el-table-column prop="criminalFiling" label="刑事立案数" min-width="60" align="center"></el-table-column>
</el-table-column>
<el-table-column label="非法采砂问题线索来源(个)" align="center">
  <el-table-column prop="sourcePolice" label="日常巡查" min-width="50" align="center"></el-table-column>
  <el-table-column prop="sourceNaturalResources" label="群众举报" min-width="50" align="center">
  </el-table-column>
  <el-table-column prop="sourceTransport" label="其它部门" min-width="50" align="center"></el-table-column>
</el-table-column>
<!-- <el-table-column prop="notes" label="备注" width="160" align="center"></el-table-column> -->
</el-table-column>
</el-table>
</el-row>
</div>
</div>
</template>

<script>
  import {
    getTableDataDetails,
    getTableDataDetailsYear,
    getAreaName,
  } from "@/api/tablebbtb/bbtb";
  import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
  export default {
    name: 'HelloWorld',
    components:{
      MyAreaSelect: MyAreaSelect,
    },
    computed: {},
    data() {
      return {
        expandedKeys: [], // 控制展开的行ID
        yearTime: undefined,
        timeData: [],
        yearTime1: undefined,
        activeName: 'two',
        // 区域选择框默认选中
        parentAreaCode: "",
        childrenAreaCode: "",
        parentAreaCode1: "",
        childrenAreaCode1: "",
        isReadOnlyParent: false, // 控制父下拉框是否可编辑
        isReadOnlyChildren: false, // 控制子下拉框是否可编辑
        isReadOnlyParent1: false, // 控制父下拉框是否可编辑
        isReadOnlyChildren1: false, // 控制子下拉框是否可编辑
        isClear: false, // 父选择框是否可清空
        formInline: {
          pageNum: 1,
          pageSize: 10,
          year: '',
          month: ''
        },
        formInline1: {
          pageNum: 1,
          pageSize: 10,
          year: '',
          month: '',
          areaCode:''
        },
        tableData: [],
        tableData1: [],
        zname:''
      };
    },
    created() {
      this.getTableTwo();
    },
    mounted() {

    },
    methods: {
      getArea() {
        getAreaName().then((res) => {
          console.log(res, '我获取的')
          this.zname = res.AREA_NAME;
          this.areaCode = res.AREA_CODE;
          if (res.DEPT_CODE.length == 9) {
            this.parentAreaCode = res.AREA_CODE;
            this.myareaname = res.AREA_NAME;
            this.isReadOnlyParent = true;
          } else {
            this.parentAreaCode = '';
            this.myareaname = '';
            this.isReadOnlyParent = false;
          }
          console.log(res, '获取的')
        });
      },
      generateRowKey(row) {
        const findParentPath = (target, tree, path = '') => {
          for (const node of tree) {
            const currentPath = path ? `${path}_${node.areaName}` : node.areaName;
            if (node === target) return path; // 找到自身时返回父路径
            if (node.children) {
              const result = findParentPath(target, node.children, currentPath);
              if (result) return result; // 向上传递有效路径
            }
          }
          return null;
        };

        const parentPath = findParentPath(row, this.tableData); // 从根数据开始查找
        // console.log(`${parentPath}_${row.areaName}`,'啥玩意')
        return parentPath ? `${parentPath}_${row.areaName}` : row.areaName;
      },
      handleExpand(row, expandedRows) {
        if (row.children) {
          this.expandedKeys = expandedRows ?
            [row.id] :
            []
        }
      },
      // 搜索按钮
      onSubmit() {
        console.log(this.timeData);
        this.formInline.startDate = this.timeData[0];
        this.formInline.endDate = this.timeData[1]
        this.formInline.pageNum = 1;
        this.getTable()
      },
      // 重置按钮
      onReset() {
        this.formInline = {
          pageNum: 1,
          pageSize: 10,
          startDate: '',
          endDate: ''
        }
        this.timeData = [];
        // this.yearTime = null;
        this.getTable()
      },
      // 搜索按钮
      onSubmit1() {
        this.formInline1.year = this.yearTime1.split('-')[0];
        this.formInline1.month = parseInt(this.yearTime1.split('-')[1]).toString();
        this.formInline1.pageNum = 1;
        this.getTableTwo()
      },
      // 重置按钮
      onReset1() {
        this.formInline1 = {
          pageNum: 1,
          pageSize: 10,
          year: '',
          month: '',
          areaCode:''
        }
        this.yearTime1 = null;
         this.parentAreaCode = null;
        this.getTableTwo()
      },
      handleClick(tab, event) {
        this.activeName = tab.name;
        if (tab.name == 'one') {
          this.getTable();
        } else if (tab.name == 'two') {
          this.getTableTwo();
        }
      },
      handleSizeChange(val) {
        this.formInline.pageNum = 1
        this.formInline.pageSize = val
        this.getTable()
      },
      handleCurrentChange(val) {
        this.formInline.pageNum = val
        this.getTable()

      },
      getTable() {
        getTableDataDetails(this.formInline).then((res) => {
          console.log(res,'获取数据1')
          this.tableData = res;
          // this.total = res.total
        });
      },
      getTableTwo() {
        getTableDataDetailsYear(this.formInline1).then((res) => {
          this.tableData1 = res;
          console.log(res,'获取数据2')
          // this.total = res.total
          this.getArea();
        });
      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('statement/exportByAreaName', {
            startDate: this.timeData[0],
            endDate: this.timeData[1]
          }, `${this.timeData[0]?this.timeData[0]+'至':''}${this.timeData[1]?this.timeData[1]:''}打击河道非法采砂情况统计表.xlsx`,
          'application/json')
      },
      /** 导出按钮操作 */
      handleExport1() {
        this.download('statement/exportByYear', {
          year: this.yearTime1
        }, `${this.yearTime1?this.yearTime1+'年':''}全年情况统计表.xlsx`, 'application/json')
      }
    }
  };
</script>

<style scoped>

  /* 基础样式 */
  .li-tips {
    position: relative;
    display: inline-block;
    margin: 10px 0;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #ffc107;
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    /* color: #6c757d; */
    font-size: 14px; color: red; font-weight: 600;
    line-height: 1.6;
  }

  /* 悬浮动画 */
  .li-tips:hover {
    background-color: #fff8e1;
    border-left-color: #ffab00;
  }

  /* 图标增强 */
  .li-tips::before {
    content: "⚠️";
    margin-right: 8px;
    vertical-align: middle;
  }

  h1 {
    color: #42b983;
  }

  ::v-deep .el-table__expand-column .el-table__expand-icon {
    display: none !important;
  }

  ::v-deep .el-table__cell .el-table__indent {
    padding-left: 0px !important;
  }

  ::v-deep .el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    height: 0px !important;
    line-height: 0px;
    /* border: 0 !important; */
    padding: 0;
    margin: 0;
  }

  .custom-width {
    width: 150px;
  }

  .el-table__expand-icon>.el-icon {
    display: none !important;
  }
</style>
