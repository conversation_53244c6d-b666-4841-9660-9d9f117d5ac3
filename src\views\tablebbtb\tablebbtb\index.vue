<template>
  <div class="app-container" :gutter="10">
    <!--  模糊搜索部分-->
    <el-row>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item>
          <el-date-picker value-format="yyyy-MM" format="yyyy 年 MM 月" v-model="timeData" type="monthrange"
            range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份" :clearable="false">
          </el-date-picker>
        </el-form-item>
        <!-- {{formInline.areaCode}} -->
        <el-form-item v-if="state != 0">
          <my-area-select v-model="formInline.areaCode" :external-parent-value="parentAreaCode"
            :external-children-value="childrenAreaCode" :read-only-parent="isReadOnlyParent"
            :read-only-children="isReadOnlyChildren" :is-clear="isClear" style="font-size: medium" />
          <!-- <new-select v-model="formInline.areaCode" :externalParentValue="parentAreaCode"
            :externalChildrenValue="childrenAreaCode" :read-only-parent="isReadOnlyParent"
            :read-only-children="isReadOnlyChildren" :is-clear="isClear" @parentNameLi="fclick"
            @childrenNameLi="zclick" :state="state" :disabled="isreadonly" style="font-size: medium" /> -->
        </el-form-item>
        <el-form-item>
          <el-select v-model="formInline.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in allStatus" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="formInline.fillStatus" placeholder="请选择填报状态" clearable>
            <el-option v-for="item in fillStatusList" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <!-- 新增按钮部分 -->
    <el-row style="margin-bottom: 10px">
      <el-button type="primary" @click="handleAdd">新建月报</el-button>
      <el-button type="primary" @click="handleDelete">批量删除</el-button>
    </el-row>

    <!--  表格部分-->
    <el-row>
      <el-table ref="innerTable" :data="tableData" @selection-change="handleSelectionChange" border style="width: 100%"
        size="mini" height="420px">
        <el-table-column type="selection" width="40"></el-table-column>
        <!-- <el-table-column label="报告时间" min-width="180" align="center" prop="newMonth" sortable>
        </el-table-column> -->
        <!--   <el-table-column label="提交部门" min-width="180" align="center" sortable>
        </el-table-column> -->
        <!--  <el-table-column label="报告类型" min-width="180" align="center" prop="issueType" sortable>
        </el-table-column> -->
        <el-table-column label="所属区县" min-width="180" align="center" prop="areaName">
        </el-table-column>
        <el-table-column label="所属部门" min-width="180" align="center" prop="deptName">
        </el-table-column>
        <el-table-column label="审核状态" min-width="180" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="reType(scope.row.status, scope.row.countyStatus, scope.row.cityStatus, scope.row.deptId)"
              disable-transitions>{{ reTypeName(scope.row.status, scope.row.countyStatus, scope.row.cityStatus,
                scope.row.deptId) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否填报" min-width="180" align="center" prop="fillStatus">
          <template slot-scope="scope">
          <el-tag :type="reTb(scope.row.fillStatus)"
            disable-transitions>{{scope.row.fillStatus}}</el-tag>
            </template>
        </el-table-column>
        <el-table-column label="报告时间" min-width="180" align="center" prop="fillTime">
        </el-table-column>
        <el-table-column label="操作" min-width="180" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleView(scope.row)">详情</el-button>
            <!-- <div class="myclass" v-if="deptcode==scope.row.deptCode"> -->
            <el-button type="text" @click="modifyView(scope.row)"
              v-if="deptcode == scope.row.deptCode && scope.row.status == 2">修改</el-button>
            <el-button type="text" @click="modifyView(scope.row)"
              v-if="deptcode == scope.row.deptCode && scope.row.status == 3">继续编辑</el-button>
            <el-button type="text" @click="handelDelete(scope.row.id)"
              v-if="deptcode == scope.row.deptCode && scope.row.status && (scope.row.status != 1 && scope.row.status != '1')">删除</el-button>
            <!-- </div> -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
        style="text-align: right;margin-top:5px" :current-page="formInline.pageNum" :page-sizes="[10, 20, 50, 100]"
        :page-size="10" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </el-row>
    <my-dialog :title="title" :visible.sync="open" width="1040px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="85px">
        <div class="myscroll">
          <el-card class="box-card">
            <div class="mycustom">
              <el-form-item label="区域名称" label-width="100px">
                <!-- <new-select v-model="form.areaCode" :externalParentValue="parentAreaCode1"
                :externalChildrenValue="childrenAreaCode1" :read-only-parent="isReadOnlyParent1"
                :read-only-children="isReadOnlyChildren1" :is-clear="isClear" @parentNameLi="fclick"
                @childrenNameLi="zclick" :state="state" :disabled="isreadonly" style="font-size: medium" /> -->
                <el-input class="custom-width" v-model="zname" placeholder="请输入内容" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="所属部门" prop="deptname">
                <el-input class="custom-width" v-model="deptname" placeholder="请输入部门" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="报告时间" prop="filingTimeAll" label-width="100px">
                <el-date-picker class="custom-width" v-model="form.filingTimeAll" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly">
                </el-date-picker>
              </el-form-item>
            </div>
          </el-card>
          <el-card>
            <div class="mycustom">
              <el-form-item label="出动水行政执法人员(人次)" lable-position="left" prop="waterEnforcementPerson"
                label-width="150px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.waterEnforcementPerson" placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="出动警务人员(人次)" lable-position="left" prop="policePerson" label-width="190px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.policePerson"
                  placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="出动其他行政执法人员(人次)" lable-position="left" prop="otherPerson" label-width="220px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.otherPerson"
                  placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="联合执法次数(次)" lable-position="left" prop="jointLawEnforcement" label-width="150px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.jointLawEnforcement" placeholder="请输入次数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="查处非法采砂(处)" lable-position="left" prop="illegalSandMining" label-width="190px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.illegalSandMining" placeholder="请输入数量" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="移交涉黑涉恶线索(条)" lable-position="left" prop="transferredToPolice" label-width="220px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.transferredToPolice" placeholder="请输入条数" :readonly="isreadonly"></el-input>
              </el-form-item>

            </div>
          </el-card>
          <!-- <el-card class="isLine">
            <div class="mycustom">
              <div class="myTables">
           外非法采砂线索来源
              </div>
            </div>
            <div class="mycustom">
              <el-form-item label="自然资源部门（个）" lable-position="left" prop="sourceNaturalResources" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.sourceNaturalResources" placeholder="请输入个数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="交通运输部门（个）" lable-position="left" prop="sourceTransport" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.sourceTransport"
                  placeholder="请输入个数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="公安部门（个）" lable-position="left" prop="sourcePolice" label-width="120px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.sourcePolice"
                  placeholder="请输入个数" :readonly="isreadonly"></el-input>
              </el-form-item>
            </div>
          </el-card> -->
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="印发宣传材料(册)" prop="publicityMaterials" lable-position="left" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.publicityMaterials" placeholder="请输入册数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="媒体宣传报道(次)" prop="mediaReports" lable-position="left" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.mediaReports"
                  placeholder="请输入次数" :readonly="isreadonly"></el-input>
              </el-form-item>
            </div>
          </el-card>
          <div class="isLine">
            <div class="mycustom">
              <el-button type="primary" icon="el-icon-circle-plus-outline" size="medium" v-if="zilei == true"
                @click="handleAddSon">新增案件</el-button>
            </div>
            <div class="mycustom">
              <el-table ref="innerTable" :data="form.statementDos" @selection-change="handleSelectionChange" border
                style="width: 100%" size="mini">
                <el-table-column type="index" width="50" label="序号"></el-table-column>
                <el-table-column label="立案时间" min-width="80" align="center" prop="filingTime">
                </el-table-column>
                <el-table-column label="报告类型" min-width="80" align="center" prop="issueType">
                </el-table-column>
                <el-table-column label="所在河流" min-width="80" align="center" prop="riverName">
                </el-table-column>
                <el-table-column label="整治单位" min-width="80" align="center" prop="responsibleUnit">
                </el-table-column>
                <el-table-column label="整治责任人" min-width="80" align="center" prop="chargePerson">
                </el-table-column>
                <el-table-column label="行政立案" min-width="80" align="center" prop="administrativeCase">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.administrativeCase == 0 ? 'warning' : 'success'" disable-transitions>{{
                      scope.row.administrativeCase == 0 ? '否' : '是' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="是否结案" min-width="80" align="center" prop="caseClosed">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.caseClosed == 0 ? 'warning' : 'success'" disable-transitions>{{
                      scope.row.caseClosed == 0 ? '否' : '是' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="180" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleView1(scope.row)">详情</el-button>
                    <el-button type="text" @click="modifyView1(scope.row, scope.$index)"
                      v-if="zilei == true">修改</el-button>
                    <el-button type="text" @click="handelDelete1(scope.row, scope.$index)"
                      v-if="zilei == true">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <!-- <div class="mycustom">
            <el-form-item label="整治情况" prop="remediationStatus">
              <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入内容"
                v-model="form.remediationStatus" :readonly="isreadonly">
              </el-input>
            </el-form-item>
          </div> -->
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="warning" @click="submitForm(3)" v-if="isreadonly == false">暂 存</el-button>
        <el-button type="primary" @click="submitForm(0)" v-if="isreadonly == false">提 交</el-button>
        <el-button @click="cancel">{{ isreadonly == false ? '取 消' : '关 闭' }}</el-button>
      </div>
    </my-dialog>
    <my-dialog :title="title1" :visible.sync="open1" width="1150px" append-to-body>
      <el-form ref="form1" :model="form1" :rules="rules1" label-width="85px">
        <div class="myscroll">
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="问题类型" prop="issueType" label-width="100px">
                <el-select class="custom-width" v-model="form1.issueType" placeholder="请选择类型" :disabled="isreadonly1"
                  clearable>
                  <el-option v-for="item in bbtbProblum" :key="item.value" :label="item.name" :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="案件办理单位" prop="responsiblePerson" label-width="104px">
                <el-input class="custom-width" v-model="form1.responsiblePerson" placeholder="请输入单位名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="所在河流" prop="riverName" label-width="100px">
                <el-input class="custom-width" v-model="form1.riverName" placeholder="请输入河流名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="线索来源" prop="sourceClues" label-width="100px">
                <el-select class="custom-width" v-model="form1.sourceClues" placeholder="请选择来源" :disabled="isreadonly1"
                  clearable>
                  <el-option v-for="item in sourceDate" :key="item.value" :label="item.name" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="具体位置" prop="village" label-width="100px">
                <el-input class="custom-width" v-model="form1.village" placeholder="请输入具体位置" :readonly="isreadonly1"
                  maxlength="100"></el-input>
              </el-form-item>
              <el-form-item label-width="0px">
                <el-select class="custom-width-select" v-model="typeClass" @change="setType" placeholder=""
                  :disabled="isreadonly1">
                  <el-option v-for="item in typeName" :key="item.value" :label="item.name" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <Dufenmiao v-model="coordinates" @input="getValue" :mydisabled="isreadonly1" v-show="typeClass == 0">
              </Dufenmiao>
              <el-form-item label="经度" prop="longitude" label-width="50px" v-show="typeClass == 1">
                <el-input class="custom-width" v-model="form1.longitude"
                  @input="form1.longitude = $options.filters.number(form1.longitude)" placeholder="请输入经度"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="纬度" prop="latitude" label-width="50px" v-show="typeClass == 1">
                <el-input class="custom-width" v-model="form1.latitude"
                  @input="form1.latitude = $options.filters.number(form1.latitude)" placeholder="请输入纬度"
                  :readonly="isreadonly1"></el-input>
                <!-- <i class="el-icon-location-outline mapUp" @click="mapClick"></i> -->
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="面积(㎡)" prop="sandPitArea" label-width="100px">
                <el-input class="custom-width" @input="form1.sandPitArea = $options.filters.number(form1.sandPitArea)"
                  v-model="form1.sandPitArea" placeholder="请输入面积" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="深度(m)" prop="sandPitDepth" label-width="104px">
                <el-input class="custom-width" v-model="form1.sandPitDepth"
                  @input="form1.sandPitDepth = $options.filters.number(form1.sandPitDepth)" placeholder="请输入深度"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="采砂量(m³)" prop="illegalExtractionVolume" label-width="100px">
                <el-input class="custom-width" v-model="form1.illegalExtractionVolume"
                  @input="form1.illegalExtractionVolume = $options.filters.number(form1.illegalExtractionVolume)"
                  placeholder="请输入采砂量" :readonly="isreadonly1" @blur="setTab"></el-input>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="illegalExtractionVolumeType">
              <el-form-item label="是否启动生态损害赔偿" prop="whetherCompensation" label-width="160px">
                <el-switch v-model="form1.whetherCompensation" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.whetherCompensation == false">
              <el-form-item label="不启动原因" prop="notCause" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入原因并说明"
                  v-model="form1.notCause" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="整治单位" prop="responsibleUnit" label-width="100px">
                <el-input class="custom-width" v-model="form1.responsibleUnit" placeholder="请输入单位名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="整治责任人" prop="chargePerson" label-width="104px">
                <el-input class="custom-width" v-model="form1.chargePerson" placeholder="请输入责任人名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="完成时限" prop="completionDeadline" label-width="100px">
                <el-date-picker class="custom-width" v-model="form1.completionDeadline" type="date" placeholder="请选择日期"
                  value-format="yyyy-MM-dd" :disabled="isreadonly1">
                </el-date-picker>
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="整治措施" prop="regulationMeasure" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="输入具体措施"
                  v-model="form1.regulationMeasure" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="行政立案" prop="administrativeCase">
                <el-switch v-model="form1.administrativeCase" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="true">
                </el-switch>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.administrativeCase == true">
              <el-form-item label="涉案人数" prop="suspectsCount" label-width="100px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form1.suspectsCount"
                  placeholder="请输入人数" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="没收违法所得(万元)" prop="confiscationIllegalGains" label-width="146px">
                <el-input class="custom-width" v-model="form1.confiscationIllegalGains" @blur="validateUnit1"
                  @input="form1.confiscationIllegalGains = $options.filters.number(form1.confiscationIllegalGains)"
                  placeholder="请输入金额" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="罚款(万元)" prop="fineAmount" label-width="90px">
                <el-input class="custom-width" v-model="form1.fineAmount" @blur="validateUnit"
                  @input="form1.fineAmount = $options.filters.number(form1.fineAmount)" placeholder="请输入金额"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="立案时间" prop="filingTime" label-width="90px">
                <el-date-picker class="custom-width" v-model="form1.filingTime" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly1">
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.administrativeCase == true">
              <el-form-item label="案件简要情况" prop="administrativeCaseDetails" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="描述时间 地点 行为人 行为 违反法律法规等基本案情"
                  v-model="form1.administrativeCaseDetails" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="是否结案" prop="caseClosed" label-width="100px">
                <el-switch v-model="form1.caseClosed" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1" @change="mychangge">
                </el-switch>
              </el-form-item>
              <el-form-item label="结案时间" prop="closingTime" label-width="100px" v-show="form1.caseClosed == true">
                <el-date-picker class="custom-width" v-model="form1.closingTime" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly1">
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.caseClosed == true">
              <el-form-item label="结案信息" prop="closingInformation" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="案件办理情况"
                  v-model="form1.closingInformation" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="移送公安机关" prop="transferredToPolice" label-width="110px">
                <el-switch v-model="form1.transferredToPolice" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="涉黑涉恶" prop="involvedInOrganizedCrime">
                <el-switch v-model="form1.involvedInOrganizedCrime" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="制定整治措施" prop="remediationMeasuresDefined" label-width="110px">
                <el-switch v-model="form1.remediationMeasuresDefined" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <!-- <el-form-item label="整改完结" prop="rectificationCompleted" label-width="80px">
              <el-switch v-model="form1.rectificationCompleted" class="tableScopeSwitch" active-text="是"
                inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
              </el-switch>
            </el-form-item> -->
              <el-form-item label="是否追责" prop="liability" label-width="80px">
                <el-switch v-model="form1.liability" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="追责人数" prop="liabilityNumberPersons" label-width="80px" v-show="form1.liability">
                <el-input-number v-model="form1.liabilityNumberPersons" @change="handleChange" :min="0" :max="10"
                  label="描述文字" :disabled="isreadonly1"></el-input-number>
              </el-form-item>
            </div>
            <div class="mycustomName" v-show="form1.liability">
              <div v-for="(item, index) in form1.reportPersonList" :key="index" class="autoNames">
                <el-form-item label="姓名" :prop="'reportPersonList.' + index + '.liabilityName'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入姓名" v-model="item.liabilityName"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <el-form-item label="职务" :prop="'reportPersonList.' + index + '.liabilityPost'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入职务" v-model="item.liabilityPost"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <el-form-item label="追责问责形式" :prop="'reportPersonList.' + index + '.accountability'"
                  label-width="120px">
                  <el-input class="custom-width" placeholder="请输入问责形式" v-model="item.accountability"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <i class="el-icon-delete" @click="deleteItem(item, index)"></i>
                </el-form-item> -->
              </div>
            </div>
          </el-card>
          <!-- <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="联合执法" prop="jointLawEnforcement">
                <el-switch v-model="form1.jointLawEnforcement" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.jointLawEnforcement==true">
              <el-form-item label="水行政执法人员(人)" prop="waterEnforcementPerson" label-width="160px">
                <el-input class="custom-width" v-model="form1.waterEnforcementPerson" placeholder="请输入内容"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="乡镇行政执法人员(人)" prop="townshipPerson" label-width="160px">
                <el-input class="custom-width" v-model="form1.townshipPerson" placeholder="请输入内容"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="警务人员(人)" prop="policePerson" label-width="120px">
                <el-input class="custom-width" v-model="form1.policePerson" placeholder="请输入内容"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form1.jointLawEnforcement==true">
              <el-form-item label="查处非法采砂(处)" prop="illegalSandMining" label-width="160px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form1.illegalSandMining" placeholder="请输入内容" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="其它行政执法人员(自然资源/交通运输)" prop="otherPerson" label-width="160px">
                <el-input class="custom-width" v-model="form1.otherPerson" placeholder="请输入内容"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="刑事立案数(起)" prop="criminalFiling" label-width="120px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form1.criminalFiling"
                  placeholder="请输入内容" :readonly="isreadonly1"></el-input>
              </el-form-item>
            </div>
          </el-card> -->
          <el-card class="isLine">
            <!-- <div class="mycustom">
              <el-form-item label="新增月份" prop="newMonth" label-width="100px">
                <el-date-picker class="custom-width" v-model="form1.newMonth" type="month" placeholder="请选择"
                  value-format="yyyy-MM" :disabled="isreadonly1">
                </el-date-picker>
                </el-date-picker>
              </el-form-item>
            </div> -->
            <div class="mycustom">
              <el-form-item label="处理备注" prop="notes" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入备注内容" v-model="form1.notes"
                  :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm1(modifyLi)" v-if="isreadonly1 == false">确 定</el-button>
        <el-button @click="cancel1">{{ isreadonly1 == false ? '取 消' : '关 闭' }}</el-button>
      </div>
    </my-dialog>
    <el-dialog title="地图定位" :visible.sync="mapTaost" @close="closeMap" width="600px">
      <VueMap></VueMap>
    </el-dialog>
  </div>
</template>
<script>
  import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
  import NewSelect from "@/components/YB/MyAreaSelect_li.vue";
  import VueMap from "@/components/VueMap/index.vue";
  import Dufenmiao from "@/components/Dufenmiao/index.vue";
  import {
    getTbaleData,
    getDeleteS,
    getDelete,
    addReportForms,
    getFormsId,
    modifyReportForms,
    getAreaName,
  } from "@/api/tablebbtb/bbtb";
  import {
    numberValidator
  } from '@/utils/validators'
  import DictUtils from "@/utils/dictUtils";
  export default {
    filters: {
      number: (value) => numberValidator(value)
    },
    name: "HelloWorld",
    components: {
      MyAreaSelect: MyAreaSelect,
      NewSelect: NewSelect,
      VueMap: VueMap,
      Dufenmiao: Dufenmiao
    },
    data() {
      return {
        coordinates: [0, 0],
        address: '',
        typeClass: 0,
        typeName: [{
          name: '度分秒',
          value: 0
        }, {
          name: '度',
          value: 1
        }],
        allStatus: [{
          name: '待审核',
          value: 0
        }, {
          name: '已通过',
          value: 1
        }, {
          name: '未通过',
          value: 2
        }, {
          name: '县级待审核',
          value: 3
        }, {
          name: '市级待审核',
          value: 4
        }],
        //控制页面是否显示详情，修改
        isreadonly: false,
        isreadonly1: false,
        childrenValue: '',
        parentValue: '',
        //表格高度
        inTableHeight: 300,
        multipleSelection: [], //表格多选框数据
        timeData: null, //时间
        bbtbProblum: '',
        sourceDate: '',
        // 区域选择框默认选中
        parentAreaCode: "",
        childrenAreaCode: "",
        parentAreaCode1: "",
        childrenAreaCode1: "",
        isReadOnlyParent: false, // 控制父下拉框是否可编辑
        isReadOnlyChildren: false, // 控制子下拉框是否可编辑
        isReadOnlyParent1: false, // 控制父下拉框是否可编辑
        isReadOnlyChildren1: false, // 控制子下拉框是否可编辑
        isClear: false, // 父选择框是否可清空
        msg: "Welcome to Your Vue.js App",
        formInline: {
          pageNum: 1,
          pageSize: 10,
          // issueType: "", //报告类型
          areaName: "", //区域名称
          areaCode: "", //区域编码
          startTime: null, //开始时间
          endTime: null, //结束时间
          status: null
        },
        total: 0,
        tableData: [],
        //父表单
        form: {
          id: '',
          filingTimeAll: undefined,
          areaName: undefined,
          areaCode: undefined,
          jointLawEnforcement: undefined,
          waterEnforcementPerson: undefined,
          policePerson: undefined,
          otherPerson: undefined,
          illegalSandMining: undefined,
          transferredToPolice: undefined,
          sourcePolice: undefined,
          sourceNaturalResources: undefined,
          sourceTransport: undefined,
          publicityMaterials: undefined,
          mediaReports: undefined,
          statementDos: []
        },
        //子表单
        form1: {
          id: '',
          longitudeType: undefined,
          closingTime: undefined,
          closingInformation: undefined,
          reportPersonList: [],
          filingTime: undefined,
          liabilityNumberPersons: 0,
          whetherCompensation: true,
          notCause: undefined,
          regulationMeasure: undefined,
          reportType: undefined,
          issueType: undefined,
          responsiblePerson: undefined,
          riverName: undefined,
          village: undefined,
          hasSandPit: false,
          illegalExtractionVolume: undefined,
          administrativeCase: true,
          transferredToPolice: false,
          involvedInOrganizedCrime: false,
          remediationStatus: undefined,
          notes: undefined,
          longitude: undefined,
          latitude: undefined,
          sandPitCount: undefined,
          sandPitArea: undefined,
          sandPitDepth: undefined,
          suspectsCount: undefined,
          fineAmount: undefined,
          confiscationIllegalGains: undefined,
          administrativeCaseDetails: undefined,
          caseClosed: false,
          remediationMeasuresDefined: false,
          responsibleUnit: undefined,
          chargePerson: undefined,
          completionDeadline: undefined,
          rectificationCompleted: false,
          liability: false,
          status: undefined,
          createUserId: undefined,
          updateUserId: undefined,
          newMonth: undefined,
          sourceClues: undefined,
          criminalFiling: undefined,
          illegalSandMining: undefined,
          otherPerson: undefined,
          policePerson: undefined,
          townshipPerson: undefined,
          waterEnforcementPerson: undefined,
          jointLawEnforcement: false,
        },
        rules: {
          // areaCode: [{
          //   required: true,
          //   message: '请选择所在区域',
          //   trigger: 'change'
          // }],
          filingTimeAll: [{
            type: 'string',
            required: true,
            message: '请选择报告时间',
            trigger: 'change'
          }]
        },
        rules1: {
          filingTime: [{
            type: 'string',
            required: true,
            message: '请选择立案时间',
            trigger: 'change'
          }],
          closingTime: [{
            validator: (rule, value, callback) => {
              if (this.form1.caseClosed) {
                if (value && typeof value === 'string') {
                  callback();
                } else {
                  callback(new Error('请选择结案时间'));
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }],
          issueType: [{
            required: true,
            message: '请选择问题类型',
            trigger: 'change'
          }],
          sourceClues: [{
            required: true,
            message: '请选择线索来源',
            trigger: 'change'
          }],
          // newMonth: [{
          //   type: 'string',
          //   required: true,
          //   message: '请选择新增月份',
          //   trigger: 'change'
          // }],
          administrativeCaseDetails: [{
            required: true,
            message: '请输入案件简要情况',
            trigger: 'blur'
          }, ],
          illegalExtractionVolume: [{
            required: true,
            message: '请输入采砂量',
            trigger: 'blur'
          }, ],
          suspectsCount: [{
            required: true,
            message: '请输入涉案人数',
            trigger: 'blur'
          }, ],
          riverName: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }, ],
          village: [{
            required: true,
            message: '请输入',
            trigger: 'blur'
          }, ],
          // longitude: [{
          //   required: true,
          //   message: '请输入经度',
          //   trigger: 'blur'
          // }, ],
          // latitude: [{
          //   required: true,
          //   message: '请输入纬度',
          //   trigger: 'blur'
          // }, ],
        },
        textarea: '',
        // 弹出层标题
        title: "",
        title1: "",
        // 是否显示弹出层
        open: false,
        open1: false,
        zname: '',
        fname: '',
        deptname: '',
        deptcode: '',
        rolenames: '',
        zilei: false,
        modifyLi: 0,
        index: '',
        //自定义状态，0是县级填报人1是市级填报人
        state: 0,
        areaCode: undefined,
        mapTaost: false,
        map: undefined,
        illegalExtractionVolumeType: false,
        num: 0,
        oldnum: 0,
        geocoder: null,
        myareaname: '',
        fillStatusList:[]
      };
    },
    created() {
      this.getDateMyYear();
      this.getTable();
      // this.getArea();
      // 获取字典数据
      DictUtils.cList('bbtbProblum').then(r => {
        this.bbtbProblum = r.bbtbProblum
      });
      DictUtils.cList('sourceTypes').then(r => {
        this.sourceDate = r.sourceTypes
      });
      DictUtils.cList('fillStatus').then(r => {
        this.fillStatusList = r.fillStatus
      });
      this.rolenames = this.$store.state.user.rolenames;
      this.deptcode = this.$store.state.user.deptcode;
      this.deptname = this.$store.state.user.deptname;
      console.log(this.deptcode, '111111');

      console.log(this.$store.state.user.deptcode, '这是判断长度', this.$store.state.user.rolenames.includes('审批人'))
      if (this.$store.state.user.deptcode.length == 9) {
        this.state = 1; //市级
      } else if (this.$store.state.user.deptcode.length > 9) {
        this.state = 0; //县级
      } else if (this.$store.state.user.deptcode.length < 9) {
        this.state = 2; //省级
      }
    },
    mounted() {
      // this.$nextTick(() => { this.initMap(); });

      // 全局函数，作为回调
      window.initBaiduMap = this.initMap;
      window.onresize = function() {
        that.changeTableHeight();
      };
      //监听浏览器标签页切换
      document.addEventListener("visibilitychange", function() {
        if (!document.hidden) {
          that.changeTableHeight();
        }
      });
    },
    methods: {
      /*本页面主逻辑为区县级填报报表，点击新增报表弹出主表，主表主要是区域案件外联合执法人数的填报，然后可以新增子表，子表主要数据是案件的数据，可以填报多条数据，并且以数组形式传回给后端，新增表单子表必须有案件，提交成功后如果是区县级则会汇总到县级审批人页面进行审批，如果是市级填报则直接到市级审批*/
      getDateMyYear() {
          const now = new Date();
          const currentYear = now.getFullYear();
          const currentMonth = String(now.getMonth() + 1).padStart(2, '0');
          this.timeData=[`${currentYear}-01`, `${currentYear}-${currentMonth}`];
          this.formInline.startTime=currentYear+'-01';
          this.formInline.endTime=currentYear+'-'+currentMonth;
          // return {
          //   timeData: [`${currentYear}-01`, `${currentYear}-${currentMonth}`]
          // };
        },
      validateUnit() {
        if (this.form1.fineAmount > 10) {
          this.$confirm('罚款金额单位为万元，请复核数据单位，确保无误', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).catch(() => {})
        }
      },
      validateUnit1() {
        if (this.form1.confiscationIllegalGains > 10) {
          this.$confirm('没收违法所得金额单位为万元，请复核数据单位，确保无误', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).catch(() => {})
        }
      },
      mychangge(e) {
        console.log(e, '这里是否执行')
        if (e == false) {
          this.form1.closingTime = undefined;
          this.form1.closingInformation = '';
        }
      },
      setType(e) {
        console.log(e)
        this.form1.latitude = undefined;
        this.form1.longitude = undefined;
        this.coordinates = [0, 0];
      },
      getValue(val) {
        this.form1.latitude = val[1];
        this.form1.longitude = val[0];
      },
      handleChange(value) {
        let num = this.oldnum;
        if (value > num) {
          this.addItem();
          this.form1.liabilityNumberPersons = value;
          this.oldnum = value;
        } else {
          this.deleteItem(this.form1.liabilityNumberPersons - 1);
          this.form1.liabilityNumberPersons = value;
          this.oldnum = value;
        }
      },
      addItem() {
        this.form1.reportPersonList.push({
          name: '',
          phone: ''
        })
      },
      deleteItem(index) {
        this.form1.reportPersonList.splice(-1)
      },
      setTab() {
        if (this.form1.illegalExtractionVolume > 500) {
          this.illegalExtractionVolumeType = true;
        } else {
          this.illegalExtractionVolumeType = false;
        }
      },
      mapClick() {
        this.mapTaost = true;
      },
      closeMap() {
        this.mapTaost = false;
      },
      //返回名称
      reTbName(type) {
        if (type == 'notFill') {
          return '未填报';
        } else if (type == 'filled') {
          return '已填报';
        } else{
          return '未知状态';
        }
      },
      reTb(type) {
        if (type == '未填报') {
          return 'danger';
        } else if (type == '已填报') {
          return 'success';
        } else{
          return 'danger';
        }
      },
      // 返回状态
      reType(type, x, s, deptId) {
        if (type == 0 || type == '' || type == null || type == undefined) {
          return 'warning';
        } else if (type == 1) {
          return 'success';
        } else if (type == 2) {
          return 'danger';
        }
      },
      reTypeName(type, x, s, deptId) {
        console.log(type, '传过来的types',x,s)
        if (type == 0 || type == '' || type == null || type == undefined) {
          if (x == 0) {
            return '县级待审核';
          } else if (s == 0 && x == 1) {
            return '市级待审核';
          } else {
            return '未审核';
          }
          //   else if (deptId&&deptId.length==7) {
          //   return '已通过';
          // }
        } else if (type == 1) {
          return '已通过';
        } else if (type == 2) {
          return '未通过';
        } else if (type == 3) {
          return '暂存';
        } else {
          return '未审核';
        }
      },
      //获取选择地区名称
      fclick(res) {
        console.log(res)
        // this.zname = res;
      },
      zclick(res) {
        console.log(res)
        // this.zname = res;
      },
      //设置表格高度
      changeTableHeight() {
        if (!this.$refs.innerTable) {
          return;
        }

        var tableHeight = window.innerHeight || document.body.clientHeight;

        //计算表格上方的元素高度，和底部的元素高度，自动计算一个值
        var disTop = this.$refs.innerTable.$el;

        //如果表格上方有元素则减去这些高度适应窗口，66是底下留白部分
        tableHeight -= disTop.offsetTop + 60 + 84;
        /*if (disTop.offsetParent) {
          tableHeight -= disTop.offsetParent.offsetTop;
        }*/
        this.$nextTick(function() {
          this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
        })

        //重绘表格
        this.doLayout();
      },
      // 多选框
      handleSelectionChange(val) {
        this.multipleSelection = []
        val.forEach((item) => {
          this.multipleSelection.push(item.id)
        })
      },
      handleAdd() {
        this.parentAreaCode1 = '';
        this.reset();
        this.open = true;
        this.isreadonly = false;
        this.title = "新增月报（*为必填项）";
        this.zilei = true;
        this.deptname = this.$store.state.user.deptname;
      },
      handleAddSon() {
        // this.parentAreaCode1='';
        this.reset1();
        this.open1 = true;
        this.isreadonly1 = false;
        this.title1 = "新增案件（*为必填项）";
        this.modifyLi = 0;
        this.index = '';
        this.typeClass = 0;
        this.illegalExtractionVolumeType = false;
        this.form1.liabilityNumberPersons = 0;
        this.oldnum = 0;
        this.$set(this.coordinates, 0, 0);
        this.$set(this.coordinates, 1, 0);
      },
      /** 任务详细信息 */
      handleView(row) {
        this.reset();
        this.title = "月报详情";
        this.open = true;
        this.isreadonly = true;
        // childrenValue:'',
        console.log(row)
        this.deptname = row.deptName;
        this.parentAreaCode1 = row.areaCode;
        this.zilei = false;
        this.form = row;
      },
      /** 任务详细信息 */
      handleView1(row) {
        console.log(row)
        this.reset1();
        this.title1 = "案件详情";
        this.open1 = true;
        this.isreadonly1 = true;
        // this.form1.liabilityNumberPersons=0;
        this.oldnum = row.liabilityNumberPersons;
        // childrenValue:'',
        // this.parentAreaCode1=row.areaCode;
        row.hasSandPit = row.hasSandPit == 0 ? false : true;
        row.administrativeCase = row.administrativeCase == 0 ? false : true;
        row.transferredToPolice = row.transferredToPolice == 0 ? false : true;
        row.involvedInOrganizedCrime = row.involvedInOrganizedCrime == 0 ? false : true;
        row.caseClosed = row.caseClosed == 0 ? false : true;
        row.remediationMeasuresDefined = row.remediationMeasuresDefined == 0 ? false : true;
        row.rectificationCompleted = row.rectificationCompleted == 0 ? false : true;
        row.liability = row.liability == 0 ? false : true;
        row.jointLawEnforcement = row.jointLawEnforcement == 0 ? false : true;
        row.whetherCompensation = row.whetherCompensation == 0 ? false : true;
        row.sourceClues = row.sourceClues ? row.sourceClues.toString() : '';
        this.typeClass = parseInt(row.longitudeType);
        this.$set(this.coordinates, 0, row.longitude);
        this.$set(this.coordinates, 1, row.latitude);
        this.form1 = row;
        this.setTab();
        this.modifyLi = 0;
        this.index = '';
      },
      modifyView(row) {
        this.reset();
        this.title = "修改报告单";
        this.open = true;
        this.isreadonly = false;
        this.parentAreaCode1 = row.areaCode;
        this.form = row;
        this.zilei = true;
      },
      modifyView1(row, index) {
        this.reset1();
        this.title1 = "修改案件";
        this.open1 = true;
        this.isreadonly1 = false;
        // this.form1.liabilityNumberPersons=0;
        this.oldnum = row.liabilityNumberPersons;
        this.parentAreaCode1 = row.areaCode;
        row.hasSandPit = row.hasSandPit == 0 ? false : true;
        row.administrativeCase = row.administrativeCase == 0 ? false : true;
        row.transferredToPolice = row.transferredToPolice == 0 ? false : true;
        row.involvedInOrganizedCrime = row.involvedInOrganizedCrime == 0 ? false : true;
        row.caseClosed = row.caseClosed == 0 ? false : true;
        row.remediationMeasuresDefined = row.remediationMeasuresDefined == 0 ? false : true;
        row.rectificationCompleted = row.rectificationCompleted == 0 ? false : true;
        row.liability = row.liability == 0 ? false : true;
        row.jointLawEnforcement = row.jointLawEnforcement == 0 ? false : true;
        row.whetherCompensation = row.whetherCompensation == 0 ? false : true;
        row.sourceClues = row.sourceClues ? row.sourceClues.toString() : '';
        this.typeClass = parseInt(row.longitudeType);
        this.$set(this.coordinates, 0, row.longitude);
        this.$set(this.coordinates, 1, row.latitude);
        this.form1 = row;
        this.setTab();
        this.modifyLi = 1;
        this.index = index;
      },
      /** 提交按钮 */
      submitForm(status) {
        this.$refs["form"].validate((valid, errorObj) => {
          if (valid) {

            this.form.status = status;
            if (this.state == 2) {
              this.form.status = 1;
            }
            if (this.form.statementDos) {
              let statementDos = JSON.parse(JSON.stringify(this.form.statementDos));
              if (statementDos) {
                for (let i = 0; i < statementDos.length; i++) {
                  statementDos[i].hasSandPit = statementDos[i].hasSandPit == false ? 0 : 1;
                  statementDos[i].administrativeCase = statementDos[i].administrativeCase == false ? 0 : 1;
                  statementDos[i].transferredToPolice = statementDos[i].transferredToPolice == false ? 0 : 1;
                  statementDos[i].involvedInOrganizedCrime = statementDos[i].involvedInOrganizedCrime == false ? 0 :
                    1;
                  statementDos[i].caseClosed = statementDos[i].caseClosed == false ? 0 : 1;
                  statementDos[i].remediationMeasuresDefined = statementDos[i].remediationMeasuresDefined == false ?
                    0 : 1;
                  statementDos[i].rectificationCompleted = statementDos[i].rectificationCompleted == false ? 0 : 1;
                  statementDos[i].liability = statementDos[i].liability == false ? 0 : 1;
                  statementDos[i].jointLawEnforcement = statementDos[i].jointLawEnforcement == false ? 0 : 1;
                  statementDos[i].whetherCompensation = statementDos[i].whetherCompensation == false ? 0 : 1;
                }
              }
              this.form.statementDos = statementDos;
            }
            console.log(this.state, this.areaCode)
            if (this.state == 0 && this.areaCode && this.areaCode.length <= 11 && this.areaCode.length != 7) {
              this.$modal.msgWarning("区域异常，请联系技术人员排查！");
              return;
            }
            if (this.form.id) {
              this.form.areaName = this.zname;
              this.form.areaCode = this.areaCode;
              modifyReportForms(this.form).then(r => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.onReset();
              });
            } else {
              this.form.areaName = this.zname;
              this.form.areaCode = this.areaCode;
              addReportForms(this.form).then(r => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.onReset();
              });
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      },
      //子表提交，并push到主表数组
      submitForm1(type) {
        this.$refs["form1"].validate((valid, errorObj) => {
          if (valid) {
            if(this.form1.latitude&&this.form1.latitude!=''){

            }else{
              this.$modal.msgWarning("请输入经纬度！");
              return false;
            }
            if(this.form1.longitude&&this.form1.longitude!=''){

            }else{
              this.$modal.msgWarning("请输入经纬度！");
              return false;
            }
            this.form1.hasSandPit = this.form1.hasSandPit == false ? 0 : 1;
            this.form1.administrativeCase = this.form1.administrativeCase == false ? 0 : 1;
            this.form1.transferredToPolice = this.form1.transferredToPolice == false ? 0 : 1;
            this.form1.involvedInOrganizedCrime = this.form1.involvedInOrganizedCrime == false ? 0 : 1;
            this.form1.caseClosed = this.form1.caseClosed == false ? 0 : 1;
            this.form1.remediationMeasuresDefined = this.form1.remediationMeasuresDefined == false ? 0 : 1;
            this.form1.rectificationCompleted = this.form1.rectificationCompleted == false ? 0 : 1;
            this.form1.liability = this.form1.liability == false ? 0 : 1;
            this.form1.jointLawEnforcement = this.form1.jointLawEnforcement == false ? 0 : 1;
            this.form1.whetherCompensation = this.form1.whetherCompensation == false ? 0 : 1;
            this.form1.longitudeType = this.typeClass;
            if (type == 1) {
              this.$modal.msgSuccess("修改成功");
              this.$set(this.form.statementDos, this.index, this.form1);
              this.open1 = false;
            } else {
              this.$modal.msgSuccess("新增成功");
              this.form.statementDos.push(this.form1);
              this.open1 = false;
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      },
      handleSizeChange(val) {
        this.formInline.pageNum = 1
        this.formInline.pageSize = val
        this.getTable()
      },
      handleCurrentChange(val) {
        this.formInline.pageNum = val
        this.getTable()

      },
      getArea() {
        getAreaName().then((res) => {
          console.log(res, '我获取的')
          this.zname = res.AREA_NAME;
          this.areaCode = res.AREA_CODE;
          if (res.DEPT_CODE.length == 9) {
            this.parentAreaCode = res.AREA_CODE;
            this.myareaname = res.AREA_NAME;
            this.isReadOnlyParent = true;
          } else {
            this.parentAreaCode = '';
            this.myareaname = '';
            this.isReadOnlyParent = false;
          }
          console.log(res, '获取的')
        });
      },
      // 获取表格数据
      getTable() {
        getTbaleData(this.formInline).then((res) => {
          console.log(res, '获取的月报填报列表')
          this.tableData = res.records;
          this.total = res.total;
          this.getArea();
        });
      },

      // 重置按钮
      onReset() {
        // this.formInline.pageNum=1;
        // this.formInline.pageSize=10;
        // this.formInline.startTime=null;
        // this.formInline.endTime=null;
        //  this.formInline.status=null;
        //  this.childrenAreaCode=null;
        this.formInline = {
          pageNum: 1,
          pageSize: 10,
          // issueType: "", //报告类型
          areaName: "", //区域名称
          areaCode: "", //区域编码
          startTime: null, //开始时间
          endTime: null, //结束时间
          status: null
        }
        this.parentAreaCode = null;
        this.timeData = null;
        if (this.state == 1) {
          console.log(this.parentAreaCode, this.childrenAreaCode)
          // this.getArea();
        }
        this.getDateMyYear();
        this.getTable()
      },
      // 搜索按钮
      onSubmit() {
        this.formInline.pageNum = 1;
        if (this.timeData) {
          this.formInline.startTime = this.timeData[0];
          this.formInline.endTime = this.timeData[1];
        } else {
          this.formInline.startTime = null;
          this.formInline.endTime = null;
        }
        this.getTable()
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 取消按钮
      cancel1() {
        this.open1 = false;
        this.reset1();
      },
      handelDelete(row) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          getDelete({
            id: row
          }).then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getTable()
          })

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      handelDelete1(row, index) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.form.statementDos.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });

        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      handleDelete() {
        if (this.multipleSelection.length == 0) {
          this.$modal.msgWarning("请选择要删除的数据")
          return
        }
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {

          getDeleteS(this.multipleSelection).then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getTable()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });

      },
      // 表单重置
      reset() {
        this.form = {

          id: null,
          filingTimeAll: undefined,
          areaName: undefined,
          areaCode: undefined,
          jointLawEnforcement: undefined,
          waterEnforcementPerson: undefined,
          policePerson: undefined,
          otherPerson: undefined,
          illegalSandMining: undefined,
          transferredToPolice: undefined,
          sourcePolice: undefined,
          sourceNaturalResources: undefined,
          sourceTransport: undefined,
          publicityMaterials: undefined,
          mediaReports: undefined,
          statementDos: []
        }
        this.resetForm("form");
      },
      // 表单重置
      reset1() {
        this.form1 = {

          id: null,
          longitudeType: undefined,
          closingTime: undefined,
          closingInformation: undefined,
          filingTime: undefined,
          liabilityNumberPersons: 0,
          reportPersonList: [],
          whetherCompensation: true,
          notCause: undefined,
          regulationMeasure: undefined,
          reportType: undefined,
          issueType: undefined,
          responsiblePerson: undefined,
          riverName: undefined,
          village: undefined,
          hasSandPit: false,
          illegalExtractionVolume: undefined,
          administrativeCase: true,
          transferredToPolice: false,
          involvedInOrganizedCrime: false,
          remediationStatus: undefined,
          notes: undefined,
          longitude: undefined,
          latitude: undefined,
          sandPitCount: undefined,
          sandPitArea: undefined,
          sandPitDepth: undefined,
          suspectsCount: undefined,
          fineAmount: undefined,
          confiscationIllegalGains: undefined,
          administrativeCaseDetails: undefined,
          caseClosed: false,
          remediationMeasuresDefined: false,
          responsibleUnit: undefined,
          chargePerson: undefined,
          completionDeadline: undefined,
          rectificationCompleted: false,
          liability: false,
          status: undefined,
          createUserId: undefined,
          updateUserId: undefined,
          // areaName:undefined,
          // areaCode:undefined,
          newMonth: undefined,
          sourceClues: undefined,
          criminalFiling: undefined,
          illegalSandMining: undefined,
          otherPerson: undefined,
          policePerson: undefined,
          townshipPerson: undefined,
          waterEnforcementPerson: undefined,
          jointLawEnforcement: false,
        }
        this.resetForm("form1");
      },
    },
  };
</script>

<style scoped>
  h1 {
    color: #42b983;
  }

  .myclass {
    display: flex;
  }

  .mycustomName {
    width: 100%;
  }

  .autoNames {
    width: 100%;
    display: flex;
  }

  .mapUp {
    float: right;
    font-size: 30px;
    padding: 4px 0 0 5px;
    color: #67C23A;
    cursor: pointer;
  }

  .isLine {
    margin-top: 10px;
  }

  .myTables {
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-weight: 700;
    text-indent: 8px;
  }

  .custom-width {
    width: 150px;
  }

  .custom-width-select {
    width: 100px;
  }

  .customonly-width {
    width: 100px;
  }

  .mycustom {
    display: flex;
  }

  .custom-widthAll {
    width: 700px;
  }

  .myscroll {
    overflow: auto;
    height: 480px;
    width: 100%;

  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    width: 12px;
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
  }

  /* 滚动条滑块在鼠标悬停时的样式 */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  ::v-deep .tableScopeSwitch .el-switch__label {
    position: absolute;
    display: none;
    color: #fff;
  }

  ::v-deep .el-form-item__label {
    text-align: left;
    /* 确保 label 是左对齐的 */
    text-indent: 8px;
  }

  /*打开时文字位置设置*/

  ::v-deep .tableScopeSwitch .el-switch__label--right {
    z-index: 1;
    right: 20px;
    /*不同场景下可能不同，自行调整*/
  }

  /*关闭时文字位置设置*/

  ::v-deep .tableScopeSwitch .el-switch__label--left {
    z-index: 1;
    left: 20px;
    /*不同场景下可能不同，自行调整*/
  }

  /*显示文字*/

  ::v-deep .tableScopeSwitch .el-switch__label.is-active {
    display: block;
  }

  ::v-deep .myscroll input[type=number]::-webkit-inner-spin-button,
  ::v-deep .myscroll input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  ::v-deep .myscroll input[type=number] {
    -moz-appearance: textfield;
    /* Firefox */
  }

  ::v-deep .el-input__inner {
    line-height: 1px !important;
  }

  .box-card {
    margin-bottom: 10px;
    margin-top: 5px;
    height: 70px;
  }

  /**
   * 解决el-input设置类型为number时，去掉输入框后面上下箭头
   **/
  /* ::v-deep .myscroll input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  } */
</style>
