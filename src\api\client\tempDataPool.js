import request from '@/utils/request'

// 查询客户端临时数据池详细
export function getTempDataPool(id) {
  return request({
    url: '/client/tempDataPool/info/' + id,
    method: 'post'
  })
}

// 新增客户端临时数据池
export function addTempDataPool(data) {
  return request({
    url: '/client/tempDataPool/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改客户端临时数据池
export function updateTempDataPool(data) {
  return request({
    url: '/client/tempDataPool/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改客户端临时数据池并更新缓存
export function updateTempDataPoolAndCache(data) {
  return request({
    url: '/client/tempDataPool/updateAndUpdateCache',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除客户端临时数据池
export function delTempDataPool(id) {
  return request({
    url: '/client/tempDataPool/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除客户端临时数据池
export function delTempDataPoolBatch(ids) {
  return request({
    url: '/client/tempDataPool/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

// 获取开启终端的项目列表
export function listProjectNamesByUseClient() {
  return request({
    url: '/project/project/listProjectNamesByUseClient',
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

// 获取某个项目下的磅站列表
export function listStationByProjectId(projectId) {
  return request({
    url: '/project/weighingStation/list/' + projectId,
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}


