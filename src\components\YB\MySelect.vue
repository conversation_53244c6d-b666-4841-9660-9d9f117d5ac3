<template>
  <el-select v-bind="$attrs"
             v-on="$listeners"
             style="width: 100%;"
             v-model="innerValue"
             :clearable="clearableVal"
             :disabled="disabled"
             :filterable="filterable"
             :multiple="multiple">
      <slot v-bind:options="finalOptions">
        <el-option v-for="(item,index) in finalOptions"
                   :key="item[itemValue]"
                   :label="item[itemName]"
                   :value="item[itemValue]"
        ></el-option>
      </slot>
  </el-select>
</template>

<script>
import BaseMixin from "@/components/YB/mixins/BaseMixin";

export default {
  name: "MySelect",
  mixins: [BaseMixin],
  props:{
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  computed:{
    clearableVal () {
      return this.disabled ? false : this.clearable
    },
  }
}
</script>

<style scoped>

</style>
