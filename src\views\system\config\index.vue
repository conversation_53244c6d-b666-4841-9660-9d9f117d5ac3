<template>
  <div class="app-container">

    <!-- tabs页签 -->
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane :label="category.name" :name="category.value" v-for="(category,index) in categoryList">
        </el-tab-pane>
          <el-form ref="form" :model="form" label-width="180px" label-position="right">
            <template v-for="config in configsArray">
                  <my-form-item :label="config.item.configName" :prop="config.id" :rules="[{notNull:!config.item.nullable, message:config.item.configName+'不能为空'}]">
                        <my-input style="width: 500px" v-model="form[config.id]"
                                v-bind:placeholder="config.item.configName"/>
                  </my-form-item>
            </template>
            <div style="margin-left: 180px;">
              <el-button type="primary" @click="submitForm">保 存</el-button>
            </div>
          </el-form>
    </el-tabs>


  </div>
</template>

<script>
  import { addConfig,getCategoryList,getListByConfigItem } from "@/api/system/config";

  export default {
    data() {
      return {
        activeName:'basic',
        // 参数表格数据
        configsArray: [],
        categoryList:[],
        configCategory:'',
        index: 0,
        form:{},
      };
    },
    created() {
      this.getCategoryList();
    },
    methods: {
      /** 点击页签事件 */
      handleClick(tab, event){
        this.configCategory = tab.name;
        this.index = tab.index;
        this.reload();
      },
      /** 查询配置类别 */
      getCategoryList() {
        getCategoryList().then(response => {
          this.categoryList = response.configCategorys;
          this.configCategory = this.categoryList[0].value;
          this.index = 0;
          this.reload();
        });
      },
      reload(){
        getListByConfigItem(this.configCategory).then(response => {
          this.configsArray = response.configs;
          let result = {};
          this.configsArray.forEach((item, index) => result[item.id] = item.paramValue);
          this.form = result;
          this.$forceUpdate();
        });
      },
      /** 提交按钮 */
      submitForm: function() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.configsArray.forEach((item, index) => {
              item.paramValue = this.form[item.id];
            });
            this.$modal.loading()
            addConfig(this.configsArray,this.configCategory).then(response => {
                this.$modal.msgSuccess("保存成功");
                this.reload();
              this.$modal.closeLoading();
              }).catch(r=>{
              this.$modal.closeLoading();
            });
          }
        });
      },

    }
  };
</script>
