import request from '@/utils/request'

// 查询磅站表详细
export function getWeighingStation(id) {
  return request({
    url: '/project/weighingStation/info/' + id,
    method: 'post'
  })
}

// 新增磅站表
export function addWeighingStation(data) {
  return request({
    url: '/project/weighingStation/save',
    method: 'post',
    data: data,
    showLoading: true,
    headers: {allowRepeatSubmit: true}
  })
}

// 修改磅站表
export function updateWeighingStation(data) {
  return request({
    url: '/project/weighingStation/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除磅站表
export function delWeighingStation(id) {
  return request({
    url: '/project/weighingStation/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除磅站表
export function delWeighingStationBatch(ids) {
  return request({
    url: '/project/weighingStation/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


