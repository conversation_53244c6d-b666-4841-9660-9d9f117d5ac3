import request from '@/utils/request'

// 查询签名表详细
export function getSignature(id) {
  return request({
    url: '/sys/signature/info/' + id,
    method: 'post'
  })
}

// 新增签名表
export function addSignature(data) {
  return request({
    url: '/sys/signature/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改签名表
export function updateSignature(data) {
  return request({
    url: '/sys/signature/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除签名表
export function delSignature(id) {
  return request({
    url: '/sys/signature/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除签名表
export function delSignatureBatch(ids) {
  return request({
    url: '/sys/signature/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


