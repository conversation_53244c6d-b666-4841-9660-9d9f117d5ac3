import request from '@/utils/request'
export function getProjectListByCondition(data) {
  return request({
    url: '/project/statistics/list',
    method: 'post',
    data: data
  })
}

export function getTruckRealTime(projectId) {
  return request({
    url: '/third/bus/getTruckReal?projectId=' + projectId,
    method: 'post',
    headers: {allowRepeatSubmit: true},
  })
}

export function getTruckHistory(params){
  return request({
    url: '/third/bus/getTruckHis',
    method: 'post',
    data: params,
    headers: {allowRepeatSubmit: true},
  })
}

export function getCountByDeptId(projectId){
  return request({
    url: '/project/project/getCountByDeptId/' + projectId,
    method: 'post',
    headers: {allowRepeatSubmit: true},
  })
}

export function getCameraListByDeptId(deptId) {
  return request({
    url:'/video/liveCamera/getListByDeptId/'+deptId,
    method: 'post',
  })
}
