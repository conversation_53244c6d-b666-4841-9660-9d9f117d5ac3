import reques from '@/utils/request'

//采砂月报表项目信息获取方法
// export function getProjectListForMonthlyReports(queryParams) {
//   return reques({
//     url: '/project/statistics/list',
//     method: 'post',
//     data: queryParams
//   })
// }

// 获取项目信息（项目名称+标段，不分页）
export function getProjectList(queryParams) {
  return reques({
    url: '/project/statistics/getProjectList',
    method: 'post',
    data: queryParams
  })
}

// 获取用户deptCode
export function getUser() {
  return reques({
    url: '/project/statistics/getUser',
    method: 'post',
  })
}

//项目综合利用报表合计
export function getProjectTotalList(queryParams) {
  return reques({
    url: '/project/statistics/getPeojectTotal',
    method: 'post',
    data: queryParams,
    headers: {allowRepeatSubmit: true}
  })
}
