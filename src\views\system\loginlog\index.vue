<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="70px">
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="用户名或姓名"
          clearable
          style="width: 200px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          style="width: 200px"
          v-model="queryParams.startTime"
          value-format="yyyy-MM-dd"
          placeholder="登录时间起"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          style="width: 200px"
          v-model="queryParams.endTime"
          value-format="yyyy-MM-dd"
          placeholder="登录时间止"
        ></el-date-picker>
      </el-form-item>
<!--      <el-form-item label="登录状态" prop="status">-->
<!--        <el-select-->
<!--          v-model="queryParams.loginStatus"-->
<!--          placeholder="登录状态"-->
<!--          clearable-->
<!--          style="width: 200px"-->
<!--        ><el-option-->
<!--            v-for="loginStatus in loginStatusList"-->
<!--            :key="loginStatus.value"-->
<!--            :label="loginStatus.name"-->
<!--            :value="loginStatus.value"-->
<!--        /></el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete('')"
          v-hasPermi="['sys:loginlog:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar
        class="rightToolbar"
        :search.sync="search"
        :showSearch.sync="showSearch"
        @queryTable="getList">
      </right-toolbar>
    </el-row>
    <my-table url="/sys/loginlog/list" ref="sysLoginLogTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column min-width="120" label="用户名" align="center" prop="userName" column-key="u.USERNAME" sortable="custom" fixed="left"/>
      <el-table-column min-width="160" label="登录时间" align="center" prop="loginTime" column-key="log.LOGIN_TIME" sortable="custom"/>
      <el-table-column min-width="160" label="退出时间" align="center" prop="logoutTime" column-key="log.LOGOUT_TIME" sortable="custom"/>
      <el-table-column min-width="120" label="在线时长" align="center" prop="onlineTimeLength" :formatter="formatTime" column-key="log.ONLINE_TIME_LENGTH"  sortable="custom"/>
      <el-table-column min-width="150" label="登录IP" align="center" prop="loginIp" column-key="log.LOGIN_IP" sortable="custom"/>
      <el-table-column min-width="150" label="备注" align="center" prop="remark" column-key="log.REMARK" sortable="custom"/>
      <el-table-column min-width="80" label="操作" align="center" column-key="caozuo" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="danger"
            class="btn-table-operate"
            title="删除"
            size="mini"
            icon="el-icon-delete"
            v-hasPermi="['sys:loginlog:delete']"
            @click="handleDelete(scope.row.id)"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>
  </div>
</template>
<script>
import { batchDelLoginLog, delOneLoginLog } from '@/api/system/loginlog'
import dictUtils from '@/utils/dictUtils'
export default {
  name: 'Loginlog',
  data() {
    return {
      loading: true,
      search: true, //控制显隐查询
      showSearch: true,  //显示显隐查询
      queryParams: {
        userName: '',
        // loginStatus: '',
        startTime: '',
        endTime: ''
      },
      loginStatusList: [],  //登录状态字典数组
      ids: [],  // 选中数组
      multiple: true,
    }
  },
  methods: {
    //查询登录日志列表
    getList(restart) {
      this.$refs.sysLoginLogTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    //搜索操作
    handleQuery() {
      this.getList()
    },
    //重置操作
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    //多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    //(批量)删除操作
    handleDelete(id) {
      if (id) {
        this.$modal.confirm('确定要删除选中的记录？').then(()=> {
          return delOneLoginLog(id)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('操作成功')
        }).catch(() => {})  //捕获异常错误
      } else {
        this.$modal.confirm('确定要删除选中的 '+this.ids.length+' 条记录？').then(() => {
          return batchDelLoginLog(this.ids)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('操作成功')
        }).catch(() => {})  //捕获异常错误
      }
    },
    //格式化时间
    formatTime(row, column, cellValue, index) {
      if (cellValue == null) {
        return ''
      }
      if (cellValue < 60) {
        return cellValue + '秒'
      }
      if (cellValue < 3600) {
        return Math.floor(cellValue / 60) + '分' + (cellValue % 60) + '秒'
      }
      return Math.floor(cellValue / 3600) + '小时' + (Math.floor(cellValue / 60) % 60) + '分' + (cellValue % 60) + '秒'
    },
  },
  mounted() {
    //调用字典工具类
    // dictUtils.cList('loginStatus').then(r => {
    //   this.loginStatusList = r.loginStatus
    // })
  }
}
</script>

