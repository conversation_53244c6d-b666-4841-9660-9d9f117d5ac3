import request from '@/utils/request'

//获取菜单树
export function getDeptTreeData() {
  return request({
    url: '/sys/dept/select?rootId=currentUser&notType=sand',
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

export function getDeptTreeData2() {
  return request({
    url: '/sys/dept/select?rootId=currentUser',
    method: 'post',
  })
}

//新建用户时获取部门树
export function getDeptTreeDataForUser() {
  return request({
    url: '/sys/dept/select?rootId=currentUser',
    method: 'post',
  })
}

//获取区域树
export function getAreaTreeData() {
  return request({
    url: '/sys/area/select?start=1&end=4',
    method: 'post',
  })
}

//删除所选机构
export function delDeptData(deptId) {
  return request({
    url: '/sys/dept/delete/' + deptId,
    method: 'post',
  })
}

//提交机构信息
export function submitDeptData(dept) {
  var url = !dept.deptId ? '/sys/dept/save' : '/sys/dept/update'
  return request({
    url: url,
    method: 'post',
    data: dept
  })
}
