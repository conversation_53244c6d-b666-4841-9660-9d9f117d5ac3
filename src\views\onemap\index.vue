<template>
  <div id="app-container" :class="isFullScreen? 'fullScreen' : ''">
    <div id="map"  style="height: 100% ;width: 100%;background-color: bisque;" >
    </div>
    <div class="controller">
      <el-button style="margin: 4px 0px;" type="primary" size="mini" @click="onClickFullScreen">{{isFullScreen ? '窗口' : '全屏'}}</el-button>
      <br>
      <!-- <el-button type="primary" size="mini" @click="onClickLayersToggle">图层</el-button> -->
      <!-- <el-button type="primary" size="mini" @click="rotateMarker(45)">旋转45度</el-button>
      <el-button type="primary" size="mini" @click="rotateMarker(90)">旋转90度</el-button>
      <el-button type="primary" size="mini" @click="rotateMarker(0)">重置朝向</el-button> -->
    </div>
    <!-- <div>
      <my-cascader
        ref="deptCascader"
        :disabled="mode==='update'"
        v-model="project.parentId"
        placeholder="请选择监管部门"
      />
    </div> -->

    <div class="controllerView" @click="onClickResetView">
      <!-- <el-button type="primary" size="mini" @click="onClickResetView">返回视野</el-button> -->
    </div>
    <div class="panel panel-header panel-header-1" ></div>
    <div class="panel panel-header panel-header-2" ></div>
    <div class="panel panel-header panel-header-3" ></div>
    <div class="panel panel-header panel-header-4" ></div>
  </div>
</template>

<script>
import L from 'leaflet';
import K from "./map_toolkit.js";
import screenfull from 'screenfull';
import '@/plugins/leaflet-pattern/leaflet.pattern-src.js';
import HBGeoJson from '@/assets/onemap/hebei/HBGeoJson.geojson';
import yehe_K4 from '@/assets/onemap/hebei/冶河可采区K4.geojson';
import yehe_J1 from '@/assets/onemap/hebei/冶河禁采区J1.geojson';
import yehe_J2 from '@/assets/onemap/hebei/冶河禁采区J2.geojson';
import yehe_J3 from '@/assets/onemap/hebei/冶河禁采区J3.geojson';
import yehe_J4 from '@/assets/onemap/hebei/冶河禁采区J4.geojson';
import yehe_B7 from '@/assets/onemap/hebei/冶河保留区B7.geojson';
import yehe_B8 from '@/assets/onemap/hebei/冶河保留区B8.geojson';
import {getProjectListByCondition,getTruckRealTime} from '@/api/onemap/onemap';

let layerTdtImg = null;
let layerTdtVec = null;
let layerTdtNote = null;

export default {
  name: 'leaflet',
  data() {
    return {
      map: null,
      isFullScreen: false,
      isLayersShownInController: false,
      queryParam:{},

      carUpdateInterval: null, //数据请求定时器
      cars: [
        // {
          // marker : null,
          // deviceId: "10641013426",
          // carPlateNum: "冀HM8888",
          // carPlateColor: "黄色",
          // lgtd: 118.0718,
          // lttd: 40.84013,
          // direction: 111,
          // tGpsStr: "2024-10-25 14:42:52",
          // idCode: "LEFYEDK46HHNA6132",
          // bdSpeed: 0,
          // setupTime: "2024-08-28",
          // bdDevSpeed: 0,
          // firstUp: null,
          // receiveTime: "2024-10-25 14:42:51",
          // pos: true,
          // tUpStr: "2024-10-25 14:42:51",
          // deviceSignalMode: "单北斗",
          // status: 524290,
          // mileage: 3060.8,
          // drift: false
        // }
      ], // 存储汽车的经纬度、朝向和marker等信息
    }
  },

  mounted() {
    if (screenfull.isEnabled) {
      console.log('Supported');
      screenfull.on('change', this.onChangeScreenFull);
    }

    // 监听全屏事件
    document.addEventListener('fullscreenchange', this.handleFullScreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);
    return;
    this.initMap() // 初始化地图
    this.fetchCarData() // 获取后台车辆数据信息
    this.updateCars(); // 更新汽车位置和朝向
    this.generateGeoJsonLayer(); //生成图层
  },

  beforeDestroy() {
    // 在组件销毁时清除定时器
    if (this.carUpdateInterval) {
        clearInterval(this.carUpdateInterval);
    }
  },

  methods: {
    //初始化地图
    initMap() {
      K.initMap("map");
      layerTdtImg = K.createLayerWmtsTdt("img", 5);
      // layerTdtImg.setOpacity(0.667);
      layerTdtVec = K.createLayerWmtsTdt("vec", 4);
      layerTdtNote = K.createLayerWmtsTdt("cva", 9999);
      // layerTdtNote.setOpacity(0.667);
      K.switchLayer(layerTdtImg, true);
      K.switchLayer(layerTdtNote, true);

      L.geoJSON(HBGeoJson, {
        style: {
          color: 'blue',
          weight: 2,
          opacity: 1,
          fillOpacity: 0
        }
      }).addTo(K.viewer);
      // this.generateCars(); // 生成初始汽车数据
    },
    //初始化采区图层
    generateGeoJsonLayer(){
      // getProjectListByCondition(this.queryParam).then(res => {
      //   const geojsonData = JSON.parse(res.list[0].geoJson)
      //   console.log("getProjectListByCondition++++++++" + geojsonData)
      // })

      // 可采区样式
        // 内部斜线图案
        var K_stripePattern = new L.StripePattern({
            weight: 2, // 线条宽度
            color: '#00ff00', // 绿
            opacity: 0.5, // 线条不透明度
            angle: 15 // 线条角度
        });
        K_stripePattern.addTo(K.viewer);
        // 边界样式
        function K_style(feature) {
          return {
            color: '#00ff00',
            weight: 3,
            opacity: 0.5,
            fillOpacity: 0.5,
            dashArray: '5, 5',
            fillPattern: K_stripePattern
          };
        }
      // 禁采区样式
        // 内部斜线图案
        var J_stripePattern = new L.StripePattern({
          weight: 2, // 线条宽度
          color: '#ff1a1a', // 红
          opacity: 0.5, // 线条不透明度
          angle: 45 // 线条角度
        });
        J_stripePattern.addTo(K.viewer);
        // 边界样式
        function J_style(feature) {
          return {
            color: '#ff1a1a',
            weight: 3,
            opacity: 0.5,
            fillOpacity: 0.5,
            dashArray: '5, 5',
            fillPattern: J_stripePattern
          };
        }

      // 保留区样式
        // 内部斜线图案
        var B_stripePattern = new L.StripePattern({
          weight: 2, // 线条宽度
          color: '#ffff66', // 黄
          opacity: 0.5, // 线条不透明度
          angle: 75 // 线条角度
        });
        B_stripePattern.addTo(K.viewer);
        // 边界样式
        function B_style(feature) {
          return {
            color: '#ffff66',
            weight: 3,
            opacity: 0.5,
            fillOpacity: 0.5,
            dashArray: '5, 5',
            fillPattern: B_stripePattern
          };
        }
      // 可采区geoJson图层
      L.geoJSON(yehe_K4, {
        style: K_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      // 禁采区geoJson图层
      L.geoJSON(yehe_J1, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J2, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J3, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_J4, {
        style: J_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      // 保留区geoJson图层
      L.geoJSON(yehe_B7, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);
      L.geoJSON(yehe_B8, {
        style: B_style,
        onEachFeature: this.onEachFeature
      }).addTo(K.viewer);

    },

    // 后台获取车辆信息
    async fetchCarData() {
      try {
        const res = await getTruckRealTime();
        const newCars = res.truckRealList.map(car => ({
          deviceId: car.deviceId,
          carPlateNum: car.carPlateNum,
          carPlateColor: car.carPlateColor,
          lgtd: car.lgtd,
          lttd: car.lttd,
          carType: car.carType,
          direction: car.direction,
          tGpsStr: car.tGpsStr,
          idCode: car.idCode,
          bdSpeed: car.bdSpeed,
        })); // 临时汽车数组

        // 更新现有的cars数组
        newCars.forEach(newCar => {
          const existingCarIndex = this.cars.findIndex(car => car.deviceId === newCar.deviceId);
          if (existingCarIndex !== -1) {
            // 如果车辆已存在，更新其信息
            const existingCar = this.cars[existingCarIndex];
            this.cars[existingCarIndex] = {
              ...newCar, // 更新新信息
              marker: existingCar.marker // 保留marker（如果存在）
            };
          } else {
            // 如果车辆不存在，添加新车辆
            this.cars.push({
              ...newCar,
              marker: null // 新车辆的marker初始化为null
            });
          }
        });
        this.updateMarkers(); // 更新标记
      } catch (error) {
        console.error('获取汽车数据失败:', error);
      }
    },
    // 更新车辆marker
    updateMarkers() {
      this.cars.forEach(car => {
        const { deviceId, carPlateNum, carPlateColor, lgtd, lttd, direction, carType, marker} = car;
        if (marker) {
          // 如果marker已存在，更新位置和朝向
          marker.setLatLng([lttd, lgtd]); // 更新位置
          const iconElement = marker.getElement();
          if (iconElement) {
            let str = iconElement.style.transform;
            let ss = str.split(' rotate(');
            iconElement.style.transform = ss[0] + ' rotate(' + direction + 'deg)'; // 旋转标记
            iconElement.style.transformOrigin = '50% 50%';
          }
        } else {
          // 如果标记不存在，添加新标记
          this.addMarker(deviceId, carPlateNum, carPlateColor, lgtd, lttd, direction, carType);
        }
      });
    },

    // 为每辆车创建marker
    addMarker(deviceId, carPlateNum, carPlateColor, lgtd, lttd, direction, carType) {
      // 创建自定义图标
      const customIcon = L.icon({
        iconUrl: require('@/assets/marker/truck.png'), // 使用本地图标
        iconAnchor: [16, 16], // 图标锚点
        popupAnchor: [-3, -76] // 弹出框锚点
      });
      const marker = L.marker([lttd, lgtd], { icon: customIcon }).addTo(K.viewer);
      // 将marker存储到对应的car对象中
      const carIndex = this.cars.findIndex(car => car.deviceId === deviceId);
      if (carIndex !== -1) {
        this.cars[carIndex].marker = marker; // 更新car对象中的marker
      }
    },
    // 更新车辆信息
    updateCars() {
      // 清除之前的定时器
      if (this.carUpdateInterval) {
        clearInterval(this.carUpdateInterval);
      }
      this.carUpdateInterval = setInterval(async () => {
        await this.fetchCarData(); // 每5秒获取一次汽车数据
      }, 3000); // 每5秒更新一次
    },

    // 给geoJson图层添加事件
    onEachFeature(feature, layer) {
      layer.on({
        click: function(e) {
          // 弹出窗口显示信息
          var popupContent = `<strong>${feature.properties.name}</strong><br>${feature.properties.info}`;
          L.popup()
              .setLatLng(e.latlng)
              .setContent(popupContent)
              .openOn(K.viewer);

          // // 高亮区域
          // layer.setStyle({
          //     opacity: 1,
          //     fillOpacity: 1
          // });
        }
      });
      // 高亮
      layer.on('mouseover', function() {
        layer.setStyle({
            opacity: 1,
            fillOpacity: 1
        });
      });
      // 取消高亮
      layer.on('mouseout', function() {
        layer.setStyle({
            opacity: 0.5,
            fillOpacity: 0.5
        });
      });
    },

    //模拟10台车辆
    // generateCars() {
    //   // 生成10辆汽车的初始位置和朝向
    //   for (let i = 0; i < 10; i++) {
    //     const lat = 38.21908555 + Math.random() * (38.23825459 - 38.21908555); // 随机纬度
    //     const lng = 114.1380116 + Math.random() * (114.148763 - 114.1380116); // 随机经度
    //     const direction = Math.random() * 360; // 随机朝向
    //     this.cars.push({ lat, lng, direction });
    //     this.addMarker(lat, lng, direction);
    //   }
    // },

    //添加车辆marker
    // addMarker(lat, lng, direction) {
    //   // 创建自定义图标
    //   const customIcon = L.icon({
    //     iconUrl: require('@/assets/marker/truck.png'), // 使用本地图标
    //     iconAnchor: [16, 16], // 图标锚点
    //     popupAnchor: [-3, -76] // 弹出框锚点
    //   });

    //   const marker = L.marker([lat, lng], { icon: customIcon }).addTo(K.viewer);
    //   this.markers.push(marker);
    // },

    //更新车辆位置和朝向
    // updateCars() {
    //   // 清除之前的定时器
    //   if (this.carUpdateInterval) {
    //     console.log("clearInterval");
    //       clearInterval(this.carUpdateInterval);
    //   }

    //   this.carUpdateInterval = setInterval(() => {

    //     this.cars.forEach((car, index) => {
    //       // 更新汽车位置和朝向，确保仍在石家庄市范围内
    //       car.lat += (Math.random() - 0.5) * 0.001; // 随机更新纬度
    //       car.lng += (Math.random() - 0.5) * 0.001; // 随机更新经度

    //       // 确保新的经纬度仍在石家庄市范围内
    //       car.lat = Math.max(38.21908555, Math.min(38.23825459, car.lat));
    //       car.lng = Math.max(114.1380116, Math.min(114.148763, car.lng));
    //       car.direction = (car.direction + (Math.random() - 0.5) * 10) % 360; // 随机更新朝向

    //       // 更新标记
    //       const marker = this.markers[index];
    //       if (marker) {
    //         marker.setLatLng([car.lat, car.lng]); // 更新位置
    //         const iconElement = marker.getElement();
    //         if (iconElement) {
    //           let str = iconElement.style.transform;
    //           let ss = str.split(' rotate(');
    //           iconElement.style.transform = ss[0] + ' rotate(' + car.direction + 'deg)'; // 旋转标记
    //           iconElement.style.transformOrigin = '50% 50%';
    //         }
    //       }
    //     });
    //   }, 5000); // 每5秒更新一次
    // },

    onClickFullScreen() {
      // return;
      if (!screenfull.isEnabled) {
        this.$message({ message: '您的浏览器不支持全屏', type: 'warning' })
        return;
      }
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    },
    onClickLayersToggle(){
      this.isLayersShownInController = !this.isLayersShownInController;
    },
    onChangeScreenFull(e) {
      console.log(e);
      this.isFullScreen = screenfull.isFullscreen;
    },
    handleFullScreenChange() {
      const isFullScreen = document.fullscreenElement || document.webkitFullscreenElement;
      if (isFullScreen) {
        // 进入全屏模式时的处理逻辑
        this.$store.dispatch('app/toggleSideBarHide', true);
        document.querySelector('.navbar').style.display = "none"
        document.querySelector('.tags-view-container').style.display = "none"
      } else {
        // 退出全屏模式时的处理逻辑
        this.$store.dispatch('app/toggleSideBarHide', false);
        document.querySelector('.navbar').style.display = ""
        document.querySelector('.tags-view-container').style.display = ""
      }
    },
    onClickResetView() {
      K.setViewToHebei();
    },

  }
}
</script>

<style scoped>
  #map {
    width: 100%;
    height: 100%;
  }
  #app-container {
    width: 100%;
    height: calc(100vh - 84px);
  }
  #app-container.fullScreen {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100vh;
    z-index: 1234;
    /* 不要超过2000，因为系统默认对话框的zIndex是2000~2030 */
    /* 不要低于1000，因为框架包裹的zIndex是930~1000 */
  }
  .controller {
    background-color: #c0c0c0c0;
    border-radius: 8px;
    padding: 8px;
    position: absolute;
    z-index: 401;
    top: 16px;
    left: 16px;
  }
  .controllerView {
    background-image: url('../../assets/onemap/reset.png');
    /* background-color: #c0c0c0c0; */
    background-repeat: round;
    position: absolute;
    z-index: 401;
    bottom: 32px;
    left: 16px;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid #ddd;;
  }

  .marker-icon {
    transition: transform 0.3s; /* 添加平滑过渡效果 */
  }
  .panel {
    position: absolute;
    background: #020F22;
    box-shadow: 0px 0px 18px 0px #0A5075, inset 0px 0px 35px 0px #4F91B3;
    border-radius: 4px 4px 4px 4px;
    border: 2px solid #0273AE;
  }
  .panel-header {
    top: 100px;

    width: 267px;
    height: 143px;
  }
  .panel-header-1 {
    left: 100px;
  }
  .panel-header-2 {
    left: 250px;
  }
  .panel-header-3 {
    left: 400px;
  }
  .panel-header-4 {
    left: 550px;
  }
</style>
