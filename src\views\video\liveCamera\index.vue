<template>
  <div class="app-container" v-cloak>
    <el-row :gutter="10" style="margin: 10px;">

      <el-col :sm="6">
        <my-tree ref="myTree" :deptList="deptList" :deptTreeProps="deptTreeProps" nodeKey="deptId"
          :highlight-current="true" :deptExpandedKeys="deptExpandedKeys" @handleLefTreeClick="handleLefTreeClick">
          <template v-slot:default="slotProps">
            <span style="font-size: 14px;color: #606266;"> <i
                :class="slotProps[':data'].type == 'sand' ? 'el-icon-s-home' : 'el-icon-folder-opened'"></i>
              {{ slotProps[':node'].label }}</span>
          </template>
        </my-tree>
      </el-col>

      <el-col :sm="18">
        <el-card style="height: 80vh">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            @submit.native.prevent>
            <el-form-item label="摄像头名称" prop="name" label-width="85px">
              <el-input clearable v-model.trim="queryParams.name" placeholder="请输入摄像头名称"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="接入平台序列号" prop="platformSn" label-width="120px">
              <el-input clearable v-model.trim="queryParams.platformSn" placeholder="请输入接入平台序列号"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
                v-hasPermi="['video:liveCamera:save']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                v-hasPermi="['video:liveCamera:update']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                v-hasPermi="['video:liveCamera:delete']">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
          </el-row>
          <!--          <my-table url="/video/liveCamera/page" ref="liveCameraTable" row-key="id" @my-selection-change="handleSelectionChange" :minWidth="120"  :fixed="true">-->
          <my-table :url="'/video/liveCamera/page/' + dept.deptCode" ref="liveCameraTable" row-key="id" maxHeight="60vh"
            @my-selection-change="handleSelectionChange" :minWidth="120" :fixed="true">
            <el-table-column header-align="center" align="left" label="摄像头名称" min-width="120" prop="name"
              sortable="custom" column-key="NAME"></el-table-column>
            <el-table-column header-align="center" align="left" label="经度" min-width="100" prop="longitude"
              sortable="custom" column-key="LONGITUDE"></el-table-column>
            <el-table-column header-align="center" align="left" label="纬度" min-width="100" prop="latitude"
              sortable="custom" column-key="LATITUDE"></el-table-column>
            <el-table-column header-align="center" align="center" label="接入平台" min-width="100" prop="platform"
              sortable="custom" column-key="PLATFORM">
              <template slot-scope="scope">
                <my-view pvalue="platform" :value="scope.row.platform"></my-view>
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="left" label="小度云地址" min-width="200" prop="appUrl"
              sortable="custom" column-key="APP_URL"></el-table-column>
            <el-table-column header-align="center" align="left" label="序列号" min-width="140" prop="platformSn"
              sortable="custom" column-key="PLATFORM_SN"></el-table-column>
            <el-table-column header-align="center" align="left" label="通道号" min-width="120" prop="channel"
              sortable="custom" column-key="CHANNEL"></el-table-column>
            <el-table-column header-align="center" align="left" label="appKey" min-width="160" prop="appKey"
              sortable="custom" column-key="APP_KEY"></el-table-column>
            <el-table-column header-align="center" align="left" label="秘钥" min-width="140" prop="appSecret"
              sortable="custom" column-key="APP_SECRET"></el-table-column>
            <el-table-column label="操作" column-key="caozuo" fixed="right" align="center" min-width="120">
              <template slot-scope="scope">
                <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-edit" title="修改"
                  @click="handleUpdate(scope.row)" v-hasPermi="['video:liveCamera:update']"></el-button>
                <el-button size="mini" type="danger" class="btn-table-operate" icon="el-icon-delete" title="删除"
                  @click="handleDelete(scope.row)" v-hasPermi="['video:liveCamera:delete']"></el-button>
              </template>
            </el-table-column>
          </my-table>

          <!-- 添加或修改摄像头表对话框 -->
          <my-dialog :title="title" :visible.sync="open" width="800px" height="70vh" append-to-body>
            <el-form ref="form" :model="liveCamera" label-width="150px">
              <el-row>
                <el-col :span="24">
                  <my-form-item label="部门" ref="deptId" prop="deptId"
                    :rules="[{ required: true, message: '所属部门不能为空' }, { required: true, validator: validatorIsSand, trigger: ['blur', 'change'] }]">
                    <treeselect :multiple="false" v-model="liveCamera.deptId" :options="deptOptions" placeholder="请选择部门"
                      :disable-branch-nodes="true" :normalizer="normalizerDept" @select="deptTreeSelect"
                      :noResultsText='noResultsText'>
                      <label slot="option-label"
                        slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                        :class="labelClassName">
                        <i :class="node.raw.type == 'sand' ? 'el-icon-s-home' : 'el-icon-folder-opened'"></i>{{
                          node.label }}
                      </label>
                    </treeselect>
                  </my-form-item>
                </el-col>
              </el-row>
              <my-form-item label="摄像头名称" ref="name" prop="name" :rules="[{ notNull: true, message: '请输入摄像头名称' }]">
                <my-input v-model="liveCamera.name" placeholder="请输入摄像头名称" :maxlength="128" />
              </my-form-item>
              <my-form-item label="经度" ref="longitude" prop="longitude"
                :rules="[{ required: false, message: '请输入经度' }, { isNumber: true, message: '只能是数字', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.longitude" placeholder="请输入经度" :maxlength="15" />
              </my-form-item>
              <my-form-item label="纬度" ref="latitude" prop="latitude"
                :rules="[{ required: false, message: '请输入纬度' }, { isNumber: true, message: '只能是数字', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.latitude" placeholder="请输入纬度" :maxlength="15" />
              </my-form-item>
              <my-form-item label="接入平台" ref="platform" prop="platform"
                :rules="[{ notNull: true, message: '请输入接入平台' }]">
                <my-select id="type" pvalue="platform" v-model="liveCamera.platform" placeholder="请输入接入平台" />
              </my-form-item>
              <my-form-item v-if="liveCamera.platform == 'xiaodu'" label="小度云地址" ref="appUrl" prop="appUrl"
                :rules="[{ notNull: true, message: '请输入小度云地址' }]">
                <my-input v-model="liveCamera.appUrl" placeholder="请输入小度云地址" />
              </my-form-item>
              <my-form-item v-if="liveCamera.platform != 'xiaodu'" label="接入序列号" ref="platformSn" prop="platformSn"
                :rules="[{ notNull: true, message: '请输入接入平台序列号' }, { pattern: /^[A-Za-z0-9]+$/, message: '只能是数字字母', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.platformSn" placeholder="请输入接入平台序列号" :maxlength="128" />
              </my-form-item>
              <my-form-item v-if="liveCamera.platform != 'tieTa' && liveCamera.platform != 'xiaodu'" label="通道号"
                ref="channel" prop="channel"
                :rules="[{ notNull: true, message: '请输入设备通道号' }, { pattern: /^[A-Za-z0-9]+$/, message: '只能是数字字母', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.channel" placeholder="请输入设备通道号" :maxlength="20" />
              </my-form-item>
              <my-form-item v-if="liveCamera.platform != 'xiaodu'" label="appKey" ref="appKey" prop="appKey"
                :rules="[{ notNull: true, message: '请输入视频平台appKey' }, { pattern: /^[A-Za-z0-9]+$/, message: '只能是数字字母', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.appKey" placeholder="请输入视频平台appKey" :maxlength="128" />
              </my-form-item>
              <my-form-item v-if="liveCamera.platform != 'xiaodu'" label="秘钥" ref="appSecret" prop="appSecret"
                :rules="[{ notNull: true, message: '请输入视频平台秘钥' }, { pattern: /^[A-Za-z0-9]+$/, message: '只能是数字字母', trigger: ['blur', 'change'] }]">
                <my-input v-model="liveCamera.appSecret" placeholder="请输入视频平台秘钥" :maxlength="128" />
              </my-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </my-dialog>
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script>
import { getLiveCamera, delLiveCamera, delLiveCameraBatch, addLiveCamera, updateLiveCamera, getDeptTreeData } from "@/api/video/liveCamera";
import { getDeptTreeDataForUser } from '@/api/system/dept';
import Treeselect from '@riophae/vue-treeselect';
import MyTree from '@/components/YB/MyTree.vue'
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { handleTree } from '@/utils/ruoyi'
import Template from '@/views/sms/template/index.vue'
export default {
  name: "LiveCamera",
  components: { Template, Treeselect, MyTree },
  data() {
    return {
      deptCode: '',
      sandType: 'sand',
      type: '',
      filterText: '',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: '',
        platformSn: ''
      },
      // 表单参数
      liveCamera: {},
      deptList: [],
      deptTreeProps: {
        children: 'children',
        label: 'name'
      },
      deptExpandedKeys: [],  //展开机构树
      dept: {
        deptId: '000_013',  //默认deptId
        areaCode: '',
        areaName: '',
        parentName: '',
        parentId: '-1',
        deptCode: '',
        parentCode: '',
        complaintMobile: '',
        queryMobile: '',
        imgList: [],
        simplifiedCode: '',
      },
      // 部门树选项
      deptOptions: [],
      normalizerDept(node) {
        return {
          id: node.deptId,
          label: node.name,
          children: node.children,
          type: node.type,
        };
      },
      noResultsText: '未找到结果',
    };
  },
  mounted() {
    //加载机构树
    this.getDeptTreeData()
    this.getDeptTreeselect()
  },
  watch: {
    filterText(val) {
      this.$refs.leftTree.filter(val);
    }
  },
  methods: {
    validatorIsSand(rule, value, callback) {
      if (this.type !== 'sand') {
        callback(new Error("所属部门必须为项目"));
      } else {
        callback();
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getDeptTreeData() {
      getDeptTreeData().then(r => {
        this.deptList = handleTree(r.deptList, 'deptId', 'parentId')
        var that = this
        this.$nextTick(() => {
          if (that.dept.deptId) {
            that.$refs.myTree.$refs.leftTree.setCurrentKey(that.dept.deptId)
            let node = that.$refs.myTree.$refs.leftTree.getNode(that.dept.deptId)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
          }
        })
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTreeselect() {
      getDeptTreeDataForUser().then(response => {
        this.deptOptions = handleTree(response.deptList, 'deptId', 'parentId');
      });
    },
    /** 选中部门回调事件 */
    deptTreeSelect(node) {
      this.liveCamera.deptId = node.deptId;
      this.liveCamera.deptCode = node.deptCode;
      this.type = node.type;
    },
    /** 查询摄像头表列表 */
    reload(restart) {
      //this.getDeptTreeData()
      this.$refs.liveCameraTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.liveCamera = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        deptId: null,
        deptCode: '',
        name: '',
        longitude: null,
        latitude: null,
        platform: '',
        platformSn: '',
        channel: '',
        appKey: '',
        appSecret: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // this.dept.deptCode = '0001'
      // this.dept.deptId = '000_013'
      this.$refs.liveCameraTable.search(this.queryParams, false);
      this.single = true;
      this.multiple = true;

    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.liveCameraTable.getSelectRowKeys()
      this.single = this.ids.length != 1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加摄像头";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.type = 'sand'
      const id = row.id || this.ids[0];
      getLiveCamera(id).then(r => {
        this.liveCamera = r.liveCamera;
        this.open = true;
        this.title = "修改摄像头";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.liveCamera.platform == 'tieTa') {
            this.liveCamera.channel = '';
          }
          if (this.liveCamera.id) {
            updateLiveCamera(this.liveCamera).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addLiveCamera(this.liveCamera).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        } else {
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that = this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function () {
        if (row.id) {
          return delLiveCamera(row.id);
        } else {
          return delLiveCameraBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handleLefTreeClick(data, treeNode, nodeObj) {
      this.dept = JSON.parse(JSON.stringify(data))
      this.deptExpandedKeys = [this.dept.deptId]
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.liveCameraTable.changeTableHeight();
  },
};
</script>
