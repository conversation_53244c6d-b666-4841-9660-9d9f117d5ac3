<template>
	<div class="app-container">
		<div class="tip">运砂量筛选合计：{{ totalVehicleLoad }} 吨</div>
		<el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
			<el-form-item label="采运管理单编号" prop="billNo">
				<el-input clearable v-model.trim="queryParams.billNo" placeholder="请输入采运管理单编号"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目名称" prop="proName">
        <el-select v-model="queryParams.proName"
                   placeholder="请选择项目名称"
                   clearable
                   filterable
                   @change="selectProject"
                   @clear="clearProject"
        >
          <el-option v-for="item in projectNameList" :label="item.name" :value="item.id" :key="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="标段名称" :style="showSection?'visibility:visible':'visibility:hidden'" prop="sectionName">
        <my-select
          v-model="queryParams.sectionName"
          :options="sectionNameList"
          item-value="sectionId"
          item-name="sectionName"
          placeholder="请选择标段名称"
          @change="selectSection"
          @clear="clearSection"
        />
      </el-form-item>
			<el-form-item label="项目类型" prop="type">
				<my-select v-model="queryParams.type" pvalue="projectType" placeholder="请选择项目类型">
				</my-select>
			</el-form-item>
			<el-form-item label="卸砂地点" prop="destination">
				<el-input clearable v-model.trim="queryParams.destination" placeholder="请输入卸砂地点"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item label="运砂人" prop="driverName">
				<el-input clearable v-model.trim="queryParams.driverName" placeholder="请输入运砂人"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item label="车牌号码" prop="carNumber">
				<el-input clearable v-model.trim="queryParams.carNumber" placeholder="请输入车牌号码"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<my-select v-model="queryParams.status" pvalue="billStatus" laceholder="请选择状态">
				</my-select>
			</el-form-item>
			<el-form-item label="磅站名称" prop="stationName">
				<el-input clearable v-model.trim="queryParams.stationName" placeholder="请输入磅站名称"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item label="车辆所属单位" prop="companyName">
				<el-input clearable v-model.trim="queryParams.companyName" placeholder="请输入名称"
					@keyup.enter.native="handleQuery" />
			</el-form-item>
			<el-form-item label="开单时间" prop="signTime">
				<!--        <el-date-picker v-model="signTime" :clearable="false" type="datetimerange" range-separator="-"-->
				<!--          :default-time="['00:00:00','23:59:59']" value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"-->
				<!--          start-placeholder="年/月/日 时:分:秒" end-placeholder="年/月/日 时:分:秒" @change="handleSignTime">-->
				<!--        </el-date-picker>-->

				<div style="display: flex; align-items: center; justify-content: center">
					<el-date-picker v-model="queryParams.signStartDateTime" type="datetime" :editable="false"
						value-format="yyyy-MM-dd HH:mm" format="yyyy-MM-dd HH:mm" :clearable="false"
						popper-class="myDatePicker" :picker-options="pickerStartDate" placeholder="开始日期"
						style="width: 170px">
					</el-date-picker>
					<div style="padding: 0px 10px">-</div>
					<el-date-picker v-model="queryParams.signEndDateTime" type="datetime" :clearable="false"
						value-format="yyyy-MM-dd HH:mm" format="yyyy-MM-dd HH:mm" popper-class="myDatePicker"
						:picker-options="pickerEndDate" placeholder="结束日期" style="width: 170px">
					</el-date-picker>
				</div>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索
				</el-button>
				<el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
				</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<!--      <el-col :span="1.5">-->
			<!--        <el-button-->
			<!--          type="primary"-->
			<!--          icon="el-icon-plus"-->
			<!--          size="mini"-->
			<!--          @click="handleAdd"-->
			<!--          v-hasPermi="['bill:bill:save']"-->
			<!--        >新增</el-button>-->
			<!--      </el-col>-->
			<!--      <el-col :span="1.5">-->
			<!--        <el-button-->
			<!--          type="success"-->
			<!--          icon="el-icon-edit"-->
			<!--          size="mini"-->
			<!--          :disabled="single"-->
			<!--          @click="handleUpdate"-->
			<!--          v-hasPermi="['bill:bill:update']"-->
			<!--        >修改</el-button>-->
			<!--      </el-col>-->
			<el-col :span="1.5">
				<el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
					v-hasPermi="['bill:bill:delete']">删除</el-button>
			</el-col>
			<el-col :span="1.5">
				<el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
					v-hasPermi="['bill:bill:export']">导出
				</el-button>
			</el-col>
			<right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
		</el-row>

		<my-table url="/bill/bill/page" ref="billTable" row-key="id" cell-class-name="el-table-max-cell2"
			:auto-request="false" @my-selection-change="handleSelectionChange" :fixed="true">
			<el-table-column label="采运管理单编号" header-align="center" align="center" min-width="150" prop="billNo"
				sortable="custom" column-key="bill.BILL_NO" fixed="left">
			</el-table-column>
			<el-table-column label="项目名称" header-align="center" align="left" min-width="200" prop="name"
				sortable="custom" column-key="project.NAME">
				<template #default="scope">
					<el-tooltip :content="scope.row.name" placement="top" effect="light">
						<div>{{ scope.row.name }}</div>
					</el-tooltip>
				</template>
			</el-table-column>
			<el-table-column label="标段名称" align="left" header-align="center" min-width="160" prop="sectionName"
				sortable="custom" column-key="project.SECTION_NAME">
			</el-table-column>
			<el-table-column label="项目位置" header-align="center" align="left" min-width="140" prop="areaName"
				sortable="custom" column-key="project.AREA_NAME"></el-table-column>
			<el-table-column label="项目类型" header-align="center" align="center" min-width="150" sortable="custom"
				column-key="project.TYPE">
				<template #default="scope">
					<my-view pvalue="projectType" :value="scope.row.projectType"></my-view>
				</template>
			</el-table-column>
			<!-- <el-table-column label="公司名称" align="left" header-align="center" min-width="160" prop="companyName"
        sortable="custom" column-key="project.COMPANY_NAME">
      </el-table-column> -->
			<el-table-column label="车牌号码" header-align="center" align="left" min-width="120" prop="carNumber"
				sortable="custom" column-key="bill.CAR_NUMBER"></el-table-column>
			<el-table-column label="个人/单位" header-align="center" align="center" min-width="120" prop="carCompany"
				sortable="custom" column-key="bill.CAR_COMPANY">
				<template #default="scope">
					{{ scope.row.carCompany ? scope.row.carCompany : "个人" }}
				</template>
			</el-table-column>
			<el-table-column label="运砂人" header-align="center" align="center" min-width="90" prop="driverName"
				sortable="custom" column-key="bill.DRIVER_NAME"></el-table-column>
			<el-table-column label="手机号码" header-align="center" align="center" min-width="110" prop="driverMobile"
				sortable="custom" column-key="bill.DRIVER_MOBILE"></el-table-column>
			<el-table-column label="实际载运量(吨)" header-align="center" align="center" min-width="140" prop="vehicleLoad"
				sortable="custom" column-key="bill.VEHICLE_LOAD">
				<template #default="scope">
					{{ common.toThousands(scope.row.vehicleLoad, 2, ",") }}
				</template>
			</el-table-column>
			<el-table-column label="卸砂地点" header-align="center" align="left" min-width="180" prop="destination"
				sortable="custom" column-key="bill.DESTINATION"></el-table-column>
			<el-table-column label="开单日期" header-align="center" align="center" min-width="160" prop="signTime"
				sortable="custom" column-key="bill.SIGN_TIME">
				<template #default="scope">
					<span>{{
            parseTime(scope.row.signTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
				</template>
			</el-table-column>
			<el-table-column label="状态" header-align="center" align="center" min-width="100" prop="status"
				sortable="custom" column-key="bill.STATUS">
				<template #default="scope">
					<el-tag :type="getClass(scope.row.status)" disable-transitions>{{
            getName(scope.row.status)
          }}</el-tag>
					<!-- <my-view pvalue="billStatus" :value="scope.row.status"></my-view> -->
				</template>
			</el-table-column>
			<el-table-column label="磅站名称" header-align="center" align="left" min-width="160" prop="stationName"
				sortable="custom" column-key="station.NAME "></el-table-column>
			<el-table-column label="操作" header-align="center" min-width="200" column-key="caozuo" fixed="right"
				align="center">
				<template slot-scope="scope">
					<el-button size="mini" type="danger" @click="handleViewNow(scope.row)"
						v-hasPermi="['client:billAdmin:billAdmin']"
						v-if="scope.row.status == 'complaint'">审核</el-button>
					<el-button size="mini" type="danger" class="btn-table-operate" icon="el-icon-location-information"
						title="核销地审核" @click="handleViewNow(scope.row, '核销地审核')" v-hasPermi="['bill:bill:redeem']"
						v-if="scope.row.status == 'pendingReview'"></el-button>
					<!--          <el-button size="mini" type="danger" @click="handleViewNow(scope.row)" v-if="scope.row.auditStatus==2&&state==0">审核</el-button>
          <div>{{state}}</div> -->
					<el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-document-copy"
						title="查看详情" @click="handleView(scope.row)" v-hasPermi="['bill:bill:info']">
					</el-button>
					<el-button v-if="scope.row.status != 'waitingAduit'" size="mini" type="success"
						class="btn-table-operate" icon="el-icon-download" title="下载单据"
						@click="handleDownload(scope.row)" v-hasPermi="['bill:bill:download']">
					</el-button>
					<el-button size="mini" title="删除" class="btn-table-operate" type="danger" icon="el-icon-delete"
						@click="handleDelete(scope.row)" v-hasPermi="['bill:bill:delete']">
					</el-button>
				</template>
			</el-table-column>
		</my-table>
		<el-dialog :title="title5" :visible.sync="open5" width="800px" append-to-body>
			<div>
				<!-- <div class="demo-input-suffix">
          <div class="istext">
            申述类型:
          </div>
          <el-input
            placeholder="请输入内容"
            v-model="sessionList.causeOfAppeal" :readonly="true">
          </el-input>
        </div> -->
				<div class="demo-input-suffix">
					<div class="istext">详细描述:</div>
					<el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="sessionList.detailDescribe"
						:readonly="true">
					</el-input>
				</div>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" class="xianyan" @click="submitAppeal(3)">通 过</el-button>
				<el-button type="danger" class="xianyan" @click="submitAppeal(4)">驳 回</el-button>
				<el-button class="xianyan" @click="cancelOpen">关 闭</el-button>
			</div>
		</el-dialog>
		<el-dialog :title="title6" :visible.sync="open6" width="800px" append-to-body>
			<div>
				<div class="demo-input-suffix">
					<div class="istext">车牌号:</div>
					<el-input placeholder="请输入内容" v-model="sFrom.carNumber" :readonly="true">
					</el-input>
				</div>
				<div class="demo-input-suffix">
					<div class="istext">卸砂地信息:</div>
					<el-input placeholder="请输入内容" v-model="sFrom.destination" :readonly="true">
					</el-input>
				</div>
				<!-- 一行显示：纬度 + 经度 -->
				<!-- <div class="geo-info">
          <span class="geo-label">纬度</span>
          <span class="geo-value">{{ sFrom.destLongitude }}</span>

          <span class="geo-label">经度</span>
          <span class="geo-value">{{ sFrom.destLongitude }}</span>
        </div> -->

				<!--  -->
				<div class="demo-input-suffix" style="display: flex; align-items: center">
					<div class="istext">纬度:</div>
					<span class="geo-value">{{ sFrom.destLatitude }}</span>
					<!-- <el-input
            placeholder="请输入内容"
            v-model="sFrom.destLatitude"
            :readonly="true"
            style="width: 120px; margin-right: 16px"
          >
          </el-input> -->

					<div class="istext" style="margin-left: 20px;">经度:</div>
					<span class="geo-value">{{ sFrom.destLongitude }}</span>
					<!-- <el-input
            placeholder="请输入内容"
            v-model="sFrom.destLongitude"
            :readonly="true"
            style="width: 120px"
          >
          </el-input> -->
				</div>
				<!-- <div class="demo-input-suffix">
          <div class="istext">纬度:</div>
          <el-input
            placeholder="请输入内容"
            v-model="sFrom.destLatitude"
            :readonly="true"
          >
          </el-input>
        </div>
        <div class="demo-input-suffix">
          <div class="istext">经度:</div>
          <el-input
            placeholder="请输入内容"
            v-model="sFrom.destLongitude"
            :readonly="true"
          >
          </el-input> -->
				<!-- </div> -->
				<div class="demo-input-suffix" style="margin-top: 20px">
					<div class="istext">详细描述:</div>
					<el-input type="textarea" :rows="5" placeholder="请输入内容" v-model="sFrom.causeOfAppeal"
						:readonly="true">
					</el-input>
				</div>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" class="xianyan" @click="submitAppeal1(3)">通 过</el-button>
				<el-button type="danger" class="xianyan" @click="submitAppeal1(4)">驳 回</el-button>
				<el-button class="xianyan" @click="cancelOpen2">关 闭</el-button>
			</div>
		</el-dialog>
		<!-- 添加或修改运砂单据表对话框 -->
		<my-dialog :title="title" :visible.sync="open" height="70vh" width="800px" append-to-body>
			<el-form ref="form" :model="bill" label-width="180px" disabled>
				<el-row>
					<el-col :span="24">
						<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
							单据信息
						</h2>
					</el-col>
					<el-col :span="12">
						<my-form-item label="采运管理单编号:" ref="billNo" prop="billNo" style="margin-bottom: 0">
							{{ bill.billNo }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="单据状态:" ref="status" prop="status" style="margin-bottom: 0">
							<my-view style="text-align: start" pvalue="billStatus" :value="bill.status"></my-view>
						</my-form-item>
					</el-col>

					<el-col :span="24">
						<el-divider></el-divider>
					</el-col>

					<el-col :span="24">
						<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
							项目信息
						</h2>
					</el-col>
					<el-col :span="12">
						<my-form-item label="项目名称:" ref="name" prop="name" style="margin-bottom: 0">
							{{ bill.name }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="项目位置:" ref="areaName" prop="areaName" style="margin-bottom: 0">
							{{ bill.areaName }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item class="special" label="采砂许可证/弃砂审批文号:" ref="licenseNo" prop="licenseNo"
							style="margin-bottom: 0">
							{{ bill.licenseNo }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="采砂人（供砂人）:" ref="leaderName" prop="leaderName" style="margin-bottom: 0">
							{{ bill.leaderName }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="联系电话:" ref="contact" prop="contact" style="margin-bottom: 0">
							{{ bill.contact }}
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="磅站名称:" ref="stationName" prop="stationName" style="margin-bottom: 0">
							{{ bill.stationName }}
						</my-form-item>
					</el-col>

					<el-col :span="24">
						<el-divider></el-divider>
					</el-col>


         <el-col :span="24">
         						<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
         							运输信息
         						</h2>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="运砂车品牌型号:" ref="brand" prop="carBrand" style="margin-bottom: 0">
         							{{ bill.carBrand }}
         						</my-form-item>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="车牌号码:" ref="carNumber" prop="carNumber" style="margin-bottom: 0">
         							{{ bill.carNumber }}
         						</my-form-item>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="运砂人:" ref="driverName" prop="driverName" style="margin-bottom: 0">
         							{{ bill.driverName }}
         						</my-form-item>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="联系电话:" ref="driverMobile" prop="driverMobile" style="margin-bottom: 0">
         							{{ bill.driverMobile }}
         						</my-form-item>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="实际载运量(吨):" ref="vehicleLoad" prop="vehicleLoad" style="margin-bottom: 0">
         							{{ bill.vehicleLoad }}
         						</my-form-item>
         					</el-col>
                  <el-col v-if="bill.inWeight!=null&&bill.inWeight>0" :span="12">
                    <my-form-item label="皮重(吨):" ref="inWeight" prop="inWeight" style="margin-bottom: 0">
                      {{ bill.inWeight }}
                    </my-form-item>
                  </el-col>
                  <el-col v-if="bill.outWeight!=null&&bill.outWeight>0" :span="12">
                    <my-form-item label="毛重(吨):" ref="outWeight" prop="outWeight" style="margin-bottom: 0">
                      {{ bill.outWeight }}
                    </my-form-item>
                  </el-col>
         					<el-col :span="12">
         						<my-form-item label="起运时间:" ref="signTime" prop="signTime" style="margin-bottom: 0">
         							<span>{{ parseTime(bill.signTime, "{y}-{m}-{d} {h}:{i}") }}</span>
         						</my-form-item>
         					</el-col>
         					<el-col :span="12">
         						<my-form-item label="预计到达时间:" ref="estimatedArrivalTime" prop="estimatedArrivalTime"
         							style="margin-bottom: 0">
         							<span>{{
                         parseTime(bill.estimatedArrivalTime, "{y}-{m}-{d} {h}:{i}")
                       }}</span>
         						</my-form-item>
         					</el-col>

					<el-col :span="24">
						<my-form-item label="卸砂地点:" ref="destination" prop="destination" style="margin-bottom: 0">
							<span>{{ bill.destination }}</span>
						</my-form-item>
					</el-col>
					<el-col :span="24" v-if="showInImg">
						<my-form-item label="入场照片:" style="margin-bottom: 0">
							<el-image v-for="(item, index) in inImgList" :key="item.id" title="查看照片"
								class="currentPicture" :src="item.thumbnailPath" fit="cover"
								@click="handleViewPicture(inImgList, index)" />
						</my-form-item>
					</el-col>

					<el-col :span="24">
						<my-form-item :label="showInImg ? '出场照片:' : '现场照片:'" style="margin-bottom: 0">
							<el-image v-for="(item, index) in imgList" :key="item.id" title="查看照片"
								class="currentPicture" :src="item.thumbnailPath" fit="cover"
								@click="handleViewPicture(imgList, index)" />
						</my-form-item>
					</el-col>

					<el-col :span="24">
						<el-divider></el-divider>
					</el-col>

					<el-col :span="24">
						<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
							签字审核
						</h2>
					</el-col>
					<el-col :span="12">
						<my-form-item label="承运人:" ref="driverSignatureCode" prop="driverSignatureCode"
							style="margin-bottom: 0">
							<el-image class="signature" :src="bill.driverSignatureCode" fit="cover" />
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="经办人:" ref="projectLeaderSignatureCode" prop="projectLeaderSignatureCode"
							style="margin-bottom: 0">
							<el-image class="signature" :src="bill.projectLeaderSignatureCode" fit="cover" />
						</my-form-item>
					</el-col>
					<el-col :span="12">
						<my-form-item label="采砂现场监管人员:" ref="supervisorSignatureCode" prop="supervisorSignatureCode"
							style="margin-bottom: 0">
							<el-image class="signature" :src="bill.supervisorSignatureCode" fit="cover" />
						</my-form-item>
					</el-col>
					<!--          <el-col :span="12" v-if="bill.status!=='waitingAduit'">-->
					<!--            <my-form-item label="水行政主管部门:" ref="authoritySignatureCode" prop="authoritySignatureCode"-->
					<!--                          style="margin-bottom: 0">-->
					<!--              <el-image class="signature" :src="bill.authoritySignatureCode" fit="cover"/>-->
					<!--            </my-form-item>-->
					<!--          </el-col>-->

					<div v-if="bill.status === 'completed'">
						<el-col :span="24">
							<el-divider></el-divider>
						</el-col>

						<el-col :span="24">
							<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
								核销信息
							</h2>
						</el-col>
						<el-col :span="24">
							<my-form-item label="核销地点:" ref="verifyCancelAddress" prop="verifyCancelAddress"
								style="margin-bottom: 0">
								{{ bill.verifyCancelAddress }}
								<a @click="showVerifyCancelMap"><img height="23px"
										src="@/assets/icons/map/marker.png" /></a>
							</my-form-item>
						</el-col>
						<el-col :span="12">
							<my-form-item label="核销经度:" ref="verifyCancelLongitude" prop="verifyCancelLongitude"
								style="margin-bottom: 0">
								{{ bill.verifyCancelLongitude }}
							</my-form-item>
						</el-col>
						<el-col :span="12">
							<my-form-item label="核销纬度:" ref="verifyCancelLatitude" prop="verifyCancelLatitude"
								style="margin-bottom: 0">
								{{ bill.verifyCancelLatitude }}
							</my-form-item>
						</el-col>
						<el-col :span="12">
							<my-form-item label="卸砂负责人:" ref="destLeader" prop="destLeader" style="margin-bottom: 0">
								{{ bill.destLeader }}
							</my-form-item>
						</el-col>
						<el-col :span="12">
							<my-form-item label="负责人电话:" ref="destLeaderMobile" prop="destLeaderMobile"
								style="margin-bottom: 0">
								{{ bill.destLeaderMobile }}
							</my-form-item>
						</el-col>
						<el-col :span="24">
							<my-form-item label="签字:" ref="verifyCancelSignatureCode" prop="verifyCancelSignatureCode"
								style="margin-bottom: 0">
								<el-image class="signature" :src="bill.verifyCancelSignatureCode" fit="cover" />
							</my-form-item>
						</el-col>
						<el-col :span="24">
							<my-form-item label="现场照片:" style="margin-bottom: 0">
								<el-image v-for="(item, index) in verifyImgList" :key="item.id" title="查看照片"
									class="currentPicture" :src="item.thumbnailPath" fit="cover"
									@click="handleViewPicture(verifyImgList, index)" />
							</my-form-item>
						</el-col>
					</div>
					<div v-if="bill.causeOfAppeal">
						<el-col :span="24">
							<el-divider></el-divider>
						</el-col>

						<el-col :span="24">
							<h2 style="border-left: 5px solid #5297f7; padding-left: 5px">
								申述记录
							</h2>
						</el-col>
						<el-col :span="24">
							<my-form-item label="申述类型:" style="margin-bottom: 0">
								{{ bill.causeOfAppeal }}
							</my-form-item>
						</el-col>
						<el-col :span="12">
							<my-form-item label="申述原因:" style="margin-bottom: 0">
								{{ bill.detailDescribe }}
							</my-form-item>
						</el-col>
					</div>
				</el-row>
			</el-form>
		</my-dialog>

		<!--    卸砂地点弹出层-->
		<my-dialog title="核销地点" :visible.sync="verifyCancelOpen" width="1000px" height="600px" append-to-body
			:z-index="3000">
			<el-form ref="weighingStation" :model="bill" label-width="80px">
				<!-- 腾讯地图组件 -->
				<my-tencent-map :center="{ lat: 38.041323, lng: 114.514686 }" :zoom="12" :disabled="true"
					:switchType="true" :isClickPopup="true" :lat="Number(bill.verifyCancelLatitude)"
					:lng="Number(bill.verifyCancelLongitude)" />
			</el-form>
		</my-dialog>
		<!-- 预览图片遮罩层 -->
		<el-image-viewer style="z-index: 2050" v-if="imageVisible" :url-list="imageUrl" :on-close="
        () => {
          imageVisible = false;
        }
      " />
	</div>
</template>

<script>
	import {
		getBill,
		delBill,
		delBillBatch,
		addBill,
		updateBill,
		appealBill,
		getAllNum,
		getAppealBillData,
		appealBillSucess,
		appealBillReject,
    getProjectSection
	} from "@/api/bill/bill";
	import MyAreaSelect from "@/components/YB/MyAreaSelect";
	import common from "@/utils/common";
	import item from "@/layout/components/Sidebar/Item.vue";
	import MyTencentMap from "@/components/YB/MyTencentMap.vue";
	import DictUtils from "@/utils/dictUtils";
  import { listProjectNames } from '@/api/client/client'
	export default {
		name: "Bill",
		computed: {
			common() {
				return common;
			},
		},
		components: {
			MyTencentMap,
			MyAreaSelect,
			"el-image-viewer": () =>
				import("element-ui/packages/image/src/image-viewer"),
		},
		data() {
			return {
				sFrom: {
					carNumber: "",
					causeOfAppeal: "",
					destLatitude: "",
					destLongitude: "",
					destination: "",
				},
				totalVehicleLoad: 0,
				// 选中数组
				ids: [],
				// 非单个禁用
				single: true,
				// 非多个禁用
				multiple: true,
				// 显示搜索条件
				showSearch: true,
				// 总条数
				total: 0,
				// 弹出层标题
				title: "",
				// 是否显示弹出层
				open: false,
				// 弹出层标题
				title5: "",
				// 弹出层标题
				title6: "",
				// 是否显示弹出层
				open5: false,
				// 是否显示弹出层
				open6: false,
				// 查询参数
				queryParams: {
					billNo: "",
					destination: "",
					areaCode: "",
					status: "",
					driverName: "",
					carNumber: "",
          proName: '',
          projectIdList:[],
					areaName: "",
					sectionName: "",
					companyName: "",
					type: "",
					signStartDateTime: common.formatDate(new Date(), "yyyy-MM-01 00:00"),
					signEndDateTime: common.formatDate(new Date(), "yyyy-MM-dd 23:59"),
				},
				// 查询参数-开单时间
				signTime: [
					common.formatDate(new Date(), "yyyy-MM-01 00:00:00"),
					common.formatDate(new Date(), "yyyy-MM-dd 23:59:59"),
				],
				// 表单参数
				bill: {},
				// 现场照片
				imgList: [],
				//进场照片
				inImgList: [],
				showInImg: true,
				// 过磅单
				weighOrderImgList: [],
				// 核销现场照片
				verifyImgList: [],
				// 是否显示预览图片弹出层
				imageVisible: false,
				// 图片路径
				imageUrl: [],
				verifyCancelOpen: false,
				newbillStatus: [],
				sessionList: {
					detailDescribe: undefined,
					causeOfAppeal: undefined,
				},
				billId: "",
				state: 0,
				pickerStartDate: this.pickerStartDate1(),
				pickerEndDate: this.pickerEndDate1(),
        projectNameList:[],//项目名称下拉
        projectNameAllList:[],//项目名称下拉
        sectionNameList:[],
        showSection:true
			};
		},
    watch:{
      'queryParams.areaCode':{
        handler(newVal){
          this.queryParams.proName=''
          this.queryParams.projectIdList = []
          this.queryParams.sectionName = ''
          this.sectionNameList = []
          if(newVal){
            if(newVal.length<=11){
              this.projectNameList= this.projectNameAllList.filter(i=>i.areaCode.includes(newVal))
            }
            if(newVal.length>=15){
              this.projectNameList= this.projectNameAllList.filter(i=>i.areaCode === newVal)
            }
          }else {
            this.projectNameList=this.projectNameAllList
          }
        },
        deep:true
      },
    },
		created() {
			DictUtils.cList("billStatus").then((r) => {
				this.newbillStatus = r.billStatus;
				console.log(r, "获取");
			});
			console.log(this.$store.state.user, "用户");
			let mapptrue = this.$store.state.user.rolenames.indexOf("砂场管理员") != -1;
			console.log(this.$store.state.user.deptcode, "获取");
			if (this.$store.state.user.deptcode.length == 9) {
				this.state = 1; //市级
			} else if (this.$store.state.user.deptcode.length == 14) {
				this.state = 0; //县级
			} else if (
				this.$store.state.user.deptcode.length == 19 &&
				mapptrue == true
			) {
				this.state = 4; //县下属砂管员
			} else if (this.$store.state.user.deptcode.length < 9) {
				this.state = 2; //省级
			}
		},
		mounted() {
			this.reload();
      getProjectSection().then(res => {
        this.projectNameList = res.list
        this.projectNameAllList= this.projectNameList
      })
		},
		methods: {
      selectProject(value){
        const val = this.projectNameAllList.filter(i=>i.id === value)[0]
        this.sectionNameList = []
        this.queryParams.sectionName = ''
        this.queryParams.projectIdList = []
        if(val) {
          if(val.childrenList&&val.childrenList.length == 1){
            this.queryParams.proName = val.childrenList[0].proName
            this.queryParams.projectIdList = [val.childrenList[0].sectionId]
            this.showSection = false
          }else {
            this.showSection = true
            this.sectionNameList = val.childrenList
            this.queryParams.projectIdList = val.childrenList.map(i=>i.sectionId)
          }
        }
      },
      clearProject(){
        this.queryParams.proName = ''
        this.queryParams.projectIdList =[]
        this.queryParams.sectionName = ''
        this.sectionNameList = []
        this.showSection = true
      },
      selectSection(val){
        if(val) {
          this.queryParams.projectIdList = [val]
        }
      },
      clearSection(){
        const val = this.projectNameAllList.filter(i=>i.id === this.queryParams.proName)[0]
        this.queryParams.projectIdList = val.childrenList.map(i=>i.sectionId)
      },
			pickerStartDate1() {
				let that = this;
				return {
					disabledDate(time) {
						const endData = that.queryParams.signEndDateTime.slice(0, 10);
						return (
							Date.parse(new Date(common.formatDate(time, "yyyy-MM-dd"))) >
							Date.parse(new Date(endData))
						);
					},
				};
			},
			pickerEndDate1() {
				let that = this;
				return {
					disabledDate(time) {
						const startData = that.queryParams.signStartDateTime.slice(0, 10);
						return (
							Date.parse(new Date(common.formatDate(time, "yyyy-MM-dd"))) <
							Date.parse(new Date(startData))
						);
					},
				};
			},
			getAll(data) {
				console.log(data, "传输过去的参数");
				getAllNum(data).then((res) => {
					console.log(res, "获取采运单列表");
					this.totalVehicleLoad = res.totalVehicleLoad;
				});
			},
			submitAppeal(status) {
				let data = {
					billId: this.billId,
					status: status,
				};
				appealBill(data).then((res) => {
					if (status == 3) {
						this.$modal.msgSuccess("审核通过！");
					}
					if (status == 4) {
						this.$modal.msgSuccess("驳回成功！");
					}
					this.open5 = false;
					this.billId = "";
					this.reset5();
					this.reload();
				});
			},
			submitAppeal1(status) {
				console.log(this.billId, "审核id");
				console.log(status, "审核状态");
				let data = {
					billId: this.billId,
					// status:status
				};
				if (status == 3) {
					appealBillSucess(data).then((res) => {
						this.$modal.msgSuccess("审核通过！");
						this.open6 = false;
						this.reload(true);
					});
				} else if (status == 4) {
					appealBillReject(data).then((res) => {
						this.$modal.msgSuccess("驳回成功！");
						this.open6 = false;
						this.reload(true);
					});
				}
			},
			//获取运单状态名称
			getName(status) {
				let item = this.newbillStatus.find((v) => v.enName === status);
				return item ? item.name : "未知状态"; // 兜底处理
			},
			getClass(status) {
				if (status == "inTransit") {
					return "success";
				} else if (status == "completed") {
					return "warning";
				} else if (status == "abnormal") {
					return "danger";
				} else if (status == "complaint") {
					return "danger";
				} else {
					return "warning";
				}
			},
			//打开显示卸砂地点的地图
			showVerifyCancelMap() {
				this.verifyCancelOpen = true;
			},
			/** 查询运砂单据表列表 */
			reload(restart) {
				let tempParams = JSON.parse(JSON.stringify(this.queryParams));
				tempParams.signStartDateTime = tempParams.signStartDateTime + ":00";
				tempParams.signEndDateTime = tempParams.signEndDateTime + ":59";
				this.$refs.billTable.search(tempParams, restart);
				this.getAll(tempParams);
				this.single = true;
				this.multiple = true;
			},
			/** 取消按钮 */
			cancelOpen() {
				this.open5 = false;
				this.reset5();
			},
			cancelOpen2() {
				this.open6 = false;
				// this.reset5();
			},
			/** 取消按钮 */
			cancel() {
				this.open = false;
				this.reset();
			},
			reset5() {
				this.sessionList = {
					detailDescribe: undefined,
					causeOfAppeal: undefined,
				};
			},
			/** 表单重置 */
			reset() {
				this.bill = {
					type: "",
					areaName: "",
					name: "",
					id: "",
					billNo: "",
					projectId: "",
					carNumber: "",
					carCompany: "",
					carBrand: "",
					carOwnerName: "",
					carKerbWeight: "",
					carMaxPayload: "",
					driverName: "",
					driverMobile: "",
					vehicleLoad: "",
					destination: "",
					destLongitude: null,
					destLatitude: null,
					estimatedArrivalTime: "",
					destLeader: "",
					destLeaderMobile: "",
					signTime: "",
					auditTime: "",
					status: "",
					driverSignatureId: "",
					projectLeaderSignatureId: "",
					supervisorSignatureId: "",
					authoritySignatureId: "",
					authorityId: "",
					verifyCancelUserId: "",
					verifyCancelTime: "",
					verifyCancelLongitude: null,
					verifyCancelLatitude: null,
					distance: null,
					inDataId: "",
					outDataId: "",
					inWeight: null,
					outWeight: null,
				};
				this.imgList = [];
				this.showInImg = true;
				this.inImgList = [];
				this.verifyImgList = [];
				this.weighOrderImgList = [];
				this.resetForm("form");
			},

			/** 搜索按钮操作 */
			handleQuery() {
				this.reload(true);
			},

			/** 重置按钮操作 */
			resetQuery() {
				this.resetForm("queryForm");
        this.queryParams.projectIdList = []
				this.queryParams.signStartDateTime = common.formatDate(
					new Date(),
					"yyyy-MM-01 00:00"
				);
				this.queryParams.signEndDateTime = common.formatDate(
					new Date(),
					"yyyy-MM-dd 23:59"
				);
				console.log(this.queryParams.signEndDateTime);
				this.handleQuery();
			},

			/** 多选框选中数据 */
			handleSelectionChange(selection) {
				this.ids = this.$refs.billTable.getSelectRowKeys();
				this.single = this.ids.length !== 1;
				this.multiple = !this.ids.length;
			},

			handleViewNow(row, type) {
				console.log(type, "类型");
				if (type == "核销地审核") {
					this.title6 = "核销地修改审核";
					this.billId = row.id;
					this.open6 = true;
					getAppealBillData({
						billId: row.id
					}).then((res) => {
						console.log(res);
						this.sFrom = res.appeals;
					});
					return;
				}
				console.log(row, "信息");
				this.billId = row.id;
				this.reset();
				this.title5 = "申述审核";

				getBill(row.id).then((res) => {
					console.log(res.bill);
					this.sessionList = res.bill;
					this.open5 = true;
				});
			},
			/** 任务详细信息 */
			handleView(row) {
				console.log(row, "信息");
				this.reset();
				this.title = "查看采运管理单详情";
				getBill(row.id).then((res) => {
					this.bill = res.bill;
					this.imgList = res.imgList;
					if (res.inImgList && res.inImgList.length > 0) {
						this.inImgList = res.inImgList;
						this.showInImg = true;
					} else {
						this.showInImg = false;
					}
					this.verifyImgList = res.verifyImgList;
					this.weighOrderImgList = res.weighOrderImgList;
					this.open = true;
				});
			},

			/** 删除按钮操作 */
			handleDelete(row) {
				this.$modal
					.confirm("是否确认删除选中的数据吗？")
					.then(() => {
						if (row.id) {
							return delBill(row.id);
						} else {
							return delBillBatch(this.ids);
						}
					})
					.then(() => {
						this.reload();
						this.$modal.msgSuccess("删除成功");
					})
					.catch(() => {});
			},

			/** 下载附件按钮操作 */
			handleDownload(row) {
				this.download(
					"bill/bill/exportPdf/" + row.id, {},
					`运砂单据_${row.billNo}.pdf`
				);
			},

			/** 导出按钮操作 */
			handleExport() {
				this.download(
					"bill/bill/exportBill", {
						...this.queryParams,
					},
					`运砂单据_${new Date().getTime()}.xlsx`,
					"application/json"
				);
			},
			/** 预览图片操作 */
			handleViewPicture(row, index) {
				this.imageUrl = [];
				JSON.parse(JSON.stringify(row)).forEach((item) => {
					this.imageUrl.push(item.uploadFilePath);
				});
				this.imageVisible = true;
				var imglist = [];
				var imglist1 = [];
				if (this.imageUrl.length > 1 || index > 0) {
					imglist = this.imageUrl.slice(index + 1);
					imglist1 = this.imageUrl.slice(0, index);
					imglist = imglist.concat(imglist1);
					imglist.unshift(row[index].uploadFilePath);
					this.imageUrl = imglist;
				} else {
					this.imageUrl = [];
					this.imageUrl.push(JSON.parse(JSON.stringify(row[0].uploadFilePath)));
				}
			},
		},
		activated() {
			//组件被激活时重绘表格
			this.$refs.billTable.changeTableHeight();
		},
	};
</script>

<style scoped lang="scss">
	//::v-deep .special .el-form-item__content {
	//  display: inline!important;
	//}
	.geo-info {
		display: flex;
		align-items: center;
		gap: 12px;
		/* 控制间距 */
		font-size: 14px;
		color: #303133;
		font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
			Microsoft YaHei, Arial, sans-serif;
		color: #606266;
	}

	.geo-label {
		font-weight: 400;
		// padding-right: ;
	}

	.geo-value {
		font-family: Menlo, Consolas, "Courier New", monospace;
		font-variant-numeric: tabular-nums;
		/* 等宽数字，方便对齐 */
		color: #409eff;
		background: #f4f4f5;
		padding: 2px 6px;
		border-radius: 3px;
		letter-spacing: 0.3px;
	}

	.tip {
		background-color: rgb(236, 248, 255);
		padding: 8px 16px;
		border-radius: 4px;
		border-left: 5px solid rgb(80, 191, 255);
		// margin: 20px 0px;
		margin-bottom: 10px;
		vertical-align: middle;
		font-size: 14px;
		color: #606266;
		font-weight: 700;
	}

	.currentPicture {
		width: 100px;
		height: 100px;
		cursor: pointer;
		margin-right: 10px;
	}

	.signature {
		width: 120px;
		height: 36px;
		vertical-align: middle;
	}

	::v-deep .el-divider--horizontal {
		margin: 15px 0;
	}

	.demo-input-suffix {
		display: flex;
		margin-bottom: 20px;
	}

	.istext {
		width: 100px;
	}
</style>
<style lang="scss"></style>
