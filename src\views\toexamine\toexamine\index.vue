<template>
  <div class="app-container" :gutter="10">
    <!--  模糊搜索部分-->
    <!-- <el-row>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" @click="onSubmit">获取最新数据</el-button>
        </el-form-item>
      </el-form>
    </el-row> -->
    <el-row>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item>
          <el-date-picker value-format="yyyy-MM" format="yyyy 年 MM 月" v-model="timeData" type="monthrange"
            range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份">
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item v-if="state!=0">
          <my-area-select v-model="formInline.areaCode" :external-parent-value="parentAreaCode"
            :external-children-value="childrenAreaCode" :read-only-parent="isReadOnlyParent"
            :read-only-children="isReadOnlyChildren" :is-clear="isClear" style="font-size: medium" />
        </el-form-item> -->
        <el-form-item>
          <el-select v-model="formInline.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in allStatus" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <!--  表格部分-->
    <el-row>
      <el-table ref="innerTable" :data="tableData" border style="width: 100%" size="mini">
        <el-table-column type="index" width="40">
        </el-table-column>
        <el-table-column label="所属区县" min-width="180" align="center" prop="areaName">
        </el-table-column>
        <el-table-column label="所属部门" min-width="180" align="center" prop="deptName">
        </el-table-column>
        <el-table-column label="审核状态" min-width="180" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="reType(scope.row.status)" disable-transitions>{{ reTypeName(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" min-width="180" align="center" prop="createTime">
        </el-table-column>
        <el-table-column label="操作" min-width="180" align="center">
          <template slot-scope="scope">
            <el-button type="danger" @click="handleView(scope.row)" v-if="scope.row.cityStatus == 0">审核</el-button>
            <!-- <el-button type="text" @click="modifyView(scope.row)">修改</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
        style="text-align: right;margin-top:5px" :current-page="formInline.pageNum" :page-sizes="[10, 20, 50, 100]"
        :page-size="10" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </el-row>
    <my-dialog :title="title" :visible.sync="open" width="1060px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="85px">
        <div class="myscroll">
          <el-card class="box-card">
            <div class="mycustom">
              <el-form-item label="区域名称" prop="areaCode" label-width="100px">
                <!-- <new-select v-model="form.areaCode" :externalParentValue="parentAreaCode1"
                :externalChildrenValue="childrenAreaCode1" :read-only-parent="isReadOnlyParent1"
                :read-only-children="isReadOnlyChildren1" :is-clear="isClear" @parentNameLi="fclick"
                @childrenNameLi="zclick" :disabled="isreadonly" style="font-size: medium" /> -->
                <el-input class="custom-width" v-model="form.areaName" placeholder="请输入区域名称"
                  :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="所属部门" prop="deptname">
                <el-input class="custom-width" v-model="deptname" placeholder="请输入部门" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="报告时间" prop="filingTimeAll" label-width="100px">
                <el-date-picker class="custom-width" v-model="form.filingTimeAll" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly">
                </el-date-picker>
              </el-form-item>
            </div>
          </el-card>
          <el-card>
            <div class="mycustom">
              <el-form-item label="出动水行政执法人员(人次)" lable-position="left" prop="waterEnforcementPerson"
                label-width="150px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.waterEnforcementPerson" placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="出动警务人员(人次)" lable-position="left" prop="policePerson" label-width="190px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.policePerson"
                  placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="出动其他行政执法人员(人次)" lable-position="left" prop="otherPerson" label-width="220px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.otherPerson"
                  placeholder="请输入人次" :readonly="isreadonly"></el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="联合执法次数(次)" lable-position="left" prop="jointLawEnforcement" label-width="150px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.jointLawEnforcement" placeholder="请输入次数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="查处非法采砂(处)" lable-position="left" prop="illegalSandMining" label-width="190px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.illegalSandMining" placeholder="请输入数量" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="移交涉黑涉恶线索(条)" lable-position="left" prop="transferredToPolice" label-width="220px">
                <el-input class="customonly-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.transferredToPolice" placeholder="请输入条数" :readonly="isreadonly"></el-input>
              </el-form-item>

            </div>
          </el-card>
          <!-- <el-card class="isLine">
          <div class="mycustom">
            <div class="myTables">
              非法采砂线索来源
            </div>
          </div>
          <div class="mycustom">
            <el-form-item label="自然资源部门（个）" lable-position="left" prop="sourceNaturalResources" label-width="150px">
              <el-input class="customonly-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                v-model="form.sourceNaturalResources" placeholder="请输入个数" :readonly="isreadonly"></el-input>
            </el-form-item>
            <el-form-item label="交通运输部门（个）" lable-position="left" prop="sourceTransport" label-width="150px">
              <el-input class="customonly-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.sourceTransport"
                placeholder="请输入个数" :readonly="isreadonly"></el-input>
            </el-form-item>
            <el-form-item label="公安部门（个）" lable-position="left" prop="sourcePolice" label-width="120px">
              <el-input class="customonly-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.sourcePolice"
                placeholder="请输入个数" :readonly="isreadonly"></el-input>
            </el-form-item>
          </div>
          </el-card> -->
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="印发宣传材料(册)" prop="publicityMaterials" lable-position="left" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form.publicityMaterials" placeholder="请输入册数" :readonly="isreadonly"></el-input>
              </el-form-item>
              <el-form-item label="媒体宣传报道(次)" prop="mediaReports" lable-position="left" label-width="150px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form.mediaReports"
                  placeholder="请输入次数" :readonly="isreadonly"></el-input>
              </el-form-item>
            </div>
          </el-card>
          <div class="isLine">
            <div class="mycustom">
              <el-button type="primary" icon="el-icon-circle-plus-outline" size="medium" v-if="zilei == true"
                @click="handleAddSon">新增案件</el-button>
            </div>
            <div class="mycustom">
              <el-table ref="innerTable" :data="statementDos" border style="width: 100%" size="mini">
                <el-table-column type="index" width="50" label="序号"></el-table-column>
                <el-table-column label="立案时间" min-width="80" align="center" prop="filingTime">
                </el-table-column>
                <el-table-column label="报告类型" min-width="80" align="center" prop="issueType">
                </el-table-column>
                <el-table-column label="所在河流" min-width="80" align="center" prop="riverName">
                </el-table-column>
                <el-table-column label="整治单位" min-width="80" align="center" prop="responsibleUnit">
                </el-table-column>
                <el-table-column label="整治责任人" min-width="80" align="center" prop="chargePerson">
                </el-table-column>
                <el-table-column label="行政立案" min-width="80" align="center" prop="administrativeCase">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.administrativeCase == 0 ? 'warning' : 'success'" disable-transitions>{{
                      scope.row.administrativeCase == 0 ? '否' : '是' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="是否结案" min-width="80" align="center" prop="caseClosed">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.caseClosed == 0 ? 'warning' : 'success'" disable-transitions>{{
                      scope.row.caseClosed == 0 ? '否' : '是' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="180" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="handleView1(scope.row)">详情</el-button>
                    <el-button type="text" @click="modifyView1(scope.row, scope.$index)"
                      v-if="zilei == true">修改</el-button>
                    <el-button type="text" @click="handelDelete1(scope.row, scope.$index)"
                      v-if="zilei == true">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <!-- <div class="mycustom">
            <el-form-item label="整治情况" prop="remediationStatus">
              <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入内容"
                v-model="form.remediationStatus" :readonly="isreadonly">
              </el-input>
            </el-form-item>
          </div> -->
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="xianyan" @click="submitForm(1)" v-hasPermi="['auditFlow:auditFlow:update']">通
          过</el-button>
        <el-button type="danger" class="xianyan" @click="submitForm(2)" v-hasPermi="['auditFlow:auditFlow:update']">驳
          回</el-button>
        <el-button class="xianyan" @click="cancel">{{ isreadonly == false ? '取 消' : '关 闭' }}</el-button>
      </div>
    </my-dialog>
    <el-dialog :title="title1" :visible.sync="open1" width="1150px" append-to-body :before-close="cancel1">
      <el-form ref="form6" :model="form6" :rules="rules1" label-width="85px">
        <div class="myscroll">
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="问题类型" prop="issueType" label-width="100px">
                <el-select class="custom-width" v-model="form6.issueType" placeholder="请选择类型" :disabled="isreadonly1"
                  clearable>
                  <el-option v-for="item in bbtbProblum" :key="item.value" :label="item.name" :value="item.name">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="案件办理单位" prop="responsiblePerson" label-width="100px">
                <el-input class="custom-width" v-model="form6.responsiblePerson" placeholder="请输入单位名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="所在河流" prop="riverName" label-width="100px">
                <el-input class="custom-width" v-model="form6.riverName" placeholder="请输入河流名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="线索来源" prop="sourceClues" label-width="100px">
                <el-select class="custom-width" v-model="form6.sourceClues" placeholder="请选择来源" :disabled="isreadonly1"
                  clearable>
                  <el-option v-for="item in sourceDate" :key="item.value" :label="item.name" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="具体位置" prop="village" label-width="100px">
                <el-input class="custom-width" v-model="form6.village" placeholder="请输入具体位置"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label-width="0px">
                <el-select class="custom-width-select" v-model="typeClass" @change="setType" placeholder=""
                  :disabled="isreadonly1">
                  <el-option v-for="item in typeName" :key="item.value" :label="item.name" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <Dufenmiao v-model="coordinates" @input="getValue" :mydisabled="isreadonly1" v-show="typeClass == 0">
              </Dufenmiao>
              <el-form-item label="经度" prop="longitude" label-width="50px" v-show="typeClass == 1">
                <el-input class="custom-width" v-model="form6.longitude"
                  @input="form6.longitude = $options.filters.number(form6.longitude)" placeholder="请输入经度"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="纬度" prop="latitude" label-width="50px" v-show="typeClass == 1">
                <el-input class="custom-width" v-model="form6.latitude"
                  @input="form6.latitude = $options.filters.number(form6.latitude)" placeholder="请输入纬度"
                  :readonly="isreadonly1"></el-input>
                <!-- <i class="el-icon-location-outline mapUp" @click="mapClick"></i> -->
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <!-- <div class="mycustom">
            <el-form-item label="形成砂坑" prop="hasSandPit" label-width="100px">
              <el-switch v-model="form6.hasSandPit" class="tableScopeSwitch" active-text="是" inactive-text="否"
                active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
              </el-switch>
            </el-form-item>
          </div> -->
            <div class="mycustom">
              <!-- <el-form-item label="砂坑个数" prop="sandPitCount" label-width="100px">
              <el-input class="custom-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.sandPitCount"
                placeholder="请输入内容" :readonly="isreadonly1"></el-input>
            </el-form-item> -->
              <el-form-item label="面积(㎡)" prop="sandPitArea" label-width="100px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.sandPitArea"
                  placeholder="请输入面积" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="深度(m)" prop="sandPitDepth" label-width="100px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.sandPitDepth"
                  placeholder="请输入深度" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="采砂量(m³)" prop="illegalExtractionVolume" label-width="100px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));"
                  v-model="form6.illegalExtractionVolume" placeholder="请输入采砂量" :readonly="isreadonly1"
                  @blur="setTab"></el-input>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="illegalExtractionVolumeType">
              <el-form-item label="是否启动生态损害赔偿" prop="whetherCompensation" label-width="160px">
                <el-switch v-model="form6.whetherCompensation" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form6.whetherCompensation == false">
              <el-form-item label="不启动原因" prop="notCause" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入原因并说明"
                  v-model="form6.notCause" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="整治单位" prop="responsibleUnit" label-width="100px">
                <el-input class="custom-width" v-model="form6.responsibleUnit" placeholder="请输入单位名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="整治责任人" prop="chargePerson" label-width="104px">
                <el-input class="custom-width" v-model="form6.chargePerson" placeholder="请输入责任人名称"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="完成时限" prop="completionDeadline" label-width="100px">
                <el-date-picker class="custom-width" v-model="form6.completionDeadline" type="date" placeholder="请选择日期"
                  value-format="yyyy-MM-dd" :disabled="isreadonly1">
                </el-date-picker>
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="整治措施" prop="regulationMeasure" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="输入具体措施"
                  v-model="form6.regulationMeasure" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="行政立案" prop="administrativeCase">
                <el-switch v-model="form6.administrativeCase" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form6.administrativeCase == true">
              <el-form-item label="涉案人数" prop="suspectsCount" label-width="100px">
                <el-input class="custom-width" type="number"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.suspectsCount"
                  placeholder="请输入人数" :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="没收违法所得(万元)" prop="confiscationIllegalGains" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" label-width="146px">
                <el-input class="custom-width" v-model="form6.confiscationIllegalGains" placeholder="请输入金额"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="罚款(万元)" prop="fineAmount" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" label-width="90px">
                <el-input class="custom-width" v-model="form6.fineAmount" placeholder="请输入金额"
                  :readonly="isreadonly1"></el-input>
              </el-form-item>
              <el-form-item label="立案时间" prop="filingTime" label-width="90px">
                <el-date-picker class="custom-width" v-model="form6.filingTime" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly1">
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form6.administrativeCase == true">
              <el-form-item label="案件简要情况" prop="administrativeCaseDetails" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="描述时间 地点 行为人 行为 违反法律法规等基本案情"
                  v-model="form6.administrativeCaseDetails" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
            <div class="mycustom">
              <el-form-item label="是否结案" prop="caseClosed" label-width="100px">
                <el-switch v-model="form6.caseClosed" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="结案时间" prop="closingTime" label-width="100px" v-show="form6.caseClosed == true">
                <el-date-picker class="custom-width" v-model="form6.closingTime" type="month" placeholder="请选择日期"
                  value-format="yyyy-MM" :disabled="isreadonly1">
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="mycustom" v-show="form6.caseClosed == true">
              <el-form-item label="结案信息" prop="closingInformation" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="案件办理情况"
                  v-model="form6.closingInformation" :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <el-form-item label="移送公安机关" prop="transferredToPolice" label-width="110px">
                <el-switch v-model="form6.transferredToPolice" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="涉黑涉恶" prop="involvedInOrganizedCrime">
                <el-switch v-model="form6.involvedInOrganizedCrime" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="制定整治措施" prop="remediationMeasuresDefined" label-width="110px">
                <el-switch v-model="form6.remediationMeasuresDefined" class="tableScopeSwitch" active-text="是"
                  inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
            </div>
          </el-card>
          <el-card class="isLine">
            <div class="mycustom">
              <!-- <el-form-item label="整改完结" prop="rectificationCompleted" label-width="80px">
              <el-switch v-model="form6.rectificationCompleted" class="tableScopeSwitch" active-text="是"
                inactive-text="否" active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
              </el-switch>
            </el-form-item> -->
              <el-form-item label="是否追责" prop="liability" label-width="80px">
                <el-switch v-model="form6.liability" class="tableScopeSwitch" active-text="是" inactive-text="否"
                  active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
                </el-switch>
              </el-form-item>
              <el-form-item label="追责人数" prop="liabilityNumberPersons" label-width="80px" v-show="form6.liability">
                <el-input-number v-model="form6.liabilityNumberPersons" @change="handleChange" :min="0" :max="10"
                  label="描述文字" :disabled="isreadonly1"></el-input-number>
              </el-form-item>
            </div>
            <div class="mycustomName">
              <div v-for="(item, index) in form6.reportPersonList" :key="index" class="autoNames">
                <el-form-item label="姓名" :prop="'reportPersonList.' + index + '.liabilityName'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入姓名" v-model="item.liabilityName"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <el-form-item label="职务" :prop="'reportPersonList.' + index + '.liabilityPost'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入职务" v-model="item.liabilityPost"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <el-form-item label="追责问责形式" :prop="'reportPersonList.' + index + '.accountability'"
                  label-width="120px">
                  <el-input class="custom-width" placeholder="请输入问责形式" v-model="item.accountability"
                    :disabled="isreadonly1"></el-input>
                </el-form-item>
                <!-- <el-form-item>
                  <i class="el-icon-delete" @click="deleteItem(item, index)"></i>
              </el-form-item> -->
              </div>
            </div>
          </el-card>
          <!-- <el-card class="isLine">
          <div class="mycustom">
            <el-form-item label="联合执法" prop="jointLawEnforcement">
              <el-switch v-model="form6.jointLawEnforcement" class="tableScopeSwitch" active-text="是" inactive-text="否"
                active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1">
              </el-switch>
            </el-form-item>
          </div>
          <div class="mycustom" v-show="form6.jointLawEnforcement==true">
            <el-form-item label="水行政执法人员(人)" prop="waterEnforcementPerson" label-width="160px">
              <el-input class="custom-width" v-model="form6.waterEnforcementPerson" placeholder="请输入内容"
                :readonly="isreadonly1"></el-input>
            </el-form-item>
            <el-form-item label="乡镇行政执法人员(人)" prop="townshipPerson" label-width="160px">
              <el-input class="custom-width" v-model="form6.townshipPerson" placeholder="请输入内容"
                :readonly="isreadonly1"></el-input>
            </el-form-item>
            <el-form-item label="警务人员(人)" prop="policePerson" label-width="120px">
              <el-input class="custom-width" v-model="form6.policePerson" placeholder="请输入内容"
                :readonly="isreadonly1"></el-input>
            </el-form-item>
          </div>
          <div class="mycustom" v-show="form6.jointLawEnforcement==true">
            <el-form-item label="查处非法采砂(处)" prop="illegalSandMining" label-width="160px">
              <el-input class="custom-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.illegalSandMining"
                placeholder="请输入内容" :readonly="isreadonly1"></el-input>
            </el-form-item>
            <el-form-item label="其它行政执法人员(自然资源/交通运输)" prop="otherPerson" label-width="160px">
              <el-input class="custom-width" v-model="form6.otherPerson" placeholder="请输入内容"
                :readonly="isreadonly1"></el-input>
            </el-form-item>
            <el-form-item label="刑事立案数(起)" prop="criminalFiling" label-width="120px">
              <el-input class="custom-width" type="number"
                onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)));" v-model="form6.criminalFiling"
                placeholder="请输入内容" :readonly="isreadonly1"></el-input>
            </el-form-item>
          </div>
          </el-card> -->
          <el-card class="isLine">
            <!-- <div class="mycustom">
            <el-form-item label="新增月份" prop="newMonth" label-width="100px">
              <el-date-picker class="custom-width" v-model="form6.newMonth" type="month" placeholder="请选择"
                value-format="yyyy-MM" :disabled="isreadonly1">
              </el-date-picker>
              </el-date-picker>
            </el-form-item>
          </div> -->
            <div class="mycustom">
              <el-form-item label="处理备注" prop="notes" label-width="100px">
                <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="请输入备注内容" v-model="form6.notes"
                  :readonly="isreadonly1">
                </el-input>
              </el-form-item>
            </div>
          </el-card>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitform6(modifyLi)" v-if="isreadonly1 == false">确 定</el-button>
        <el-button @click="cancel1">{{ isreadonly1 == false ? '取 消' : '关 闭' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
import NewSelect from "@/components/YB/MyAreaSelect_li.vue";
import Dufenmiao from "@/components/Dufenmiao/index.vue";
import {
  getToexamineData,
  getDeleteS,
  getDelete,
  addReportForms,
  getFormsId,
  modifyReportForms,
  examineType,
  getAreaName,
} from "@/api/tablebbtb/bbtb";
import {
  getDeptTreeData
} from "@/api/system/dept";
import DictUtils from "@/utils/dictUtils";
export default {
  name: "HelloWorld",
  components: {
    MyAreaSelect: MyAreaSelect,
    NewSelect: NewSelect,
    Dufenmiao: Dufenmiao
  },
  data() {
    return {
      statementDos: [],
      coordinates: [0, 0],
      typeClass: 0,
      typeName: [{
        name: '度分秒',
        value: 0
      }, {
        name: '度',
        value: 1
      }],
      allStatus: [{
        name: '待审核',
        value: 0
      }, {
        name: '已通过',
        value: 1
      }, {
        name: '未通过',
        value: 2
      }],
      deptname: '',
      isreadonly: false,
      isreadonly1: false,
      childrenValue: '',
      parentValue: '',
      //表格高度
      inTableHeight: 300,
      multipleSelection: [], //表格多选框数据
      timeData: null, //时间
      bbtbProblum: '',
      // 区域选择框默认选中
      parentAreaCode: "",
      childrenAreaCode: "",
      parentAreaCode1: "",
      childrenAreaCode1: "",
      isReadOnlyParent: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren: false, // 控制子下拉框是否可编辑
      isReadOnlyParent1: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren1: false, // 控制子下拉框是否可编辑
      isClear: false, // 父选择框是否可清空
      msg: "Welcome to Your Vue.js App",
      formInline: {
        pageNum: 1,
        pageSize: 10,
        // issueType: "", //报告类型
        areaName: "", //区域名称
        areaCode: "", //区域编码
        startDate: null, //开始时间
        endDate: null, //结束时间
        status: null
      },
      total: 0,
      tableData: [],
      form: {
        id: '',
        filingTimeAll: undefined,
        areaName: undefined,
        areaCode: undefined,
        jointLawEnforcement: undefined,
        waterEnforcementPerson: undefined,
        policePerson: undefined,
        otherPerson: undefined,
        illegalSandMining: undefined,
        transferredToPolice: undefined,
        sourcePolice: undefined,
        sourceNaturalResources: undefined,
        sourceTransport: undefined,
        publicityMaterials: undefined,
        mediaReports: undefined,
        statementDos: []
      },
      form6: {
        id: '',
        longitudeType: undefined,
        closingTime: undefined,
        closingInformation: undefined,
        filingTime: undefined,
        reportPersonList: [],
        liabilityNumberPersons: 0,
        whetherCompensation: true,
        notCause: undefined,
        regulationMeasure: undefined,
        reportType: undefined,
        issueType: undefined,
        responsiblePerson: undefined,
        riverName: undefined,
        village: undefined,
        hasSandPit: false,
        illegalExtractionVolume: undefined,
        administrativeCase: false,
        transferredToPolice: false,
        involvedInOrganizedCrime: false,
        remediationStatus: undefined,
        notes: undefined,
        longitude: undefined,
        latitude: undefined,
        sandPitCount: undefined,
        sandPitArea: undefined,
        sandPitDepth: undefined,
        suspectsCount: undefined,
        fineAmount: undefined,
        confiscationIllegalGains:undefined,
        administrativeCaseDetails: undefined,
        caseClosed: false,
        remediationMeasuresDefined: false,
        responsibleUnit: undefined,
        chargePerson: undefined,
        completionDeadline: undefined,
        rectificationCompleted: false,
        personnelResponsibility: false,
        status: undefined,
        createUserId: undefined,
        updateUserId: undefined,
        newMonth: undefined,
        sourceClues: undefined,
        criminalFiling: undefined,
        illegalSandMining: undefined,
        otherPerson: undefined,
        policePerson: undefined,
        townshipPerson: undefined,
        waterEnforcementPerson: undefined,
        jointLawEnforcement: false,
      },
      rules: {
        areaCode: [{
          required: true,
          message: '请选择所在区域',
          trigger: 'change'
        }],
      },
      rules1: {
        // newMonth: [{
        //   type: 'string',
        //   required: true,
        //   message: '请选择新增月份',
        //   trigger: 'change'
        // }],
        sourceClues: [{
          required: true,
          message: '请选择线索来源',
          trigger: 'change'
        }],
        issueType: [{
          required: true,
          message: '请选择问题类型',
          trigger: 'change'
        }],
        riverName: [{
          required: true,
          message: '请输入',
          trigger: 'blur'
        },],
        village: [{
          required: true,
          message: '请输入',
          trigger: 'blur'
        },],

      },

      textarea: '',
      // 弹出层标题
      title: "",
      title1: "",
      // 是否显示弹出层
      open: false,
      open1: false,
      zname: '',
      fname: '',
      zilei: false,
      modifyLi: 0,
      index: '',
      //自定义状态，0是县级填报人1是市级填报人
      state: 0,
      areaCode: undefined,
      mapTaost: false,
      map: undefined,
      illegalExtractionVolumeType: false,
      num: 0,
      oldnum: 0,
      geocoder: null,
      sourceDate: '',
    };
  },
  created() {
    this.getDeptTreeselect();
    this.getTable();
    this.getArea();
    // 获取字典数据
    DictUtils.cList('bbtbProblum').then(r => {
      this.bbtbProblum = r.bbtbProblum
    });
    DictUtils.cList('sourceTypes').then(r => {
      this.sourceDate = r.sourceTypes
    });
    if (this.$store.state.user.deptcode.length == 9) {
      this.state = 1;//市级
    } else if (this.$store.state.user.deptcode.length > 9) {
      this.state = 0;//县级
    } else if (this.$store.state.user.deptcode.length < 9) {
      this.state = 2;//省级
    }
  },
  mounted() {
    let that=this;
    window.onresize = function () {
      that.changeTableHeight();
    };
    //监听浏览器标签页切换
    document.addEventListener("visibilitychange", function () {
      if (!document.hidden) {
        that.changeTableHeight();
      }
    });
  },
  methods: {
    setType(e) {
      console.log(e)
      this.form6.latitude = undefined;
      this.form6.longitude = undefined;
      this.coordinates = [0, 0];
    },
    getValue(val) {
      this.form6.latitude = val[1];
      this.form6.longitude = val[0];
    },
    handleChange(value) {
      let num = this.oldnum;
      if (value > num) {
        this.addItem();
        this.form6.liabilityNumberPersons = value;
        this.oldnum = value;
      } else {
        this.deleteItem();
        this.form6.liabilityNumberPersons = value;
        this.oldnum = value;
      }
    },
    addItem() {
      this.form6.reportPersonList.push({
        name: '',
        phone: ''
      })
    },
    deleteItem(item, index) {
      this.form6.reportPersonList.splice(index, 1)
    },
    setTab() {
      if (this.form6.illegalExtractionVolume > 500) {
        this.illegalExtractionVolumeType = true;
      } else {
        this.illegalExtractionVolumeType = false;
      }
    },
    getDeptTreeselect() {
      getDeptTreeData().then(res => {
        console.log(res)
        this.deptList = res.deptList;
      });
    },
    // 返回状态
    reType(type) {
      if (type == 0 || type == '' || type == null || type == undefined) {
        return 'warning';
      } else if (type == 1) {
        return 'success';
      } else if (type == 2) {
        return 'danger';
      }
    },
    reTypeName(type) {
      console.log(type, '哪里的啊')
      if (type == 0 || type == '' || type == null || type == undefined) {
        return '待审核';
      } else if (type == 1) {
        return '已通过';
      } else if (type == 2) {
        return '未通过';
      } else if (type == 3) {
        return '暂存';
      }
    },
    fclick(res) {
      console.log(res)
      // this.fname = res;
    },
    zclick(res) {
      console.log(res)
      // this.zname = res;
    },
    getArea() {
      getAreaName().then((res) => {
        this.zname = res.AREA_NAME;
        this.areaCode = res.AREA_CODE;
        if (res.DEPT_CODE.length == 9) {
          this.parentAreaCode = res.AREA_CODE;
          this.myareaname = res.AREA_NAME;
          this.isReadOnlyParent = true;
        } else {
          this.parentAreaCode = '';
          this.myareaname = '';
          this.isReadOnlyParent = false;
        }
        console.log(res, '获取的')
      });
    },
    //设置表格高度
    changeTableHeight() {
      if (!this.$refs.innerTable) {
        return;
      }

      var tableHeight = window.innerHeight || document.body.clientHeight;

      //计算表格上方的元素高度，和底部的元素高度，自动计算一个值
      var disTop = this.$refs.innerTable.$el;

      //如果表格上方有元素则减去这些高度适应窗口，66是底下留白部分
      tableHeight -= disTop.offsetTop + 60 + 84;
      /*if (disTop.offsetParent) {
        tableHeight -= disTop.offsetParent.offsetTop;
      }*/
      this.$nextTick(function () {
        this.inTableHeight = tableHeight < 300 ? 300 : tableHeight;
      })

      //重绘表格
      this.doLayout();
    },
    // 多选框
    handleSelectionChange(val) {
      this.multipleSelection = []
      val.forEach((item) => {
        this.multipleSelection.push(item.id)
      })
    },
    handleAdd() {
      this.parentAreaCode1 = '';
      this.reset();
      this.open = true;
      this.isreadonly = false;
      this.title = "新增报告（*为必填项）";
      this.zilei = true;
    },
    handleAddSon() {
      // this.parentAreaCode1='';
      this.reset1();
      this.open1 = true;
      this.isreadonly1 = false;
      this.title1 = "新增案件（*为必填项）";
      this.modifyLi = 0;
      this.index = '';
      this.form6.liabilityNumberPersons = 0;
      this.oldnum = 0;
      this.typeClass = 0;
    },
    /** 任务详细信息 */
    handleView(row) {
      console.log(row)
      this.reset();
      this.title = "报告单审核";
      this.open = true;
      this.isreadonly = true;
      this.parentAreaCode1 = row.areaCode;
      this.deptname = row.deptName;
      this.statementDos = row.statementDos ? JSON.parse(JSON.stringify(row.statementDos)) : []
      this.form = row;
    },
    /** 任务详细信息 */
    handleView1(row) {
      this.reset1();
      this.title1 = "案件详情";
      this.open1 = true;
      this.isreadonly1 = true;

      // 深拷贝原始数据
      const copiedRow = JSON.parse(JSON.stringify(row));
      console.log(copiedRow)
      // 转换布尔值（操作拷贝后的对象）
      this.typeClass = parseInt(copiedRow.longitudeType);
      copiedRow.hasSandPit = copiedRow.hasSandPit == 0 ? false : true;
      copiedRow.administrativeCase = copiedRow.administrativeCase == 0 ? false : true;
      copiedRow.transferredToPolice = copiedRow.transferredToPolice == 0 ? false : true;
      copiedRow.involvedInOrganizedCrime = copiedRow.involvedInOrganizedCrime == 0 ? false : true;
      copiedRow.caseClosed = copiedRow.caseClosed == 0 ? false : true;
      copiedRow.remediationMeasuresDefined = copiedRow.remediationMeasuresDefined == 0 ? false : true;
      copiedRow.rectificationCompleted = copiedRow.rectificationCompleted == 0 ? false : true;
      copiedRow.liability = copiedRow.liability == 0 ? false : true;
      copiedRow.jointLawEnforcement = copiedRow.jointLawEnforcement == 0 ? false : true;
      copiedRow.whetherCompensation = copiedRow.whetherCompensation == 0 ? false : true;
      copiedRow.sourceClues = copiedRow.sourceClues ? copiedRow.sourceClues.toString() : '';
      // 使用拷贝后的数据
      this.$set(this.coordinates, 0, copiedRow.longitude);
      this.$set(this.coordinates, 1, copiedRow.latitude);
      this.form6 = copiedRow;
      this.setTab();
      this.modifyLi = 0;
      this.index = '';
    },
    modifyView(row) {
      this.reset();
      this.title = "修改月报";
      this.open = true;
      this.isreadonly = false;
      this.parentAreaCode1 = row.areaCode;
      this.form = row;
      this.zilei = true;
      // getFormsId(row.id).then(res => {
      //   this.open = true;
      // });
    },
    modifyView1(row, index) {
      this.reset1();
      this.title1 = "修改案件";
      this.open1 = true;
      this.isreadonly1 = false;
      this.form6.liabilityNumberPersons = 0;
      this.oldnum = 0;
      this.typeClass = parseInt(row.longitudeType);
      this.parentAreaCode1 = row.areaCode;
      row.hasSandPit = row.hasSandPit == 0 ? false : true;
      row.administrativeCase = row.administrativeCase == 0 ? false : true;
      row.transferredToPolice = row.transferredToPolice == 0 ? false : true;
      row.involvedInOrganizedCrime = row.involvedInOrganizedCrime == 0 ? false : true;
      row.caseClosed = row.caseClosed == 0 ? false : true;
      row.remediationMeasuresDefined = row.remediationMeasuresDefined == 0 ? false : true;
      row.rectificationCompleted = row.rectificationCompleted == 0 ? false : true;
      row.liability = row.liability == 0 ? false : true;
      row.jointLawEnforcement = row.jointLawEnforcement == 0 ? false : true;
      row.whetherCompensation = row.whetherCompensation == 0 ? false : true;
      row.sourceClues = row.sourceClues ? row.sourceClues.toString() : '';
      this.$set(this.coordinates, 0, row.longitude);
      this.$set(this.coordinates, 1, row.latitude);


      this.form6 = row;
      this.modifyLi = 1;
      this.index = index;
    },
    /** 提交按钮 */
    submitForm(status) {
      let data = {
        id: this.form.id,
        // countyStatus:status,
        status: status
      }
      examineType(data).then(r => {
        if (status == 1) {
          this.$modal.msgSuccess("审核成功！");
        } else if (status == 2) {
          this.$modal.msgWarning("已驳回！");
        }
        this.open = false;
        this.onReset();
      });
    },
    handleSizeChange(val) {
      this.formInline.pageNum = 1
      this.formInline.pageSize = val
      this.getTable()
    },
    handleCurrentChange(val) {
      this.formInline.pageNum = val
      this.getTable()

    },
    // 获取表格数据
    getTable() {
      getToexamineData(this.formInline).then((res) => {
        this.tableData = res.data;
        this.total = res.total
      });
    },

    // 重置按钮
    onReset() {
      this.formInline = {
        pageNum: 1,
        pageSize: 10,
        // issueType: "", //报告类型
        areaName: "", //区域名称
        areaCode: "", //区域编码
        startDate: null, //开始时间
        endDate: null, //结束时间
        status: null
      }
      this.timeData = null;
      this.getTable()
    },
    // 搜索按钮
    onSubmit() {
      this.formInline.pageNum = 1;
      if (this.timeData) {
        this.formInline.startDate = this.timeData[0];
        this.formInline.endDate = this.timeData[1];
      } else {
        this.formInline.startDate = null;
        this.formInline.endDate = null;
      }
      this.getTable()
    },
    handleChange(value) {
      console.log(value);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.deptname = '';
      this.reset();
    },
    // 取消按钮
    cancel1() {
      this.open1 = false;
      console.log(this.form.statementDos, '数组');
      this.reset1();
    },
    handelDelete(row) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getDelete({
          id: row
        }).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getTable()
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handelDelete1(row, index) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.statementDos.splice(index, 1);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    handleDelete() {
      if (this.multipleSelection.length == 0) {
        this.$modal.msgWarning("请选择要删除的数据")
        return
      }
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        getDeleteS(this.multipleSelection).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getTable()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    // 表单重置
    reset() {
      this.form = {

        id: null,
        filingTimeAll: undefined,
        areaName: undefined,
        areaCode: undefined,
        jointLawEnforcement: undefined,
        waterEnforcementPerson: undefined,
        policePerson: undefined,
        otherPerson: undefined,
        illegalSandMining: undefined,
        transferredToPolice: undefined,
        sourcePolice: undefined,
        sourceNaturalResources: undefined,
        sourceTransport: undefined,
        publicityMaterials: undefined,
        mediaReports: undefined,
        statementDos: []
      }
      this.resetForm("form");
    },
    // 表单重置
    reset1() {
      this.form6 = {

        id: null,
        longitudeType: undefined,
        closingTime: undefined,
        closingInformation: undefined,
        filingTime: undefined,
        reportPersonList: [],
        liabilityNumberPersons: 0,
        whetherCompensation: true,
        notCause: undefined,
        regulationMeasure: undefined,
        reportType: undefined,
        issueType: undefined,
        responsiblePerson: undefined,
        riverName: undefined,
        village: undefined,
        hasSandPit: false,
        illegalExtractionVolume: undefined,
        administrativeCase: false,
        transferredToPolice: false,
        involvedInOrganizedCrime: false,
        remediationStatus: undefined,
        notes: undefined,
        longitude: undefined,
        latitude: undefined,
        sandPitCount: undefined,
        sandPitArea: undefined,
        sandPitDepth: undefined,
        suspectsCount: undefined,
        fineAmount: undefined,
        confiscationIllegalGains:undefined,
        administrativeCaseDetails: undefined,
        caseClosed: false,
        remediationMeasuresDefined: false,
        responsibleUnit: undefined,
        chargePerson: undefined,
        completionDeadline: undefined,
        rectificationCompleted: false,
        personnelResponsibility: false,
        status: undefined,
        createUserId: undefined,
        updateUserId: undefined,
        // areaName:undefined,
        // areaCode:undefined,
        newMonth: undefined,
        sourceClues: undefined,
        criminalFiling: undefined,
        illegalSandMining: undefined,
        otherPerson: undefined,
        policePerson: undefined,
        townshipPerson: undefined,
        waterEnforcementPerson: undefined,
        jointLawEnforcement: false,
      }
      this.resetForm("form6");
    },
  },
};
</script>

<style scoped>
/* .xianyan{
    width: 120px !important;
    height: 50px;
    font-size: 24px;
  } */
h1 {
  color: #42b983;
}

.mycustomName {
  width: 100%;
}

.autoNames {
  width: 100%;
  display: flex;
}

.mapUp {
  float: right;
  font-size: 30px;
  padding: 4px 0 0 5px;
  color: #67C23A;
  cursor: pointer;
}

.box-card {
  margin-bottom: 10px;
  margin-top: 5px;
  height: 70px;
}

.isLine {
  margin-top: 10px;
}

.myTables {
  float: left;
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-weight: 700;
  text-indent: 8px;
}

::v-deep .el-form-item__label {
  text-align: left;
  /* 确保 label 是左对齐的 */
  text-indent: 8px;
}

.custom-width {
  width: 150px;
}

.custom-width-select {
  width: 100px;
}

.mycustom {
  display: flex;
}

.custom-widthAll {
  width: 700px;
}

.myscroll {
  overflow: auto;
  height: 480px;
  width: 100%;

}

/* 整个滚动条 */
::-webkit-scrollbar {
  width: 12px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

/* 滚动条滑块在鼠标悬停时的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

::v-deep .tableScopeSwitch .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

/*打开时文字位置设置*/

::v-deep .tableScopeSwitch .el-switch__label--right {
  z-index: 1;
  right: 20px;
  /*不同场景下可能不同，自行调整*/
}

/*关闭时文字位置设置*/

::v-deep .tableScopeSwitch .el-switch__label--left {
  z-index: 1;
  left: 20px;
  /*不同场景下可能不同，自行调整*/
}

/*显示文字*/

::v-deep .tableScopeSwitch .el-switch__label.is-active {
  display: block;
}
</style>
