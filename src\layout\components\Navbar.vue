<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :title="sidebar.opened?'折叠':'展开'"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <div class="refresh-container" @click="refresh">
      <i class="el-icon-refresh" title="刷新"></i>
    </div>

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>

    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/>

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />

        <div class="right-menu-item hover-effect" @click="showQrCode">
          <svg-icon icon-class="phone"/>
        </div>

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <size-select id="size-select" class="right-menu-item hover-effect" />

<!--        <el-tooltip content="源码地址" effect="dark" placement="bottom">-->
<!--          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect"/>-->
<!--        </el-tooltip>-->

<!--        <el-tooltip content="文档地址" effect="dark" placement="bottom">-->
<!--          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect"/>-->
<!--        </el-tooltip>-->

<!--        <el-tooltip content="布局大小" effect="dark" placement="bottom">-->
<!--          <size-select id="size-select" class="right-menu-item hover-effect" />-->
<!--        </el-tooltip>-->

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div style="display: flex;align-items: center">
          <div class="avatar-wrapper">
            <i class="el-icon-user-solid" style="font-size: 20px;margin-right: 5px"></i>
            <span style="vertical-align: middle;margin-right: 5px">{{this.$store.getters.name}}</span>
            <img :src="avatar" class="user-avatar">
            <i class="el-icon-caret-bottom" />
          </div>
        </div>


        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <my-dialog title="微信小程序码" :visible.sync="openQrCode" width="320px" append-to-body>
        <div class="qrcode-img" v-if="openQrCode" ref="qrCodeDiv">
          <img width="100%" :src="mpImg">
        </div>
      </my-dialog>
    </div>
  </div>
</template>

<script>
import {getToken} from "@/utils/auth";
import { mapGetters } from 'vuex';
import Breadcrumb from '@/components/Breadcrumb';
import TopNav from '@/components/TopNav';
import Hamburger from '@/components/Hamburger';
import Screenfull from '@/components/Screenfull';
import SizeSelect from '@/components/SizeSelect';
import Search from '@/components/HeaderSearch';
import RuoYiGit from '@/components/RuoYi/Git';
import RuoYiDoc from '@/components/RuoYi/Doc';
import QRCode from "qrcodejs2-fix";
import mpImg from '@/assets/images/weixinmp_430.jpg'

export default {
  inject: ['reload'],
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  data() {
    return {
      //是否显示二维码弹窗
      openQrCode: false,
      mpImg:mpImg,
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar');
    },

    /** 退出登录 */
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
        localStorage.setItem('promptClosedDate', null);
        localStorage.setItem('cydclose', null);
      }).catch(() => {});
    },

    /** 打开二维码弹框 */
    async showQrCode() {
      this.openQrCode = true;
      //下载app的二维码
      // let appUrl = await this.getConfigValue('app.download.url');
      // this.$nextTick(() => {
      //   let split = window.location.href.split("/");
      //   let url = (appUrl.msg || (split[0] + "//" + split[2] + "/downloadApp")) + "?token=" + getToken();
      //   console.log(url)
      //   new QRCode(this.$refs.qrCodeDiv, {
      //     text: url,
      //     width: 280,
      //     height: 280,
      //     colorDark: '#333333', //二维码颜色
      //     colorLight: '#ffffff', //二维码背景色
      //     correctLevel: QRCode.CorrectLevel.L //容错率 L/M/H
      //   });
      // });
    },

    //页面刷新
    refresh() {
      this.reload();
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .refresh-container {
    padding: 0 5px;
    line-height: 48px;
    font-size: 20px;
    height: 100%;
    float: left;
    cursor: pointer;
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;


    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding: 0 20px!important;
}
</style>
