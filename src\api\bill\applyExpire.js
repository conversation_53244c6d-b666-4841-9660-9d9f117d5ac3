import request from '@/utils/request'

// 查询过期的运砂申请详细
export function getApplyExpire(id) {
  return request({
    url: '/bill/applyExpire/info/' + id,
    method: 'post'
  })
}

// 新增过期的运砂申请
export function addApplyExpire(data) {
  return request({
    url: '/bill/applyExpire/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改过期的运砂申请
export function updateApplyExpire(data) {
  return request({
    url: '/bill/applyExpire/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除过期的运砂申请
export function delApplyExpire(id) {
  return request({
    url: '/bill/applyExpire/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除过期的运砂申请
export function delApplyExpireBatch(ids) {
  return request({
    url: '/bill/applyExpire/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


