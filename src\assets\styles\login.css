html,body{margin:0px auto;height:100%;}
.main{
    width: 680px;height:100%;
    margin:0px auto;
    display: flex;
    flex-wrap: wrap;
    align-items:center;justify-content: center;
}
.login-main{
    width: 100%;
}
.title{
    height: 80px;
    padding:5px 0 0 34px;
    line-height: 54px;
    width: 100%;
    font-weight: 500;
    font-size: 36px;
    line-height: 40px;
    text-align: center;
    color: #1379d1;
    letter-spacing: 2px;
    text-shadow:
            -1px -1px 0 #fff,
            1px -1px 0 #fff,
            -1px 1px 0 #fff,
            1px 1px 0 #fff,
            0px 6px 6px rgba(0,0,0,0.6);
    display: block;
    /*background: url(../images/loginBackground.jpg) no-repeat left top;*/
}
.login-form{
    padding:30px 40px;
    box-sizing: border-box;
    min-height: 410px;
    border-radius: 8px;
    background: #fff;
    border: 4px solid rgba(42, 137, 219, 0.46);
}
.login-font{
    font-weight: 500;
    font-size: 28px;
    line-height: 40px;
    text-align: left;
    color: #333;
    margin-bottom:30px;
}
.login-user .input{
    width:100%;
    padding:5px 10px;box-sizing: border-box;
    height: 54px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0);
    border: 1px solid #b5b5b5;
    font-size: 18px;
    line-height: 40px;
    text-align: left;
    color: #333;
}

.login-user input:focus{outline-color: #1379d1;}
.yzm-btn{
    height:50px;
    width:140px;
    background: #fff;
    border: 0;
    border-radius: 2px;
    font-size: 16px;
    line-height: 50px;
    color: #333;
    position: absolute;
    right: 2px;top:2px;
    cursor: pointer;
}
.yzm-btn img{height: 50px;}
.input-user{
    position: relative;
}
.login-btn{
    width: 100%;
    height: 54px;
    margin-top:24px;
    border: 0;
    border-radius: 4px;
    background: #1379d1;
    font-size: 18px;
    line-height: 40px;
    color: #fff;
}
[v-cloak] {
    display: none;
}
