<template>
  <div class="app-container" ref="tableContainer" :gutter="10">
    <!--  模糊搜索部分-->
    <div class="" ref="myHeight">
    <el-row>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item>
          <el-date-picker value-format="yyyy-MM" format="yyyy 年 MM 月" v-model="timeData" type="monthrange"
            range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份">
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item>
          <el-date-picker class="custom-width" v-model="timeData" type="month" placeholder="请选择年月"
            value-format="yyyy-MM">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item>
          <my-area-select v-model="formInline.areaCode" :external-parent-value="parentAreaCode"
            :external-children-value="childrenAreaCode" :read-only-parent="isReadOnlyParent"
            :read-only-children="isReadOnlyChildren" :is-clear="isClear" style="font-size: medium"
            v-hasPermi="['zztz:zztz:select']" />
        </el-form-item>
        <el-form-item>
          <el-select v-model="formInline.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in allStatus" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="onReset">重置</el-button>
          <el-button type="warning" icon="el-icon-download" @click="handleExport" v-hasPermi="['zztz:zztz:export']">导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
     </div>
    <!-- <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
        </el-row> -->
    <!--  表格部分-->
    <el-row>
      <el-table ref="table" :data="tableData" border style="width: 100%" size="mini" header-row-class-name="custom-header"
        @selection-change="handleSelectionChange" :span-method="simpleSpanMethod" :height="tableHeight"
         :cell-class-name="setCellClass"
        >

        <!-- <el-table-column type="selection" width="40"></el-table-column> -->
        <el-table-column prop="filingTime" label="立案时间" min-width="50" align="center">
        </el-table-column>
        <el-table-column prop="areaName" label="市/区/县" min-width="70" align="center">
        </el-table-column>
        <el-table-column prop="village" label="具体位置(村庄)" min-width="90">
          <template #default="scoped">
            <span>
              {{ scoped.row.village }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column width="180">
            <el-table-column prop="longitude" label="经度" min-width="40">
            </el-table-column>
            <el-table-column prop="latitude" label="纬度" min-width="40">
            </el-table-column>
          </el-table-column> -->
        <el-table-column prop="issueType" label="问题类型" align="center" min-width="90" :show-overflow-tooltip="false">
          <template #default="scoped">
            <span>
              {{ scoped.row.issueType }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="riverName" label="所在河流" min-width="75">
          <template #default="scoped">
            <span>
              {{ scoped.row.riverName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="sourceClues" label="线索来源" min-width="50">
          <template slot-scope="scope">
            {{ getNames(scope.row.sourceClues) }}
          </template>
        </el-table-column>

        <!-- <el-table-column prop="hasSandPit" label="是否形成砂坑" min-width="50">
            <template slot-scope="scope">
              <el-tag type="success" disable-transitions v-if="scope.row.hasSandPit==1">是</el-tag>
              <el-tag type="warning" disable-transitions v-else>否</el-tag>
            </template>
          </el-table-column> -->
        <!-- <el-table-column width="180"> -->
        <!-- <el-table-column prop="sandPitCount" label="砂坑个数" min-width="50">
            </el-table-column> -->
        <el-table-column prop="sandPitArea" label="砂坑面积(㎡)" min-width="50">
        </el-table-column>
        <el-table-column prop="sandPitDepth" label="砂坑深度(米)" min-width="50">
        </el-table-column>
        <el-table-column prop="illegalExtractionVolume" label="非法采砂量(㎡)" min-width="60">
        </el-table-column>
        <el-table-column prop="administrativeCase" label="是否行政立案" min-width="50">
          <template slot-scope="scope">
            <el-tag type="success" disable-transitions v-if="scope.row.administrativeCase == 1">是</el-tag>
            <el-tag type="success" disable-transitions v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column width="180" label="行政立案" align="center">
          <el-table-column prop="suspectsCount" label="涉案人数" min-width="50">
          </el-table-column>
          <el-table-column prop="confiscationIllegalGains" label="没收违法所得(万元)" min-width="70">
         <template slot-scope="scope">
           <span>{{ scope.row.confiscationIllegalGains ? scope.row.confiscationIllegalGains.toFixed(4) : '' }}</span>
         </template>
          </el-table-column>
          <el-table-column prop="fineAmount" label="罚款(万元)" min-width="58">
            <template slot-scope="scope">
              <span>{{ scope.row.fineAmount ? scope.row.fineAmount.toFixed(4) : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="administrativeCaseDetails" align="center" label="立案情况" min-width="360" >
            <template #default="scoped">
              <el-tooltip placement="top" class="custom-tooltip"   :content="scoped.row.administrativeCaseDetails"  >
                <span class="autoScroll">
                  {{ scoped.row.administrativeCaseDetails }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="caseClosed" label="是否结案" min-width="50">
            <template slot-scope="scope">
              <el-tag type="success" disable-transitions v-if="scope.row.caseClosed == 1">是</el-tag>
              <el-tag type="warning" disable-transitions v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="closingTime" label="结案时间" min-width="50">
          </el-table-column>
          <el-table-column prop="closingInformation" label="结案信息" align="center" min-width="360">
            <template #default="scoped">
              <el-tooltip placement="top" class="custom-tooltip" :content="scoped.row.closingInformation">
                <span class="autoScroll">
                  {{ scoped.row.closingInformation }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- </el-table-column> -->

        <el-table-column prop="transferredToPolice" label="移送公安机关" min-width="50">
          <template slot-scope="scope">
            <el-tag type="success" disable-transitions v-if="scope.row.transferredToPolice == 1">是</el-tag>
            <el-tag type="success" disable-transitions v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="involvedInOrganizedCrime" label="是否涉黑涉恶" min-width="50">
          <template slot-scope="scope">
            <el-tag type="success" disable-transitions v-if="scope.row.involvedInOrganizedCrime == 1">是</el-tag>
            <el-tag type="success" disable-transitions v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="整治情况" width="180" align="center">
          <el-table-column prop="remediationMeasuresDefined" label="制定整治措施" min-width="50">
            <template slot-scope="scope">
              <el-tag type="success" disable-transitions v-if="scope.row.remediationMeasuresDefined == 1">是</el-tag>
              <el-tag type="success" disable-transitions v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="responsibleUnit" label="整治单位" min-width="50">
          </el-table-column>
          <el-table-column prop="chargePerson" label="整治责任人" min-width="50">
          </el-table-column>
          <el-table-column prop="completionDeadline" label="完成时限" min-width="50">
          </el-table-column>
          <!-- <el-table-column prop="rectificationCompleted" label="是否完成整改" min-width="50">
              <template slot-scope="scope">
                <el-tag type="success" disable-transitions v-if="scope.row.rectificationCompleted==1">是</el-tag>
                <el-tag type="warning" disable-transitions v-else>否</el-tag>
              </template>
            </el-table-column> -->
          <el-table-column prop="regulationMeasure" label="整治措施" align="center" min-width="360">
            <template #default="scoped">
              <el-tooltip placement="top" class="custom-tooltip" :content="scoped.row.regulationMeasure">
                <span class="autoScroll">
                  {{ scoped.row.regulationMeasure }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="liability" label="追究有关人员" min-width="50">
            <template slot-scope="scope">
              <el-tag type="success" disable-transitions v-if="scope.row.liability == 1">是</el-tag>
              <el-tag type="warning" disable-transitions v-else>否</el-tag>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="jointLawEnforcement" label="联合执法" min-width="50">
            <template slot-scope="scope">
              <el-tag type="success" disable-transitions v-if="scope.row.jointLawEnforcement==1">是</el-tag>
              <el-tag type="warning" disable-transitions v-else>否</el-tag>
            </template>
          </el-table-column> -->
        <!-- <el-table-column width="180" label="联合执法" align="center">
            <el-table-column prop="waterEnforcementPerson" label="水行政执法人员" min-width="50">
            </el-table-column>
            <el-table-column prop="townshipPerson" label="乡镇行政执法人员" min-width="50">
            </el-table-column>
            <el-table-column prop="policePerson" label="警务人员" min-width="50">
            </el-table-column>
            <el-table-column prop="illegalSandMining" label="查处非法采砂" min-width="50">
              <template slot-scope="scope">
                <span>{{scope.row.illegalSandMining?scope.row.illegalSandMining+'(处)':''}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="otherPerson" min-width="60">
              <template slot="header">
                         <div>
                          <el-tooltip class="item" effect="dark" content="自然资源/交通运输" placement="top">
                           <span>其它行政执法人员</span>
                           </el-tooltip>
                         </div>
                     </template>
            </el-table-column>
            <el-table-column prop="criminalFiling" label="刑事立案数" min-width="50">
              <template slot-scope="scope">
                <span>{{scope.row.criminalFiling?scope.row.criminalFiling+'(起)':''}}</span>
              </template>
            </el-table-column>
          </el-table-column> -->
        <el-table-column prop="notes" label="备注" min-width="360" align="center">
          <template #default="scoped">
            <el-tooltip placement="top" class="custom-tooltip"  :content="scoped.row.notes">
              <span class="autoScroll">
                {{ scoped.row.notes }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="caozuo" label="操作" min-width="60" align="center" fixed="right" v-if="state == 0">
          <template slot-scope="scope">
            <el-button type="text" @click="modify(scope.row)"
              v-if="state == 0 && (scope.row.caseClosed == 0 || scope.row.liability == 0)">修改</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="areaName" label="附件" min-width="50" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="subUp(scope.row, 'add')" v-if="state == 0">上传</el-button>
            <el-button type="text" @click="subUp(scope.row, 'view')" v-if="state == 1">查看</el-button>
          </template>
        </el-table-column>
        </el-table-column>

      </el-table>
      <div ref="foolter">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
        style="text-align: right;margin-top:5px" :current-page="formInline.pageNum" :page-sizes="[10, 20, 50, 100]"
        :page-size="10" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
      </div>
    </el-row>
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <!-- <my-pv-upload label="附件上传" :multiple="false" :uploadMode="'image'" :file-list="imgList"
        :pathFieldName="pathFieldName1" :fileType="fileType1" :disabled="mode=='view'" @getFileList="getFileList1">
      </my-pv-upload> -->
      <MyFileUploadLi :file-list="imgList" :pathFieldName="pathFieldName1" :fileType="fileType1"
        @getFileList="getFileList1" :disabled="mode == 'view'" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleView" v-if="mode == 'add'">确 定</el-button>
        <el-button @click="open = false">{{ mode == 'add' ? '取 消' : '关 闭' }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title1" :visible.sync="open1" width="400px" append-to-body>
      <div class="demo-image__preview">
        <el-image style="width: 100px; height: 100px" :src="url" :preview-src-list="srcList">
        </el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open1 = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title2" :visible.sync="open2" width="800px" append-to-body>
      <el-form :model="form1" :rules="rules1" ref="form1">
        <el-card>
          <div class="isover">
            <el-form-item label="是否结案" prop="caseClosed" label-width="80px">
              <el-switch v-model="form1.caseClosed" class="tableScopeSwitch" active-text="是" inactive-text="否"
                active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly1" @change="mychangge">
              </el-switch>
            </el-form-item>
          </div>
          <div class="isover" v-show="form1.caseClosed == true">
            <el-form-item label="结案时间" prop="closingTime" label-width="100px" v-show="form1.caseClosed == true">
              <el-date-picker class="custom-width" v-model="form1.closingTime" type="month" placeholder="请选择日期"
                value-format="yyyy-MM" :disabled="isreadonly1">
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="isover" v-show="form1.caseClosed == true">
            <el-form-item label="结案信息" prop="closingInformation" label-width="100px">
              <el-input class="custom-widthAll" type="textarea" :rows="5" placeholder="案件办理情况"
                v-model="form1.closingInformation" :readonly="isreadonly1">
              </el-input>
            </el-form-item>
          </div>
        </el-card>
        <el-card>
          <div class="isover">
            <el-form-item label="是否追责" prop="liability" label-width="80px">
              <el-switch v-model="form1.liability" class="tableScopeSwitch" active-text="是" inactive-text="否"
                active-color="#1890ff" inactive-color="#DCDFE6" :disabled="isreadonly2">
              </el-switch>
            </el-form-item>
            <el-form-item label="追责人数" prop="liabilityNumberPersons" label-width="80px"
              v-show="form1.liability == true">
              <el-input-number v-model="form1.liabilityNumberPersons" @change="handleChange" :min="0" :max="10"
                label="描述文字" :disabled="isreadonly2"></el-input-number>
            </el-form-item>

          </div>
          <div class="isover">
            <div class="mycustomName" v-show="form1.liability == true">
              <div v-for="(item, index) in form1.reportPersonList" :key="index" class="autoNames">
                <el-form-item label="姓名" :prop="'reportPersonList.' + index + '.liabilityName'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入姓名" v-model="item.liabilityName"
                    :disabled="isreadonly2"></el-input>
                </el-form-item>
                <el-form-item label="职务" :prop="'reportPersonList.' + index + '.liabilityPost'" label-width="100px">
                  <el-input class="custom-width" placeholder="请输入职务" v-model="item.liabilityPost"
                    :disabled="isreadonly2"></el-input>
                </el-form-item>
                <el-form-item label="追责问责形式" :prop="'reportPersonList.' + index + '.accountability'"
                  label-width="120px">
                  <el-input class="custom-width" placeholder="请输入问责形式" v-model="item.accountability"
                    :disabled="isreadonly2"></el-input>
                </el-form-item>
                <!-- <el-form-item>
                 <i class="el-icon-delete" @click="deleteItem(item, index)"></i>
             </el-form-item> -->
              </div>
            </div>
          </div>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open2 = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTableDataNew,
  upFiledId,
  getUploadFile,
  updateCaseClosed,
  getCitys,
  getAreaName
} from "@/api/tablebbtb/bbtb";
import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
import DictUtils from "@/utils/dictUtils";
import MyPvUpload from "@/components/YB/MyPvUpload";
import MyFileUploadLi from "@/components/YB/MyFileUploadLi";
import * as XLSX from 'xlsx';
import qs from 'qs'
export default {
  name: 'HelloWorld',
  components: {
    MyAreaSelect: MyAreaSelect,
    MyPvUpload: MyPvUpload,
    MyFileUploadLi: MyFileUploadLi
  },
  data() {
    return {
      tableHeight: 0,
      resizeTimer: null,
      allStatus: [{
        name: '待审核',
        value: 0
      }, {
        name: '已通过',
        value: 1
      }],
      url: '',
      srcList: [],
      title: '附件上传',
      open: false,
      title1: '附件查看',
      open1: false,
      //印章图片
      imgList: [],
      parentImgList: [],
      dialogVisible: false,
      dialogImageUrl: "",
      pathFieldName1: 'project-file',
      mode: 'add',
      fileType1: '.pdf,.bmp,.gif,.jpeg,.jpg,.png', //印章图片类型
      dialogVisible: false,
      sourceDate: '',
      // 区域选择框默认选中
      parentAreaCode: "",
      childrenAreaCode: "",
      parentAreaCode1: "",
      childrenAreaCode1: "",
      isReadOnlyParent: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren: false, // 控制子下拉框是否可编辑
      isReadOnlyParent1: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren1: false, // 控制子下拉框是否可编辑
      isClear: false, // 父选择框是否可清空
      timeData: null, //时间
      formInline: {
        pageNum: 1,
        pageSize: 10,
        // issueType: "", //报告类型
        areaName: "", //区域名称
        areaCode: "", //区域编码
        startTime: null, //开始时间
        endTime: null, //结束时间
        year: '',
        month: ''
      },
      total: 0,
      tableData: [],
      multipleSelection: [],
      pramars: {
        ids: []
      },
      myfiles: undefined,
      filingTime: undefined,
      spanArr: [],// 存储合并行数规则
      //自定义状态，0是县级填报人1是市级填报人
      state: 0,
      deptname: '',
      deptcode: '',
      rolenames: '',
      open2: false,
      title2: '修改',
      form1: {
        caseClosed: undefined,
        closingTime: undefined,
        closingInformation: undefined,
        liability: undefined,
        liabilityNumberPersons: undefined,
        reportPersonList: []
      },
      oldnum: 0,
      isreadonly1: false,
      isreadonly2: false,
      rules1: {
        closingTime: [{
          validator: (rule, value, callback) => {
            if (this.form1.caseClosed) {
              if (value && typeof value === 'string') {
                callback();
              } else {
                callback(new Error('请选择结案时间'));
              }
            } else {
              callback();
            }
          },
          trigger: 'change'
        }],

      },
      downName: ''
    };
  },
  created() {
    this.getTable();
    DictUtils.cList('sourceTypes').then(r => {
      console.log(r, '这个')
      this.sourceDate = r.sourceTypes
    });
    this.rolenames = this.$store.state.user.rolenames;
    this.deptcode = this.$store.state.user.deptcode;
    this.deptname = this.$store.state.user.deptname;
    console.log(this.$store.state.user)
    if (this.$store.state.user.deptcode.length <= 9) {
      this.state = 1;
    } else if (this.$store.state.user.deptcode.length > 9) {
      this.state = 0;
    }
    this.getCityNames();
  },
  mounted() {
    this.$nextTick(() => {
      this.calcTableHeight()
    })
        window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
    },
  methods: {
      setRowClass({ row,columnIndex }) {
        console.log(row,"6666")
    },
     setCellClass({ row,columnIndex }) {
    return row.caseClosed === '0' && columnIndex>=2 ? 'success-row' : '';
  },
    calcTableHeight() {
          const foolter = this.$refs.foolter.clientHeight; // 上下边距总和
          const otherHeight = this.$refs.myHeight.clientHeight; // 表头等其他元素高度
          const containerHeight = this.$refs.tableContainer.clientHeight;
          console.log(containerHeight,otherHeight)
          this.tableHeight = containerHeight - otherHeight - foolter - 40;
        },
        handleResize() {
          if (this.resizeTimer){
            clearTimeout(this.resizeTimer)
            }
          this.resizeTimer = setTimeout(() => {
            this.calcTableHeight()
          }, 200)
        },
    getCityNames() {
      getAreaName().then((res) => {
        console.log(res, '我获取的')
        let data = {
          areaCode: res.AREA_CODE
        }
        getCitys(data).then(resTwo => {
          console.log(resTwo, '缓过来的')
          this.downName = resTwo.fileName;
        });
      });
    },
    isOverflow(text) {
      const lineHeight = 24; // 根据实际行高调整
      const maxHeight = lineHeight * 11;
      const tempDiv = document.createElement('div');
      tempDiv.style.width = '160px'; // 匹配列宽
      tempDiv.style.position = 'absolute';
      tempDiv.style.visibility = 'hidden';
      tempDiv.style.lineHeight = `${lineHeight}px`;
      tempDiv.innerHTML = text;
      document.body.appendChild(tempDiv);
      const isOverflow = tempDiv.clientHeight > maxHeight;
      document.body.removeChild(tempDiv);
      return isOverflow;
    },
    /*本界面主要用来展示案件，按年度去展示，有可能会有导出报表操作*/
    mychangge(e) {
      console.log(e, '这里是否执行')
      if (e == false) {
        this.form1.closingTime = undefined;
        this.form1.closingInformation = '';
      }
    },
    handleChange(value) {
      let num = this.oldnum;
      if (value > num) {
        this.addItem();
        this.form1.liabilityNumberPersons = value;
        this.oldnum = value;
      } else {
        this.deleteItem(this.form1.liabilityNumberPersons - 1);
        this.form1.liabilityNumberPersons = value;
        this.oldnum = value;
      }
    },
    addItem() {
      console.log(this.form1, '没走到这里')
      this.form1.reportPersonList = this.form1.reportPersonList ? this.form1.reportPersonList : [];
      this.form1.reportPersonList.push({
        liabilityName: '',
        liabilityPost: '',
        accountability: '',
      })
    },
    deleteItem(index) {
      this.form1.reportPersonList.splice(-1)
    },
    modify(row) {
      this.open2 = true;
      console.log(row)
      let data = JSON.parse(JSON.stringify(row));
      this.isreadonly1 = data.caseClosed == 0 ? false : true;
      this.isreadonly2 = data.liability == 0 ? false : true;
      data.caseClosed = data.caseClosed == 0 ? false : true;
      data.liability = data.liability == 0 ? false : true;
      this.form1 = {
        id: data.id,
        caseClosed: data.caseClosed,
        closingTime: data.closingTime,
        closingInformation: data.closingInformation,
        liability: data.liability,
        liabilityNumberPersons: data.liabilityNumberPersons,
        reportPersonList: data.reportPersonList
      };
    },
    /** 提交按钮 */
    submitForm(status) {

      this.$refs["form1"].validate((valid, errorObj) => {
        if (valid) {
          let data = {
            id: this.form1.id,
            caseClosed: this.form1.caseClosed == false ? 0 : 1,
            closingTime: this.form1.closingTime,
            closingInformation: this.form1.closingInformation,
            liability: this.form1.liability == false ? 0 : 1,
            liabilityNumberPersons: this.form1.liabilityNumberPersons,
            reportPersonList: this.form1.reportPersonList ? this.form1.reportPersonList : []
          }
          console.log(data)
          updateCaseClosed(data).then(res => {
            if (res.msg == 'success') {
              this.$modal.msgSuccess("修改成功！");
              this.getTable();
            } else {
              this.$modal.msgWarning("修改失败！");
              this.getTable();
            }
            this.open2 = false;
            this.reset();
          });
        } else {
          this.$scrollView(errorObj);
        }
      });

    },
    reset() {
      this.form1 = {
        caseClosed: undefined,
        closingTime: undefined,
        closingInformation: undefined,
        liability: undefined,
        liabilityNumberPersons: undefined,
        reportPersonList: []
      };
    },
    subUp(e, type) {
      this.filingTime = undefined;
      this.filingTime = e.filingTime;
      if (type == 'add') {
        this.mode = 'add';
        this.open = true;
        this.title = '附件上传';
      } else {
        this.mode = 'view';
        this.open = true;
        this.title = '附件查看';
      }
      this.getFile(e.filingTime, e.areaCode)
    },
    getFile(filingTime, areaCode) {
      let datas = {
        filingTime: filingTime,
        areaCode: areaCode
      }
      getUploadFile(datas).then((res) => {
        this.handelFileList(res)
      });
    },
    handleView(scope) {
      let datas = {
        fileId: this.myfiles[0].id,
        filingTime: this.filingTime
      }
      upFiledId(datas).then((res) => {
        if (res.msg == 'success') {
          this.$modal.msgSuccess("上传成功！");
          this.open = false;
        } else {
          this.$modal.msgWarning("上传失败！");
          this.open = false;
        }
      });
    },
    handleClose() {
      this.open = false;
    },
    getFileList1(fileList) {
      this.myfiles = fileList
    },
    handelFileList(r) {
      if (r.uploadFilePath) {
        this.imgList = [
          {
            name: r.uploadFileName,
            url: r.uploadFilePath,
            status: "success",
            uploadFile: r,
          }
        ]
        this.url = r.uploadFilePath;
        this.srcList = [r.uploadFilePath];
      } else {
        this.imgList = [];
      }
    },
    // simpleSpanMethod({ row, column, rowIndex }) {
    //   const currentProp = column.property;
    //   // 仅处理 filingTime 列的合并逻辑
    //   if (currentProp === 'filingTime') {
    //     // 获取前一行数据（处理首行边界）
    //     const prevRow = rowIndex > 0 ? this.tableData[rowIndex - 1] : null;

    //     // 判断是否需要开启新合并块（满足以下条件之一则开启）：
    //     // 1. 当前是首行
    //     // 2. 当前行时间与前一行不同
    //     // 3. 当前行城市名称与前一行不同
    //     if (!prevRow ||
    //         row.filingTime !== prevRow.filingTime ||
    //         row.areaName !== prevRow.areaName) {

    //       let count = 1;
    //       // 向后查找连续满足时间和城市相同的行
    //       while (
    //         rowIndex + count < this.tableData.length &&
    //         this.tableData[rowIndex + count].filingTime === row.filingTime &&
    //         this.tableData[rowIndex + count].areaName === row.areaName
    //       ) {
    //         count++;
    //       }
    //       return { rowspan: count, colspan: 1 };
    //     } else {
    //       // 当前行属于已合并区域，隐藏单元格
    //       return { rowspan: 0, colspan: 0 };
    //     }
    //   }
    // },
    simpleSpanMethod({ row, column, rowIndex }) {
      const currentProp = column.property;
      // 同时处理 filingTime 和 areaName 列
      if (['filingTime', 'areaName'].includes(currentProp)) {
        const prevRow = rowIndex > 0 ? this.tableData[rowIndex - 1] : null;

        // 关键判断条件调整
        const isNewBlock = !prevRow ||
          row.filingTime !== prevRow.filingTime ||
          (currentProp === 'areaName' && row.areaName !== prevRow.areaName);

        if (isNewBlock) {
          let count = 1;
          // 查找连续相同 filingTime + areaName 的行
          while (
            rowIndex + count < this.tableData.length &&
            this.tableData[rowIndex + count].filingTime === row.filingTime &&
            this.tableData[rowIndex + count][currentProp] === row[currentProp]
          ) {
            count++;
          }
          return { rowspan: count, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },

    getNames(value) {
      const matchedItem = this.sourceDate.find(item => item.value === value);
      return matchedItem ? matchedItem.name : ''; // 如果没有找到，返回空字符串或其他默认值
    },
    handleExport() {
      if (this.timeData == null) {
        this.$modal.msgWarning("请选择要导出的台账年月")
        return
      }
      this.$confirm('是否要导出' + this.timeData[0] + '至' + this.timeData[1] + '的案件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pramars.ids = this.multipleSelection;
        let data = {
          startTime: this.timeData[0],
          endTime: this.timeData[1],
          areaCode: this.formInline.areaCode
        }
        if (this.formInline.areaCode && this.formInline.areaCode != '') {
          let data1 = {
            areaCode: this.formInline.areaCode
          }
          getCitys(data1).then(resTwo => {
            console.log(resTwo, '缓过来的')
            let downName = resTwo.fileName;
            this.download('statement/exportStatement', data, `${this.timeData[0] + '至' + this.timeData[1]}月${downName}`, 'application/json', 1)
          });
        } else {
          this.download('statement/exportStatement', data, `${this.timeData[0] + '至' + this.timeData[1]}月${this.downName}`, 'application/json', 1)
        }
        // let queryString = qs.stringify(data)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });

    },
    handleSelectionChange(val) {
      this.multipleSelection = []
      val.forEach((item) => {
        this.multipleSelection.push(item.id)
      })
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('statement/exportStatement', {}, `打击河道非法采砂情况统计表.xlsx`)
    // },
    //获取列表数据
    getTable() {
      console.log(this.formInline)
      getTableDataNew(this.formInline).then((res) => {

        this.tableData = res.records;
        this.total = res.total;
        this.$nextTick(() => {
          const tableWrapper = this.$refs.table?.$el.querySelector('.el-table__body-wrapper')
          if (tableWrapper) tableWrapper.scrollTop = 0
        })
      });
    },
    //分页
    handleSizeChange(val) {
      this.formInline.pageNum = 1
      this.formInline.pageSize = val
      this.getTable()
    },
    handleCurrentChange(val) {
      this.formInline.pageNum = val
      this.getTable()

    },
    // 搜索按钮
    onSubmit() {
      this.formInline.pageNum = 1;
      this.formInline.startTime = this.timeData ? this.timeData[0] : '';
      this.formInline.endTime = this.timeData ? this.timeData[1] : '';;
      // if (this.timeData) {
      //   this.formInline.startTime = this.timeData[0];
      //   this.formInline.endTime = this.timeData[1];
      // } else {
      //   this.formInline.startTime = null;
      //   this.formInline.endTime = null;
      // }
      this.getTable()
    },
    // 重置按钮
    onReset() {
      this.formInline = {
        pageNum: 1,
        pageSize: 10,
        // issueType: "", //报告类型
        areaName: "", //区域名称
        areaCode: "", //区域编码
        startTime: null, //开始时间
        endTime: null, //结束时间
        year: '',
        month: ''
      }
      this.timeData = null;
      this.getTable()
    },
    // /** 导出按钮操作 */
    // handleExport() {
    //   this.download('bill/bill/exportBill', {
    //     ...this.queryParams
    //   }, `运砂单据_${new Date().getTime()}.xlsx`, 'application/json');
    // },
  }
};
</script>
<style>
.el-tooltip__popper {
  max-width: 40%;
}
</style>
<style scoped>
/* 背景色 */
h1 {
  color: #42b983;
}
.app-container{
  height: calc(100vh - 84px); /* 根据实际布局调整 */
    width: 100%;
    overflow: hidden;
}
.autoScroll {
  display: block;
  text-align: left !important;
  white-space: normal !important;
  /* 允许换行 */
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 11;
  overflow: hidden;
  line-height: 24px;
  /* 与 JS 中 lineHeight 一致 */
}

::v-deep .el-tag {
  padding: 0 6px;
}

.autoNames {
  display: flex;
  margin-bottom: 10px;
}

.isover {
  display: flex;
  margin-bottom: 10px;
}

.custom-widthAll {
  width: 500px;
}

.lineCenter {
  line-height: 36px;
}

.isleft {
  margin-left: 28px;
}

.isright {
  margin-right: 10px;
}

.custom-width {
  width: 120px;
}

.isoverName {
  margin-right: 10px;
  /* color: #333333; */
  font-weight: 600;
}

::v-deep .custom-header {
  height: 0px !important;
}

::v-deep .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  height: 0px !important;
  line-height: 0px;
  /* border: 0 !important; */
  padding: 0;
  margin: 0;
}

::v-deep .el-image-viewer__wrapper {
  z-index: 9999999 !important;
}

::v-deep .tableScopeSwitch .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

/*打开时文字位置设置*/

::v-deep .tableScopeSwitch .el-switch__label--right {
  z-index: 1;
  right: 20px;
  /*不同场景下可能不同，自行调整*/
}

/*关闭时文字位置设置*/

::v-deep .tableScopeSwitch .el-switch__label--left {
  z-index: 1;
  left: 20px;
  /*不同场景下可能不同，自行调整*/
}

/*显示文字*/

::v-deep .tableScopeSwitch .el-switch__label.is-active {
  display: block;
}
::v-deep  .el-table .success-row {
  background-color: #e1f4aa !important;
}

</style>
