<template>
  <div class="app-container">
    <el-form ref="elForm" :model="formData">
      <el-row>
        <el-col :span="24">
          <el-form-item label="项目建设范围坐标" prop="coords">
            <div v-for="item in listCoord" v-show="item.visible"
            style="display:flex;flex-direction:row;align-items:center;">
              <el-tooltip content="弃置当前拐点" placement="left">
                <i style="font-size:20px;color:red;cursor:pointer;margin-right:12px;" class="el-icon-error"
                  @click="onClickMinusCoordInput(item.id)"></i>
              </el-tooltip>
              <input-coords @selected="onSelectCoordInput" :ref="item.ref" ></input-coords>
            </div>
            <el-tooltip content="添加拐点" placement="left">
              <i style="font-size:20px;color:green;cursor:pointer;" class="el-icon-circle-plus"
                @click="onClickPlusCoordInput"></i>
            </el-tooltip>
            <br><el-button size="mini" type="primary" @click="onClickPlot">去地图上勾绘</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <geo-plotter ref="geoPlotter" @plotted="onPlotted"></geo-plotter>
    <div slot="footer">
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import InputCoords from '@/views/components/input-coords/index.vue'
import GeoPlotter from "@/views/components/plotter/index.vue"
export default {
  components: {
    InputCoords,
    GeoPlotter,
  },
  data() {
    return {

      formData:{

      },

      listCoord: [
        { ref: "coord_0", id: 0, visible: true },
      ],
      mapDialogs: {
        "road": null,
        "pipe": null,
        "wire": null,
        "park": null,
      },
      projectToBeDeleted: {},
    }
  },
  methods: {
    onClickMinusCoordInput(k) {
      this.listCoord[k].visible = false;
    },
    onClickPlusCoordInput() {
      let n = this.listCoord.length;
      this.listCoord.push({
        ref: "coord_" + n, id: n, visible: true,
      });
      this.$nextTick(() => {
        let el0 = this.$refs[this.listCoord[0].ref][0];
        let el = this.$refs[this.listCoord[n].ref][0];
        el.mode = el0.mode;
        el.mc.centralMeridian = el0.mc.centralMeridian;
      });
    },
    onSelectCoordInput(m, cm) {
      for (let i = 0; i < this.listCoord.length; i++) {
        let el = this.$refs[this.listCoord[i].ref][0];
        el.mode = m;
        el.mc.centralMeridian = cm;
      }
    },
    onClickPlot(){
      let caller = {};
      let coordsCheckMsg = this.checkCoords();
      if (coordsCheckMsg != "") {
        caller.msg = coordsCheckMsg + "；从头开始绘制";
        caller.geojson = null;
        caller.typeAllowed = 30;
        this.onClickMap(caller);
        return;
      }
      let coordsCurrent = this.toCoords();
      caller.typeAllowed = 30;
      caller.geojson = coordsCurrent.coordsGeojson;
      caller.msg = "检查到已有坐标信息，可修改或重新绘制";
      this.onClickMap(caller);
    },
    checkCoords(){
      let n = this.listCoord.length;
      let vv = [];
      for (let i = 0; i < n; i++) {
        let visible = this.listCoord[i].visible;
        if (visible) {
          let el = this.$refs[this.listCoord[i].ref][0];
          let v = el.getValue();
          if (v.msg != "OK") {
            return ("第" + (vv.length+1) + "个拐点坐标：" + v.msg);
          } else {
            vv.push(v);
          }
        }
      }
      let k = vv.length;
      if (k < 1) {
        return ("尚未填写项目建设范围坐标");
      }
      return "";
    },
    toCoords(){
      let n = this.listCoord.length;
      let vv = [];
      for (let i = 0; i < n; i++) {
        let visible = this.listCoord[i].visible;
        if (visible) {
          let el = this.$refs[this.listCoord[i].ref][0];
          let v = el.getValue();
          if (v.msg != "OK") {
            return null;
          } else {
            vv.push(v);
          }
        }
      }
      let k = vv.length;
      if (k < 1) {
        return null;
      }
      let result = {};
      // 构建coord字段的值，以用户填写的内容为准
      let s = "";
      for (let i = 0; i < k; i++) {
        s += vv[i].xx + "," + vv[i].yy + ";";
      }
      result.coordsString = s;

      // 根据点数构建geojson
      let geo = {};
      if (k == 1) { // 一个点
        geo.type = "Point";
        geo.coordinates = [vv[0].lng, vv[0].lat];
      }
      if (k == 2) { // 一条线
        geo.type = "LineString";
        geo.coordinates = [
          [vv[0].lng, vv[0].lat],
          [vv[1].lng, vv[1].lat]
        ];
      }
      if (k > 2) {
        geo.type = "Polygon";
        let lls = [];
        for (let i = 0; i < k; i++) {
          let v = vv[i];
          lls.push([v.lng, v.lat]);
        }
        lls.push([vv[0].lng, vv[0].lat])
        geo.coordinates = [lls];
      }
      result.coordsGeojson = JSON.stringify(geo);
      return result;
    },

    onClickMap(caller){
      this.$refs.geoPlotter.msgFromCaller = caller.msg;
      this.$refs.geoPlotter.geojson = caller.geojson;
      this.$refs.geoPlotter.typeAllowed = caller.typeAllowed;
      this.$refs.geoPlotter.visible = true;
    },
    // onPlotted(s){
    //   this.mapDialogs["road"].onPlotted(s);
    //   this.mapDialogs["pipe"].onPlotted(s);
    //   this.mapDialogs["wire"].onPlotted(s);
    //   this.mapDialogs["park"].onPlotted(s);
    // },

    onPlotted(coordsString){
      // if (!this.visible) {
      //   return;
      // }
      this.fromCoords(coordsString);
    },
    fromCoords(coordsString){
      if (!coordsString) {
        return;
      }
      let ss = coordsString.split(";");
      if (ss.length == 0) {
        return;
      }
      this.listCoord = [];
      for (let i = 0; i < ss.length; i++) {
        if (!ss[i]) {
          continue;
        }
        this.listCoord.push({
          ref: "coord_" + i, id: i, visible: true,
        });
        let ll = ss[i].split(",");
        if (ll.length < 2) {
          continue;
        }
        this.$nextTick(() => {
          let el = this.$refs[this.listCoord[i].ref][0];
          el.setValue(ll[0], ll[1]);
        });
      }
    },
    handleConfirm() {

      this.$refs['elForm'].validate(valid => {
        if (!valid) {
          return;
        }
        let coordsCheckMsg = this.checkCoords();
        if (coordsCheckMsg != "") {
          this.$modal.msgError(coordsCheckMsg);
          return;
        }
        let coordsCurrent = this.toCoords();
        this.formData.coords = coordsCurrent.coordsString;
        this.formData.geojson = coordsCurrent.coordsGeojson;
        console.log("this.formData.coords:" + this.formData.coords);
        console.log("this.formData.geojson:" + this.formData.geojson);
        console.log("this.listCoord:" + this.$refs[this.listCoord[0].ref][0]);
        // if (this.formData.id) {
        //   updateProjRoad(this.formData).then((res) => {
        //     this.$modal.msgSuccess("项目信息更新成功");
        //     this.$emit("enter", true);
        //     this.close();
        //   });
        // } else {
        //   addProjRoad(this.formData).then((res) => {
        //     // this.$modal.msgSuccess("项目信息创建成功，您可以在线调整其上图位置");
        //     this.$modal.msgSuccess("项目信息创建成功");
        //     this.$emit("enter", true);
        //     this.close();
        //   });
        // }
      })
    },
  },

}
</script>

<style scoped>

</style>
