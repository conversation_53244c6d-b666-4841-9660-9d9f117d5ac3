<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"
      @submit.native.prevent>
      <el-form-item label="项目名称" prop="name">
        <my-input style="width: 205px" v-model.trim="queryParams.name" placeholder="项目名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input style="width: 205px" v-model.trim="queryParams.sectionName" placeholder="标段名称"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目位置" prop="areaCode">
        <my-area-select v-model="queryParams.areaCode" />
      </el-form-item>
      <el-form-item label="项目类型" prop="type">
        <my-select id="type" pvalue="projectType" v-model="queryParams.type" placeholder="项目类型" />
      </el-form-item>
      <!-- <el-form-item label="审核状态" prop="auditStatus">
        <MyRadioGroup id="auditStatus" pvalue="auditStatus" v-model="queryParams.auditStatus" placeholder="审核状态"></MyRadioGroup>
        <my-select id="auditStatus" pvalue="auditStatus" v-model="queryParams.auditStatus" placeholder="审核状态" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/projectDeclare/waitPage" :fixed="true" ref="projectDeclareTable" row-key="deptId"
      @my-selection-change="handleSelectionChange">
      <el-table-column label="项目名称" min-width="250" fixed="left" header-align="center" align="left" prop="name"
        sortable="custom" column-key="NAME">
        <template #default="{ row }">
          <!-- <el-tooltip :content="row.name" placement="top" effect="light">
            <div>{{ row.name }}</div>
          </el-tooltip> -->
           <div>{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="标段名称" min-width="160" prop="sectionName" header-align="center" align="left"
        sortable="custom" column-key="SECTION_NAME"></el-table-column>
      <el-table-column label="项目位置" min-width="160" prop="areaName" header-align="center" align="left" sortable="custom"
        column-key="AREA_NAME"></el-table-column>
      <el-table-column label="项目类型" min-width="150" prop="type" header-align="center" align="center" sortable="custom"
        column-key="TYPE">
        <template #default="{ row }">
          <my-view pvalue="projectType" :value="row.type"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" min-width="150" prop="auditStatus" header-align="center" align="center"
        sortable="custom" column-key="AUDIT_STATUS">
        <template #default="{ row }">
          <my-view pvalue="auditStatus" :value="row.auditStatus"></my-view>
        </template>
      </el-table-column>
      <el-table-column label="采砂人（供砂人）" min-width="150" prop="leaderName" header-align="center" align="center"
        sortable="custom" column-key="LEADER_NAME"></el-table-column>
      <el-table-column label="联系电话" min-width="150" prop="contact" header-align="center" align="center"
        sortable="custom" column-key="CONTACT"></el-table-column>
      <el-table-column label="监管部门" align="left" header-align="center" min-width="150" prop="deptName" sortable="custom"
        column-key="dept.NAME"> </el-table-column>
        <!-- <el-table-column label="审批时间" align="left" header-align="center" min-width="150" prop="departmentAuditTime" sortable="custom"
          column-key="DEPTMARTMENT_AUDITTIME"> </el-table-column> -->
      <el-table-column label="控制总量（万吨）" min-width="150" header-align="center" align="right" prop="totalYield"
        sortable="custom" column-key="TOTAL_YIELD">
        <template #default="scope">
          {{ common.toThousands(scope.row.totalYield, 2, ',') }}
        </template>
      </el-table-column>
      <el-table-column label="采砂许可证/弃砂审批文号" min-width="200" header-align="center" align="center" prop="licenseNo"
        sortable="custom" column-key="LICENSE_NO"></el-table-column>
      <el-table-column label="操作" column-key="caozuo" min-width="150" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button size="medium" type="primary" style="font-size: 16px;" icon="el-icon-finished" title="审核"
            v-has-permi="['project:projectDeclare:audit']" @click="handleFill(scope.row,'sh')"
            v-if="scope.row.auditStatus == 'departmentWaiting' || state == 1"></el-button>
          <!-- <el-button size="mini" type="success" class="btn-table-operate" icon="el-icon-document-copy" title="查看"
              @click="handleFill(scope.row,'ck')" v-has-permi="['project:projectDeclare:audit']"></el-button> -->
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改项目/沙场信息表对话框 -->
    <my-dialog :zIndex="1500" :title="title" :visible.sync="open" width="950px" height="70vh" append-to-body
      @close="cancel">
      <el-tabs type="border-card" v-model="activeName">
        <el-tab-pane label="项目信息" name="first">

          <el-card class="box-card">
            <el-form ref="form" :model="projectDeclare" label-width="180px" :disabled="mode === 'view'">
              <my-form-item label="项目名称" ref="name" prop="name" :rules="[{ notNull: true, message: '请输入项目名称' }]">
                <my-input v-model.trim="projectDeclare.name" maxlength="50" placeholder="请输入项目名称" />
              </my-form-item>
              <my-form-item label="标段名称" ref="sectionName" prop="sectionName">
                <my-input v-model.trim="projectDeclare.sectionName" maxlength="30" placeholder="请输入标段名称" />
              </my-form-item>
              <my-form-item label="项目位置" ref="areaCode" prop="areaCode"
                :rules="[{ notNull: true, message: '请选择项目位置', trigger: 'change' }]">
                <my-area-select v-model="projectDeclare.areaCode" :component-type="true" :disabled="mode == 'view'"
                  @change="getAreaName" />
              </my-form-item>
              <my-form-item label="项目类型" ref="type" prop="type"
                :rules="[{ notNull: true, message: '请选择项目类型', trigger: 'change' }]">
                <my-select pvalue="projectType" v-model="projectDeclare.type" placeholder="请选择项目类型" />
              </my-form-item>
              <my-form-item v-if="mode === 'view'" label="监管部门" ref="deptName" prop="deptName">
                <my-input v-model.trim="projectDeclare.deptName" disabled="disabled" />
              </my-form-item>
              <my-form-item v-else label="监管部门" ref="parentId" prop="parentId"
                :rules="[{ notNull: true, message: '请选择监管部门', trigger: 'change' }]">
                <my-cascader ref="deptCascader" :disabled="mode === 'update'" v-model="projectDeclare.parentId"
                  placeholder="请选择监管部门" />
              </my-form-item>

              <my-form-item label="控制总量(万吨)" ref="totalYield" prop="totalYield" :rules="[{ notNull: true, message: '请输入控制总量(万吨)' },
              { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
                <my-input v-model="projectDeclare.totalYield" maxlength="20" placeholder="请输入控制总量(万吨)" />
              </my-form-item>
              <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="25年1月1日前利用量（万吨）" ref="historyYield"
                prop="historyYield" :rules="[{ notNull: true, message: '请输入25年1月1日前利用量（万吨）' },
                { pattern: /^(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/, message: '只能是数字' }]">
                <my-input v-model="projectDeclare.historyYield" maxlength="20" placeholder="请输入25年1月1日前利用量（万吨）" />
              </my-form-item>
              <my-form-item :label="projectDeclare.type == 'abandonSand' ? '弃砂方案批文及文号' : '采砂许可证号'" ref="licenseNo"
                prop="licenseNo"
                :rules="[{ notNull: true, message: projectDeclare.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号' }]">
                <my-input v-model="projectDeclare.licenseNo" maxlength="128"
                  :placeholder="projectDeclare.type == 'abandonSand' ? '请输入弃砂方案批文及文号' : '请输入采砂许可证号'" />
              </my-form-item>
              <my-form-item :label="projectDeclare.type == 'abandonSand' ? '供砂人' : '采砂人'" ref="leaderName"
                prop="leaderName"
                :rules="[{ notNull: true, message: projectDeclare.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人' }]">
                <my-input v-model="projectDeclare.leaderName" maxlength="128"
                  :placeholder="projectDeclare.type == 'abandonSand' ? '请输入供砂人' : '请输入采砂人'" />
              </my-form-item>
              <my-form-item label="联系电话" ref="contact" prop="contact" :rules="[{ notNull: true, message: '请输入联系电话' },
              { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
                <my-input v-model="projectDeclare.contact" placeholder="请输入联系电话" />
              </my-form-item>
              <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="是否国债项目" ref="nationalDebt"
                prop="nationalDebt" :rules="[{ notNull: true, message: '请选择是否国债项目' }]">
                <my-radio :options="[{ name: '是', value: true }, { name: '否', value: false }]"
                  v-model="projectDeclare.nationalDebt"></my-radio>
              </my-form-item>
              <my-form-item v-if="projectDeclare.nationalDebt == true && projectDeclare.type == 'abandonSand'"
                label="国债类型" ref="nationalDebtType" prop="nationalDebtType"
                :rules="[{ notNull: true, message: '请选择国债类型' }]">
                <my-select pvalue="nationalDebtType" v-model="projectDeclare.nationalDebtType" placeholder="请选择国债类型" />
              </my-form-item>
              <my-form-item v-if="projectDeclare.type == 'abandonSand'" label="处置方式" ref="disposalMethodJson"
                prop="disposalMethodJson" :rules="[{ notNull: true, message: '请选择处置方式' }]">
                <div v-for="item in projectDeclare.disposalMethodJson">
                  <el-row style="margin-bottom: 10px">
                    <el-col :span="12">
                      <el-checkbox v-model="item.selected">
                        <template>
                          <my-view pvalue="disposalMethod" :value="item.method"></my-view>
                        </template>
                      </el-checkbox>
                      <my-input v-if="item.selected && item.method == 'qiTa'" style="margin-left: 20px;width: 200px"
                        v-model.trim="item.remark" :maxlength="16" placeholder="请输入其他处置方式 " />
                    </el-col>
                    <el-col :span="12">
                      <div>
                        <my-form-item label="效益（万元）" v-if="item.selected" label-width="120px" class="asterisk">
                          <my-input v-model.trim="item.amount" :maxlength="16" placeholder="请输入效益（万元）" />
                        </my-form-item>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <div style="display: flex">
                  <div>总效益（万元）</div>
                  <div>{{ common.toThousands(investmentTotal, '4', ',') }}</div>
                </div>
              </my-form-item>
              <my-form-item v-if="projectDeclare.type != 'abandonSand'" label="项目总投资（万元）" ref="investment"
                prop="investment" :rules="[{ notNull: true, message: '请输入项目总投资（万元）' }]">
                <my-input v-model.trim="projectDeclare.investment" :maxlength="16" placeholder="请输入项目总投资（万元）" />
              </my-form-item>
              <my-form-item v-if="projectDeclare.type == 'riverSand' || projectDeclare.type == ''" label="治理河长（km）"
                ref="reverLength" prop="reverLength" :rules="[{ notNull: true, message: '请输入治理河长（km）' }]">
                <my-input v-model.trim="projectDeclare.reverLength" :maxlength="16" placeholder="请输入治理河长（km）" />
              </my-form-item>
              <my-form-item label="监督举报电话" ref="complaintMobile" prop="complaintMobile"
                :rules="[{ notNull: true, message: '请输入监督举报电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
                <my-input v-model.trim="projectDeclare.complaintMobile" placeholder="请输入监督举报电话" :maxlength="32" />
              </my-form-item>
              <my-form-item label="查询电话" ref="queryMobile" prop="queryMobile"
                :rules="[{ notNull: true, message: '请输入查询电话' }, { isMobileOrTel: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
                <my-input v-model.trim="projectDeclare.queryMobile" placeholder="请输入查询电话" :maxlength="32" />
              </my-form-item>
              <el-row>
                <el-col :span="10">
                  <my-form-item :label="projectDeclare.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" prop="imgList"
                    :rules="[{ required: true, validator: validatorImg, trigger: ['blur', 'change'] }]">
                    <my-pv-upload :label="projectDeclare.type == 'abandonSand' ? '供砂人印章' : '采砂人印章'" :multiple="false"
                      :uploadMode="'image'" :file-list="imgList" :pathFieldName="pathFieldName1" :fileType="fileType1"
                      :disabled="mode == 'view'" @getFileList="getFileList1">
                    </my-pv-upload>
                  </my-form-item>
                </el-col>
                <el-col :span="12">
                  <my-form-item label="监管部门印章" prop="imgList"
                    :rules="[{ required: true, validator: validatorImg2, trigger: ['blur', 'change'] }]">
                    <my-pv-upload label="监管部门印章" :multiple="false" :uploadMode="'image'" :file-list="parentImgList"
                      pathFieldName="parent-dept-img" :fileType="fileType1" :disabled="mode == 'view'"
                      @getFileList="getFileList2">
                    </my-pv-upload>
                  </my-form-item>
                </el-col>
              </el-row>

              <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="uploadFileList" prop="uploadFileList"
                :rules="[{ required: true, validator: validatorImg3, trigger: ['blur', 'change'] }]">
                <my-file-upload :file-list="projectDeclare.uploadFileList" :pathFieldName="pathFieldName"
                  :fileType="fileType" @getFileList="getFileList" :disabled="mode == 'view'" />
                <template #label>
                  <span>{{ projectDeclare.type == 'abandonSand' ? '附件（方案批文）' : '附件（采砂许可证）' }}</span>
                </template>
              </my-form-item>
              <my-form-item :label="mode == 'add' ? '附件上传' : '附件'" ref="paiMaiFileList" prop="paiMaiFileList"
                :rules="[{ required: true, validator: validatorImg3, trigger: ['blur', 'change'] }]"
                v-show="projectDeclare.type == 'abandonSand'">
                <my-file-upload :file-list="projectDeclare.paiMaiFileList" :pathFieldName="pathFieldName8"
                  :fileType="fileType2" @getFileList="getFileList8" :disabled="mode == 'view'" />
                <template #label>
                  <span>{{ projectDeclare.type == 'abandonSand' ? '附件（中拍文件）' : '' }}</span>
                </template>
              </my-form-item>
              <my-form-item label="上述信息填报人联系电话" ref="createUserMobile" prop="createUserMobile" :rules="[{ notNull: true, message: '请输入上述信息填报人联系电话' },
              { isMobile: true, message: '请输入正确的电话格式', trigger: ['blur', 'change'] }]">
                <my-input v-model="projectDeclare.createUserMobile" placeholder="请输入上述信息填报人联系电话" />
              </my-form-item>
            </el-form>
          </el-card>

          <el-card class="box-card mycard" style='margin-top: 20px;'
            v-if="$store.state.user.deptcode.length == 9 || $store.state.user.deptcode.length < 9">
            <div class="title-container">
              <div class="bordered-box">
                <!-- <h2 class="bordered-title">现场监管措施落实情况</h2> -->
                <p>
                  <el-form :class="{ 'disabled-form': $store.state.user.deptcode.length < 9 }" ref="shenpiForm"
                    :model="shenpiForm" label-width="210px" label-position="left">
                    <div class="content-grid">
                      <div class="monitor-area">
                        <h3 class="area-title">现场视频监管设施情况</h3>
                        <div class="allTitleCount">
                          <div class="allTextCount">
                            <el-form-item label="视频监控是否正常使用" required>
                              <!-- <el-checkbox v-model="shenpiForm.video"></el-checkbox> -->
                              <el-switch v-model="shenpiForm.video" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch>
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.video">
                              <el-input v-model="shenpiForm.videoCause" type="textarea"
                                placeholder="请输入视频监控未正常使用说明原因" />
                            </div>
                          </div>
                          <div class="allTextCount" v-if="!shenpiForm.video"></div>
                          <div class="allTextCount" v-if="shenpiForm.video">
                            <el-form-item label="参数和存储是否满足设计要求" label-width="250px" required>
                              <el-checkbox v-model="shenpiForm.technology"></el-checkbox>
                              <!-- <el-switch v-model="shenpiForm.technology" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch> -->
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.technology">
                              <el-input v-model="shenpiForm.technologyCause" type="textarea"
                                placeholder="请输入视频监控参数和存储未满足设计要求的原因" />
                            </div>
                          </div>
                        </div>
                        <div class="allTitleCount" v-if="shenpiForm.video">
                          <div class="allTextCount">
                            <el-form-item label="是否按照方案要求的点位、数量布设" label-width="250px" v-if="shenpiForm.video" required>
                              <el-checkbox v-model="shenpiForm.layout"></el-checkbox>
                             <!-- <el-switch v-model="shenpiForm.layout" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch> -->
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.layout">
                              <el-input v-model="shenpiForm.layoutCause" type="textarea"
                                placeholder="请输入视频监控未按照方案要求的点位、数量布设的原因" />
                            </div>
                          </div>
                          <div class="allTextCount" ></div>
                        </div>
                      </div>
                      <div class="other-area">
                        <h3 class="area-title">其它监管设施落实情况</h3>
                        <!-- 其他区域内容 -->
                        <div class="allTitleCount">
                          <div class="allTextCount">
                            <el-form-item label="喷淋设备是否正常使用" required>
                              <el-switch v-model="shenpiForm.spray" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch>
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.spray">
                              <el-input v-model="shenpiForm.sprayCause" type="textarea"
                                placeholder="请输入喷淋设备未正常使用说明原因" />
                            </div>
                          </div>
                          <div class="allTextCount">
                            <el-form-item label="公示牌是否安装到位" required>
                              <el-switch v-model="shenpiForm.billboard" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch>
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.billboard">
                              <el-input v-model="shenpiForm.billboardCause" type="textarea"
                                placeholder="请输入计重设施和相关配套未到位原因" />
                            </div>
                          </div>
                        </div>
                        <div class="allTitleCount">
                          <div class="allTextCount">
                            <el-form-item label="计重和配套设施是否安装到位" required>
                              <el-switch v-model="shenpiForm.other" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch>
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.other">
                              <el-input v-model="shenpiForm.otherCause" type="textarea"
                                placeholder="请输入计重设施和相关配套未到位原因" />
                            </div>
                          </div>
                          <div class="allTextCount">
                            <el-form-item label="监理是否到位" required>
                              <el-switch v-model="shenpiForm.supervisor" active-text="是" inactive-text="否"
                                active-color="#1890ff" inactive-color="#F56C6C">
                              </el-switch>
                            </el-form-item>
                            <div class="disbadeyu" v-if="!shenpiForm.supervisor">
                              <el-input v-model="shenpiForm.supervisorCause" type="textarea" placeholder="请输入监理未到位原因" />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="other-area">
                        <h3 class="area-title" style="left: 80px;">审核人</h3>
                      <div class="allTitleCount">
                        <div class="allTextCount">
                          <el-form-item label="审核人(科长)" label-width="120px" required>
                          </el-form-item>
                          <div class="disbadeyu">
                            <el-input v-model="shenpiForm.reviewer" type="text" placeholder="请输入审核人" />
                          </div>
                        </div>
                      </div>
                      </div>
                    </div>
                  </el-form>
                </p>
              </div>
            </div>
          </el-card>

        </el-tab-pane>
        <el-tab-pane label="操作记录" name="second" v-if="mode !== 'add'">
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item, index) in processList" :key="item.id">
              <td style="width: 30%">{{ item.createTime }}</td>
              <td>{{ item.createUser }}</td>
              <td>
                <my-view pvalue="auditPassed" :value="item.passed + ''"></my-view>
              </td>
              <td>{{ item.opinion }}</td>
            </tr>
          </table>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="pass" v-if="stick=='sh'">审核通过</el-button>
        <el-button type="warning" @click="reject" v-if="stick=='sh'">审核不通过</el-button>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </my-dialog>



  </div>
</template>

<script>
  import {
    getProjectDeclare,
    delProjectDeclare,
    delProjectDeclareBatch,
    addProjectDeclare,
    updateProjectDeclare,
    checkProjectDeclare
  } from '@/api/project/projectDeclare'
  import Template from '@/views/sms/template/index.vue'
  import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
    import MyRadioGroup from '@/components/YB/MyRadioGroup.vue'
  import GeoPlotter from '@/views/components/plotter/index.vue'
  import InputCoords from '@/views/components/input-coords/index.vue'
  import MyPvUpload from '@/components/YB/MyPvUpload.vue'
  import MyCascader from '@/components/YB/MyCascader.vue'
  import MyFileUpload from '@/components/YB/MyFileUpload.vue'
  import {
    delProjectFile
  } from '@/api/project/project'
  import {
    checkPassShi,
    checkPassXian
  } from '@/api/reports/diversionPersonReport'
  import common from '../../../utils/common'
  import {
    getProcessList
  } from '@/api/reports/responsiblePersion'

  export default {
    name: "ProjectReview",
    computed: {
      common() {
        return common
      },
      investmentTotal() {
        return this.projectDeclare.disposalMethodJson.reduce((total, item) => {
          if (this.projectDeclare.type == 'abandonSand') {
            if (item.selected) {
              total += Number(item.amount)
            }
            return total
          }
        }, 0)
      }
    },
    components: {
      MyFileUpload,
      MyCascader,
      MyPvUpload,
      InputCoords,
      GeoPlotter,
      MyAreaSelect,
      Template,
      MyRadioGroup
    },
    data() {
      return {
        stick: 'sh',
        shenpiForm: {},
        deptId: '',
        processList: [],
        activeName: 'first',
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          name: '',
          sectionName: '',
          type: '',
          auditStatus: '',
          areaCode: '',
        },
        // 表单参数
        projectDeclare: {
          disposalMethodJson: [{
              method: 'paiMai',
              amount: '',
              selected: false
            },
            {
              method: 'shiZheng',
              amount: '',
              selected: false
            },
            {
              method: 'qiTa',
              amount: '',
              selected: false,
              remark: ''
            }
          ],
        },
        mode: '',
        // 文件类型  常用文件类型：.txt,.doc,.docx,.pdf,.xls,.xlsx,.bmp,.gif,.jpeg,.png,.jpg,.zip,.rar,.7z,.tar
        fileType: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
        fileType1: '.png', //印章图片类型
        fileType2: '.pdf,.bmp,.gif,.jpeg,.jpg,.png',
        //印章图片
        imgList: [],
        parentImgList: [],
        dialogVisible: false,
        dialogImageUrl: "",
        // 上传类型
        pathFieldName: 'project-file',
        pathFieldName1: 'seal-image-img',
        pathFieldName8: 'projectPaiMai-file',
        // 是否显示附件列表弹出层
        openFileList: false,
        row: '',
        state: '',
      };
    },

    created() {
      if (this.$store.state.user.deptcode.length == 9) {
        this.state = 1; //市级
      } else if (this.$store.state.user.deptcode.length > 9) {
        this.state = 0; //县级
      } else if (this.$store.state.user.deptcode.length < 9) {
        this.state = 2; //省级
      }
      console.log(this.state, '用户角色');
    },
    mounted() {

    },
    methods: {
      handleClick(tab, event) {
        this.processList = []
        getProcessList(this.deptId).then(res => {
          this.processList = res.list
        })
      },
      validatorImg(rule, value, callback) {
        if (this.projectDeclare.fileList == null || this.projectDeclare.fileList.length <= 0) {
          callback(new Error("请上传印章图片"));
        } else {
          callback();
        }
      },
      validatorImg2(rule, value, callback) {
        if (this.projectDeclare.parentImgList == null || this.projectDeclare.parentImgList.length <= 0) {
          callback(new Error("请上传印章图片"));
        } else {
          callback();
        }
      },
      validatorImg3(rule, value, callback) {
        if (this.projectDeclare.uploadFileList == null || this.projectDeclare.uploadFileList.length <= 0) {
          callback(new Error("请上传附件"));
        } else {
          callback();
        }
      },
      /** 查询项目信息申报表列表 */
      reload(restart) {
        this.$refs.projectDeclareTable.search(this.queryParams, restart);
        this.single = true;
        this.multiple = true;
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
        this.activeName = 'first'
      },
      pass() {
        if (!this.shenpiForm.video && !this.shenpiForm.videoCause) {
          this.$message.error('请填写视频监控未正常使用原因');
        }else if (this.shenpiForm.video && !this.shenpiForm.layout && !this.shenpiForm.layoutCause) {
          this.$message.error('请填写视频监控未按照方案要求的点位、数量布设的原因');
        }else if (this.shenpiForm.video && !this.shenpiForm.technology && !this.shenpiForm.technologyCause) {
              this.$message.error('请填写视频监控参数和存储未满足技术要求的原因');
        }else if (!this.shenpiForm.spray && !this.shenpiForm.sprayCause) {
          this.$message.error('请填写喷淋设备未正常使用原因');
        } else if (!this.shenpiForm.billboard && !this.shenpiForm.billboardCause) {
          this.$message.error('请填写公示牌未正常使用原因');
        } else if (!this.shenpiForm.supervisor && !this.shenpiForm.supervisorCause) {
          this.$message.error('请填写监理未到位原因');
        } else if (!this.shenpiForm.other && !this.shenpiForm.otherCause) {
          this.$message.error('请填写计重设施和相关配套未到位原因');
        } else if (!this.shenpiForm.reviewer) {
          this.$message.error('请填写审核人');
        } else {
          this.$confirm('是否确认审核通过当前数据?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let data = {}
            let newshen = {
              billboard: this.shenpiForm.billboard ? this.shenpiForm.billboard : false,
              billboardCause: this.shenpiForm.billboardCause,
              other: this.shenpiForm.other ? this.shenpiForm.other : false,
              otherCause: this.shenpiForm.otherCause,
              reviewer: this.shenpiForm.reviewer,
              spray: this.shenpiForm.spray ? this.shenpiForm.spray : false,
              sprayCause: this.shenpiForm.sprayCause,
              supervisor: this.shenpiForm.supervisor ? this.shenpiForm.supervisor : false,
              supervisorCause: this.shenpiForm.supervisorCause,
              video: this.shenpiForm.video ? this.shenpiForm.video : false,
              videoCause: this.shenpiForm.videoCause,
              technology: this.shenpiForm.technology ? this.shenpiForm.technology : false,
              technologyCause: this.shenpiForm.technologyCause,
              layout: this.shenpiForm.layout ? this.shenpiForm.layout : false,
              layoutCause: this.shenpiForm.layoutCause,
            }
            if (this.$store.state.user.deptcode.length == 9) {
              data = {
                deptId: this.row.deptId,
                status: 'pass',
                opinion: '',
                version: this.row.version,
                ...newshen
              }
            } else {
              data = {
                deptId: this.row.deptId,
                status: 'pass',
                opinion: '',
                version: this.row.version
              }
            }
            console.log(data, this.shenpiForm)
            checkProjectDeclare(data).then(r => {
              this.$message({
                type: 'success',
                message: '审核通过成功!'
              });
              this.open = false;
              this.activeName = 'first'
              this.reload()
            })
          })
        }
      },
      reject() {
        this.$prompt('请填写不通过原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'textarea',
          inputValidator: (value) => {
            if (value) {
              return true;
            } else {
              return '请输入不通过原因';
            }
          },
        }).then(({
          value
        }) => {
          let data = {}
          if (this.$store.state.user.deptcode.length == 9) {
            data = {
              deptId: this.row.deptId,
              status: 'reject',
              opinion: value,
              version: this.row.version,
              ...this.shenpiForm
            }
          } else {
            data = {
              deptId: this.row.deptId,
              status: 'reject',
              opinion: value,
              version: this.row.version
            }
          }
          checkProjectDeclare(data).then(r => {
            this.$message({
              type: 'success',
              message: '审核不通过成功!'
            });
            this.open = false;
            this.activeName = 'first'
            this.reload()
          })
        })
      },
      // 表单重置
      reset() {
        this.projectDeclare = {
          deptId: '',
          createUserId: '',
          updateUserId: '',
          createTime: '',
          updateTime: '',
          deptCode: '',
          name: '',
          sectionName: '',
          areaName: '',
          areaCode: '',
          type: '',
          parentId: '',
          totalYield: null,
          licenseNo: '',
          leaderName: '',
          contact: '',
          deleted: null,
          investment: null,
          reverLength: null,
          complaintMobile: '',
          queryMobile: '',
          auditStatus: '',
          version: null,
          createUserMobile: '',
          disposalMethodJson: [{
              method: 'paiMai',
              amount: '',
              selected: false
            },
            {
              method: 'shiZheng',
              amount: '',
              selected: false
            },
            {
              method: 'qiTa',
              amount: '',
              selected: false,
              remark: ''
            }
          ],
          nationalDebt: '',
          nationalDebtType: '',
          historyYield: ''
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.reload(true);
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = this.$refs.projectDeclareTable.getSelectRowKeys()
        this.single = this.ids.length != 1
        this.multiple = !this.ids.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加项目信息申报表";
      },
      /** 预览附件操作 */
      handleFill(row, type) {
        this.row = row;
        this.deptId = row.deptId;
        const deptId = row.deptId || this.deptIds[0];
        if (type == 'sh') {
          this.title = "项目审核";
          this.stick = 'sh';
        } else if (type == 'ck') {
          this.title = "项目查看";
          this.stick = 'ck';
        }
        getProjectDeclare(deptId).then(r => {
          this.projectDeclare = r.projectDeclare;
          if (this.$store.state.user.deptcode.length < 9) {
            this.shenpiForm = r.projectDeclare;
          }else{
            this.shenpiForm = {}
          }
          if (!this.projectDeclare.disposalMethodJson) {
            this.projectDeclare.disposalMethodJson = [{
                method: 'paiMai',
                amount: '',
                selected: false
              },
              {
                method: 'shiZheng',
                amount: '',
                selected: false
              },
              {
                method: 'qiTa',
                amount: '',
                selected: false,
                remark: ''
              }
            ]
          }
          this.open = true;
          this.mode = 'view';
          this.handelFileList(r)
          this.handleClick()
        });
      },
      handelFileList(r) {
        if (r.projectDeclare.fileList && r.projectDeclare.fileList.length > 0) {
          this.imgList = r.projectDeclare.fileList.map(item => {
            return {
              name: item.uploadFileName,
              url: item.uploadFilePath,
              uploadFile: item,
            }
          });
        } else {
          this.imgList = [];
        }
        if (r.projectDeclare.parentImgList && r.projectDeclare.parentImgList.length > 0) {
          this.parentImgList = r.projectDeclare.parentImgList.map(item => {
            return {
              name: item.uploadFileName,
              url: item.uploadFilePath,
              uploadFile: item,
            }
          });
        } else {
          this.parentImgList = [];
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate((valid, errorObj) => {
          if (valid) {
            if (this.projectDeclare.deptId) {
              updateProjectDeclare(this.projectDeclare).then(r => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.reload();
              });
            } else {
              addProjectDeclare(this.projectDeclare).then(r => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.reload();
              });
            }
          } else {
            this.$scrollView(errorObj);
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this;
        this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
          if (row.deptId) {
            return delProjectDeclare(row.deptId);
          } else {
            return delProjectDeclareBatch(that.ids);
          }
        }).then(() => {
          this.reload();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },
      /** 项目位置change事件 获取areaName */
      getAreaName(areaName) {
        this.projectDeclare.areaName = areaName;
        if (this.mode === 'add') {
          if (this.projectDeclare.areaCode.length >= 15) {
            const deptObj = this.$refs.deptCascader.deptList.find(item => {
              return item.areaCode === this.projectDeclare.areaCode;
            });
            this.projectDeclare.parentId = deptObj ? deptObj.deptId : '';
          } else {
            this.projectDeclare.parentId = '';
          }
        }
      },
      /** 获取fileList */
      getFileList(fileList) {
        this.projectDeclare.uploadFileList = fileList;
        this.$nextTick(() => {
          this.$refs['form'].validateField("uploadFileList")
        })
      },
      getFileList1(fileList) {
        this.projectDeclare.fileList = fileList
        this.$nextTick(() => {
          this.$refs['form'].validateField("imgList")
        })
      },
      getFileList2(fileList) {
        this.projectDeclare.parentImgList = fileList
        this.$nextTick(() => {
          this.$refs['form'].validateField("imgList2")
        })
      },
      getFileList8(fileList) {
        this.projectDeclare.paiMaiFileList = fileList
        this.$refs['form'].validateField("paiMaiFileList")
      },

      /** 查看附件列表 */
      handleFileList(row) {
        this.deptId = row.deptId;
        this.openFileList = true;
      },

      /** 下载附件操作 */
      handleDownloadFile(row) {
        this.download("/com/uploadfile/download/" + row.id, {}, row.uploadFileName);
      },

      /** 删除附件操作 */
      handleDeleteFile(row) {
        this.$modal.confirm('是否确认删除此附件？').then(() => {
          return delProjectFile(row.id);
        }).then(() => {
          this.$refs.fileTable.search();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
      },


    },
    activated() {
      //组件被激活时重绘表格
      this.$refs.projectDeclareTable.changeTableHeight();
    },
  };
</script>

<style scoped>
  ::v-deep .disabled-form {
    pointer-events: none !important;
    cursor: not-allowed !important;

  }

  ::v-deep .el-switch__label {
    color: inherit !important;
  }

  ::v-deep .el-switch__label.is-active {
    color: #F56C6C !important;
  }

  ::v-deep .el-switch__label--right.is-active {
    color: #1890ff !important;
  }

  table {
    border-spacing: 0;
    border-collapse: collapse;
  }

  table th,
  td {
    border: 1px solid rgb(238, 231, 237);
    padding: 5px;
  }

  .myvideo {
    background-color: #fff;
    border-radius: 6px;
  }

  .disbadeyu {
    width: 300px;
    /* margin-left: 40px; */
    margin-top: 10px;
  }

  ::v-deep .asterisk .el-form-item__label:before {
    content: "*" !important;
    color: #ff4949 !important;
    margin-right: 4px !important;
  }

  .allban {
    width: 400px !important;
  }

  .allTitle {
    display: flex;
    justify-content: space-between;
    /* margin: 20px; */
    /* padding: 15px; */
  }

  .allText {
    /* flex: 0 0 48%; */
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 300px !important;
  }

  .allText:first-child {
    background: rgba(64, 158, 255, 0.1);
    /* border-left: 4px solid #409EFF; */
  }

  .allText:last-child {
    background: rgba(230, 162, 60, 0.1);
    /* border-left: 4px solid #E6A23C; */
  }

  .allText i {
    margin-right: 10px;
    font-size: 18px;
  }

  .allText:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .allTitleCount {
    display: flex;
    gap: 20px;
    margin: 10px 0;
    border-radius: 12px;
  }

  .allTextCount {
    flex: 1;
    background: white;
    padding: 10px;
    border-radius: 6px;
  }


  .allTitleCount .el-form-item {
    margin-bottom: 0;
  }

  .allTitleCount .el-form-item__label {
    font-weight: 500;
    color: #606266;
    font-size: 15px;
  }

  .allTitleCount .el-checkbox {
    transform: scale(1.2);
  }

  .allTitleCount .el-textarea__inner {
    min-height: 100px;
    border-radius: 8px;
    transition: all 0.3s;
  }


  /* 边框文字标题样式 */
  .title-container {
    position: relative;
    /* margin: 40px 0; */
    text-align: center;
  }

  .bordered-box {
/*    border: 3px solid #3498db;
    border-radius: 8px; */
    padding: 20px 30px 30px;
    position: relative;
    background: #f8f9fa;
    /* box-shadow: 0 10px 30px rgba(52, 152, 219, 0.2); */
  }

  .bordered-title {
    position: absolute;
    top: -26px;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 0 20px;
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 1px;
    white-space: nowrap;
    z-index: 100;
  }

  /* 可选装饰元素 */
  .bordered-box::before {
    content: "";
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 45%;
    height: 3px;
    background: #fff;
  }

.content-area {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-top: 20px;
    position: relative;
}

/* 监控区域样式 */
.monitor-area {
    padding: 20px;
    border: 1px dashed #3498db;
    background-color: #fff;
    position: relative;
    border-radius: 5px;
}

/* 其他区域样式 */
.other-area {
    margin-top: 30px;
    padding: 20px;
    border: 1px dashed #3498db;
     background-color: #fff;
     position: relative;
     border-radius: 5px;
}
/* 区域标题样式 */
.area-title {
    color: #3498db;
    font-size: 18px;
    margin: 5px 0px;
    text-align: left;
    position: absolute;
        top: -4px;
        left: 140px;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        background: #fff;
        padding: 0 20px;
        font-weight: 600;
        letter-spacing: 1px;
        white-space: nowrap;
        z-index: 100;
}

  /* 响应式调整 */
  @media (max-width: 768px) {
    .bordered-title {
      font-size: 22px;
      white-space: normal;
      width: 80%;
    }
  }
</style>
