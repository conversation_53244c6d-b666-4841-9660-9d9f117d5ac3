
<template>
  <div>
    <!-- 添加或修改南水北调交叉河道监管责任人表对话框 -->
    <my-dialog title="河道监管责任人详情" height="70vh" v-bind="$attrs" :visible.sync="detailsOpen" width="1000px" append-to-body @close="cancel">
    <el-form ref="form" :model="diversionResponsiblePerson" disabled label-width="150px">
        <el-divider content-position="center">
          <i class="el-icon-info"></i>基本信息
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item ref="cityName" :rules="[{notNull:true,message:'请输入所在市'}]" label="所在市" prop="cityName">
              <my-input readonly v-model.trim="diversionResponsiblePerson.cityName" :maxlength="128" placeholder="请输入所在市"/>
            </my-form-item>
          </el-col>
          <el-col :span="12" v-if="diversionResponsiblePerson.districtName">
            <my-form-item ref="districtName"  label="所在县/区" prop="districtName">
              <my-input readonly v-model.trim="diversionResponsiblePerson.districtName" :maxlength="128" placeholder="请输入所在县/区"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="河道名称" ref="reverName" prop="reverName" :rules="[{notNull:true,message:'请输入河道名称'}]">
              <my-input v-model.trim="diversionResponsiblePerson.reverName" placeholder="请输入河道名称" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="起始位置" ref="startAddr" prop="startAddr" :rules="[{notNull:true,message:'请输入起始位置'}]">
              <my-input v-model.trim="diversionResponsiblePerson.startAddr" placeholder="请输入起始位置" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="终止位置" ref="endAddr" prop="endAddr" :rules="[{notNull:true,message:'请输入终止位置'}]">
              <my-input v-model.trim="diversionResponsiblePerson.endAddr" placeholder="请输入终止位置" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>各级河长责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="县级责任人" ref="responsibleXian" prop="responsibleXian" :rules="[{notNull:true,message:'请输入县级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleXian" placeholder="请输入县级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="县名称及职务" ref="positionXian" prop="positionXian" :rules="[{notNull:true,message:'请输入县名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionXian" placeholder="请输入县名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="乡级责任人" ref="responsibleXiang" prop="responsibleXiang" :rules="[{notNull:true,message:'请输入乡级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleXiang" placeholder="请输入乡级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="乡镇名称及职务" ref="positionXiang" prop="positionXiang" :rules="[{notNull:true,message:'请输入乡镇名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionXiang" placeholder="请输入乡镇名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="村级责任人" ref="responsibleCun" prop="responsibleCun" :rules="[{notNull:true,message:'请输入村级责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleCun" placeholder="请输入村级责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="村名称及职务" ref="positionCun" prop="positionCun" :rules="[{notNull:true,message:'请输入村名称及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionCun" placeholder="请输入村名称及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>水行政主管部门责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleCompetentDept" prop="responsibleCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleCompetentDept" placeholder="请输入县级水行政主管部门责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionCompetentDept" prop="positionCompetentDept" :rules="[{notNull:true,message:'请输入县级水行政主管部门单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionCompetentDept" placeholder="请输入县级水行政主管部门单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>现场监管责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleSupervise" prop="responsibleSupervise" :rules="[{notNull:true,message:'请输入现场监管责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleSupervise" placeholder="请输入现场监管责任人" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionSupervise" prop="positionSupervise" :rules="[{notNull:true,message:'请输入现场监管单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionSupervise" placeholder="请输入现场监管单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>行政执法责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleEnforce" prop="responsibleEnforce" :rules="[{notNull:true,message:'请输入行政执法责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleEnforce" placeholder="请输入行政执法责任人" :maxlength="128"/>
            </my-form-item>

          </el-col>
          <el-col :span="12">
            <my-form-item label="单位及职务" ref="positionEnforce" prop="positionEnforce" :rules="[{notNull:true,message:'请输入行政执法单位及职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionEnforce" placeholder="请输入行政执法单位及职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>南水北调中线一期工程管理单位责任人
        </el-divider>
        <el-row>
          <el-col :span="12">
            <my-form-item label="责任人" ref="responsibleDiversion" prop="responsibleDiversion" :rules="[{notNull:true,message:'请输入南水北调中线一期工程管理单位责任人'}]">
              <my-input v-model.trim="diversionResponsiblePerson.responsibleDiversion" placeholder="请输入南水北调中线一期工程管理单位责任人" :maxlength="128"/>
            </my-form-item>

          </el-col>
          <el-col :span="12">
            <my-form-item label="职务" ref="positionDiversion" prop="positionDiversion" :rules="[{notNull:true,message:'请输入南水北调中线一期工程管理单位责任人职务'}]">
              <my-input v-model.trim="diversionResponsiblePerson.positionDiversion" placeholder="请输入南水北调中线一期工程管理单位责任人职务" :maxlength="128"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center" v-if="diversionResponsiblePerson.remark">
          <i class="el-icon-info"></i>备注
        </el-divider>
        <el-row>
          <el-col :span="24" v-if="diversionResponsiblePerson.remark">
            <my-form-item ref="remark" :rules="[{notNull:true,message:'请输入备注'}]" label="备注" prop="remark">
              <my-input type="textarea" v-model.trim="diversionResponsiblePerson.remark" placeholder="请输入备注" :maxlength="255"/>
            </my-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">
          <i class="el-icon-info"></i>审核流程
        </el-divider>
        <el-row>
          <table style="width: 100%;margin-bottom: 18px" cellspacing="0" cellpadding="15" align="center">
            <th style="width: 30%">操作时间</th>
            <th>操作人</th>
            <th>操作状态</th>
            <th>操作备注</th>
            <tr align="center" v-for="(item,index) in processList" :key="item.id">
              <td style="width: 30%">{{item.createTime}}</td>
              <td >{{item.createUser}}</td>
              <td >
                <my-view pvalue="auditPassed" :value="item.passed+''"></my-view>
              </td>
              <td >{{item.opinion}}</td>
            </tr>
          </table>
        </el-row>

      </el-form>
    </my-dialog>
  </div>
</template>


<script>
import { getUser } from '@/api/statistics/projectStatistics'
import { getDiversionResponsiblePerson } from '@/api/reports/diversionResponsiblePerson'
import { getProcessList } from '@/api/reports/responsiblePersion'

export default {
  name: 'MyDiversionReportDetails',
  emits: ["close"],
  props:{
  detailsOpen: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  },
  cityName: {
    type: String,
    default: ''
  },
  districtName: {
    type: String,
    default: ''
  }
},
data() {
  return {
    diversionResponsiblePerson:{},
    processList:''
  }
},
watch:{
  detailsOpen(nvl){
    if(nvl){
      this.getDetails()
    }
  }
},
methods:{
  async getDetails(){
    const res = await getDiversionResponsiblePerson(this.id)
    this.diversionResponsiblePerson = res.diversionResponsiblePerson
    this.diversionResponsiblePerson.cityName = this.cityName
    this.diversionResponsiblePerson.districtName = this.districtName
    const response = await getProcessList(this.id)
    this.processList = response.page.list
  },
  cancel(){
    this.$emit('close')
  }
}
}
</script>


<style lang="scss"  scoped>
table {
  border-spacing: 0;
  border-collapse: collapse;
}

table th,td {
  border: 1px solid rgb(238,231,237);
  padding: 5px;
}
</style>
