<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" @submit.native.prevent>
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sys:role:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sys:role:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="batchDelete"
          v-hasPermi="['sys:role:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :search.sync="search" :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- table表格 -->
    <my-table url="/sys/role/list" ref="roleTable" row-key="roleId" @my-selection-change="handleSelectionChange">
      <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="250" />
      <el-table-column label="备注" prop="remark" align="center" width="300"></el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            class="btn-table-operate"
            icon="el-icon-user"
            title="选择用户"
            @click="handleSelectUser(scope.row.roleId)"
            v-hasPermi="['sys:role:saveUserIds']"
          ></el-button>
          <el-button
            size="mini"
            title="修改"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sys:role:update']"
          ></el-button>
          <el-button
            size="mini"
            title="删除"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sys:role:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改角色配置对话框 -->
    <my-dialog v-el-drag-dialog  :title="title" :visible.sync="open" width="500px" height="70vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="数据权限" prop="dataGroupId">
          <el-select v-model="form.dataGroupId">
            <el-option
              v-for="item in dataScopeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-tree
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="menuId"
            empty-text="加载中，请稍候"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="部门权限" prop="deptIdList">
          <div style="width: 100%">请选择可创建此角色的部门</div>
          <el-checkbox v-model="form.subShared">子部门共享</el-checkbox>
          <el-tree
            class="tree-border"
            :data="deptOptions1"
            show-checkbox
            ref="deptRef1"
            node-key="deptId"
            default-expand-all
            :check-strictly="true"
            empty-text="加载中，请稍候"
            :props="{ label: 'name', children: 'children' }"
            @check-change="deptCheckChange"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
    <!-- 选择用户弹窗 -->
    <my-dialog title="分配用户" :visible.sync="open2" width="1000px" append-to-body @close="userCancel">
      <el-form
        :model="selectParams"
        ref="selectForm"
        size="small"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="用户名" prop="username">
          <my-input
            v-model="selectParams.username"
            placeholder="用户名或姓名"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleSelect"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSelect">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetSelect">重置</el-button>
        </el-form-item>
      </el-form>
      <my-table
        :url="userUrl"
        ref="usersTable"
        row-key="userId"
        :offset="320"
        :select-keys="currentRoleUserIds"
        @my-selection-change="handleUserSelectionChange">
        <el-table-column label="用户名" min-width="80" align="center" prop="username"/>
        <el-table-column label="姓名" min-width="80" align="center" prop="showName"/>
        <el-table-column label="所属部门" min-width="80" align="center" prop="deptName"/>
        <el-table-column label="当前角色" min-width="150" align="center" prop="roleNames"/>
        <el-table-column label="状态" min-width="80" align="center">
          <template slot-scope="scope">
            <span class="table-label label-danger" v-if="scope.status===0">禁用</span>
            <span class="table-label label-success">正常</span>
          </template>
        </el-table-column>
      </my-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUser">确 定</el-button>
        <el-button @click="userCancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import {
  getRole,
  delRole,
  batchDelRole,
  addRole,
  updateRole,
  userIdsByRole, saveUserIds, listDept
} from '@/api/system/role'
import { getMenuTreeData } from "@/api/system/menu";
import { handleTree } from '@/utils/ruoyi';
import elDragDialog from '@/directive/dialog/drag'
import { getDataGroupData } from '@/api/system/user'

export default {
  name: "Role",
  directives: {
    elDragDialog  //拖拽弹窗
  },
  data() {
      const validateDeptIdList = (rule, value, callback) => {
      this.form.deptIdList = this.getDeptAllCheckedKeys();
      if (this.form.deptIdList && this.form.deptIdList.length > 0) {
        callback();
      } else {
        callback(new Error('请选择可创建此角色的部门'));
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      search:true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      menuExpand: false,
      menuNodeAll: false,
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "name"
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" }
        ],
        dataGroupId: [
          { required: true, message: "数据权限不能为空", trigger: ['blur', 'change'] }
        ],
        deptIdList: [{ required: true, validator:validateDeptIdList, trigger: ['blur', 'change'] }],

      },
      // 角色数组
      roleSelectList: [],
      // 是否显示选择用户弹窗
      open2: false,
      // 非单个禁用
      userSingle: true,
      // 非多个禁用
      userMultiple: true,
      // 选择用户弹窗查询参数
      selectParams: {},
      // 缓存dataGroupId
      currentRoleId: undefined,
      // 选择用户弹窗 选中数组
      currentRoleUserIds: [],
      //数据权限列表
      dataScopeOptions:[],
      //部门权限列表
      deptOptions1:[],
      userUrl: '',
    };
  },
  mounted() {
    this.getMenuTreeselect();
    this.getDataGroupList();
    this.getDeptTreeselect();
  },
  methods: {
    /** 添加修改角色弹窗 当部门树复选框被点击的时候触发 */
    deptCheckChange(val) {
      this.$refs.form.validate(['deptIdList'])
    },
    /** 查询部门树结构 */
    getDeptTreeselect() {
      listDept().then(response => {
        this.deptOptions1= handleTree(response, 'deptId')
      })
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      return this.$refs.deptRef1.getCheckedKeys();
    },
    /** 查询数据权限 */
    getDataGroupList() {
      getDataGroupData().then(response => {
        this.dataScopeOptions = response.list;
      });
    },
    /** 查询角色列表 */
    getList(restart) {
      this.$refs.roleTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple=true;
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      getMenuTreeData().then(response => {
        console.log(response)
        this.menuOptions = handleTree(response.menuList, 'menuId', 'parentId');
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.menu.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].menuId].expanded = false;
        }
      }
      this.menuExpand = false,
      this.menuNodeAll = false,
      this.form = {
        roleId: undefined,
        roleName: undefined,
        menuIdList: [],
        deptIdList: [],
        remark: undefined,
        subShared:false,
        dataGroupId:undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.roleTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == 'menu') {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].menuId].expanded = value;
        }
      } else if (type == 'dept') {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].menuId].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);
      } else if (type == 'dept') {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      getRole(roleId).then(response => {
        console.log(response)
        const roleMenu = response.role.menuIdList
        const roleDept= response.role.deptIdList
        this.form = response.role;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.forEach((v) => {
              this.$refs.menu.setChecked(v, true ,false);
          })
          roleDept.forEach((d) => {
            this.$refs.deptRef1.setChecked(d, true ,false);
          })
        });
        this.title = "修改角色";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$modal.loading('加载中')
          if (this.form.roleId != undefined) {
            this.form.menuIdList = this.getMenuAllCheckedKeys();
            this.form.deptIdList = this.getDeptAllCheckedKeys();
            this.form.deptId = -1;
            updateRole(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          } else {
            this.form.menuIdList = this.getMenuAllCheckedKeys();
            this.form.deptIdList = this.getDeptAllCheckedKeys();
            this.form.deptId = -1;
            addRole(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.loading=true;
              this.getList();
              this.$modal.closeLoading();
            }).catch(this.$modal.closeLoading);
          }
        }
      });
    },
    /** 查询用户角色 列表 */
    userReload(restart) {
      this.selectParams.roleId = this.currentRoleId;
      this.userUrl = "/sys/user/pageByRoleAuth";
      this.$refs.usersTable.search(this.selectParams, restart);
    },
    /** 选择用户按钮操作 */
    handleSelectUser(roleId) {
      this.open2 = true;
      this.currentRoleId = roleId;
      this.currentRoleUserIds = [];
      if (roleId) {
        userIdsByRole(roleId).then(r => {
          this.currentRoleUserIds = r.userIds;
          this.userReload(true);
        });
      }
    },
    /** 选择用户弹窗-搜索操作 */
    handleSelect() {
      this.userReload(true);
    },
    /** 选择用户弹窗-重置操作 */
    resetSelect() {
      this.resetForm('selectForm');
      this.handleSelect();
    },
    /** 选择用户弹窗-多选框选中数据 */
    handleUserSelectionChange() {
      this.currentRoleUserIds = this.$refs.usersTable.getSelectRowKeys();
      this.userSingle = this.currentRoleUserIds.length !== 1;
      this.userMultiple = !this.currentRoleUserIds.length;

    },
    /** 选择用户弹窗-确定操作 */
    submitUser() {
      var userIdsParams = this.$refs.usersTable.getSelectRowKeys();
      var that = this;
      this.$modal.loading('加载中')
      console.log(userIdsParams)
      saveUserIds(that.currentRoleId,userIdsParams).then(() => {
        that.$modal.msgSuccess('操作成功');
        that.open2 = false;
        this.userUrl = "";
        this.$modal.closeLoading()
      }).catch(this.$modal.closeLoading)
        .catch(() => {})  //捕获异常错误
    },
    /** 选择用户弹窗-取消按钮 */
    userCancel() {
      this.open2 = false;
      this.userUrl = "";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal.confirm('是否确认删除该角色？').then(() => {
        this.$modal.loading('加载中')
        return delRole(roleIds);
      }).then(() => {
        this.$modal.closeLoading()
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 批量删除按钮操作 */
    batchDelete() {
      const roleIds = this.$refs.roleTable.getSelectRowKeys();
      this.$modal.confirm('是否确认删除该角色？').then(()=>{
        this.$modal.loading('加载中')
        return batchDelRole(roleIds);
      }).then(() => {
        this.$modal.closeLoading()
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch((error) => {
        console.log(error);
        this.$modal.closeLoading();
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/role/export', {
        ...this.queryParams
      }, `role_${new Date().getTime()}.xlsx`)
    },
  }
};
</script>
<style scoped>
/deep/ .my-dialog__body{
  height: 60vh;
  overflow: auto;
}
</style>
