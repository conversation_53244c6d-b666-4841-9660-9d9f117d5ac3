<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          clearable
          v-model.trim="queryParams.projectName"
          placeholder="请输入项目名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标段名称" prop="sectionName">
        <my-input
          style="width: 205px"
          v-model.trim="queryParams.sectionName"
          placeholder="请输入标段名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="磅站名称" prop="stationName">
        <el-input
          clearable
          v-model.trim="queryParams.stationName"
          placeholder="请输入磅站名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['client:camera:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['client:camera:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['client:camera:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/client/camera/page" ref="cameraTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column header-align="center" align="left" label="项目名称" min-width="200" prop="projectName" sortable="custom" column-key="client.PROJECT_NAME"
      ></el-table-column>
      <el-table-column label="标段名称" align="left" header-align="center" min-width="120" prop="sectionName" sortable="custom" column-key="client.SECTION_NAME">
      </el-table-column>
      <el-table-column header-align="center" align="left" label="磅站名称" min-width="110" prop="stationName" sortable="custom" column-key="client.STATION_NAME"></el-table-column>
      <el-table-column header-align="center" align="center" label="序列号" min-width="100" prop="sn" sortable="custom" column-key="camera.SN"></el-table-column>
      <el-table-column header-align="center" align="center" label="IP" min-width="80" prop="ip" sortable="custom" column-key="camera.IP"></el-table-column>
      <el-table-column header-align="center" align="center" label="端口号" min-width="100" prop="port" sortable="custom" column-key="camera.PORT"></el-table-column>
      <el-table-column header-align="center" align="center" label="状态" min-width="80" prop="status" sortable="custom" column-key="camera.STATUS">
    <template #default="scope">
      <my-view pvalue="terminalStatus" :value="scope.row.status"></my-view>
    </template>
  </el-table-column>
      <el-table-column header-align="center" align="center" label="创建时间" min-width="100" prop="createTime" sortable="custom" column-key="camera.CREATE_TIME"
      ></el-table-column>
      <el-table-column header-align="center" align="center" label="修改时间" min-width="100" prop="updateTime" sortable="custom" column-key="camera.UPDATE_TIME"
      ></el-table-column>
      <el-table-column label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['client:camera:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['client:camera:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改摄像头信息表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="camera" label-width="80px">
        <el-row>
          <el-col :span="12">
            <my-form-item label="客户端" ref="clientId" prop="clientId" :rules="[{notNull:true,message:'请选择客户端',trigger:['blur','change']}]">
              <my-select
                v-model="camera.clientId"
                :options="clientArrayList"
                placeholder="请选择客户端">
              </my-select>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="序列号" ref="sn" prop="sn" :rules="[{notNull:true,message:'请输入序列号',trigger:['blur','change']}]">
              <my-input v-model="camera.sn" placeholder="请输入序列号"/>
            </my-form-item>
          </el-col>

          <el-col :span="12">
            <my-form-item label="IP" ref="ip" prop="ip" :rules="[{notNull:true,message:'请输入IP',trigger:['blur','change']}]">
              <my-input v-model="camera.ip" placeholder="请输入IP"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="端口号" ref="port" prop="port" :rules="[{notNull:true,message:'请输入端口号',trigger:['blur','change']}]">
              <my-input :maxlength="10" v-model="camera.port" placeholder="请输入端口号"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="状态" ref="status" prop="status"
                          :rules="[{notNull:true,message:'请选择状态',trigger:['blur','change']}]"
            >
              <my-select
                v-model="camera.status"
                pvalue="terminalStatus"
                placeholder="请选择状态">
              </my-select>
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getCamera, delCamera, delCameraBatch,addCamera, updateCamera } from "@/api/client/camera";
import MyAreaSelect from '@/components/YB/MyAreaSelect.vue'
import { listClient } from '@/api/client/client'

export default {
  name: "Camera",
  components: { MyAreaSelect },
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        projectName:'',
        stationName:'',
        sectionName:''
      },
      // 表单参数
      camera: {},
      // 客户端列表
      clientList: [],
    };
  },
  mounted() {
    listClient().then(res=>{
      this.clientList = res.list
    });
  },
  computed: {
    clientArrayList: function() {
      if (this.clientList && this.clientList.length > 0) {
        return this.clientList.map(item => {
          if(item.sectionName){
            return { 'name': item.projectName+"("+item.sectionName+")" + '_' + item.stationName, 'value': item.id }
          }else {
            return { 'name': item.projectName + '_' + item.stationName, 'value': item.id }
          }
        });
      }
    }
  },
  methods: {
    /** 查询摄像头信息表列表 */
    reload(restart) {
      this.$refs.cameraTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.camera = {
        clientId: '',
        sn: '',
        ip: '',
        port: null ,
        status: 'use'
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.cameraTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加摄像头信息表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getCamera(id).then(r => {
        this.camera = r.camera;
        this.open = true;
        this.title = "修改摄像头信息表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.camera.id) {
            updateCamera(this.camera).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addCamera(this.camera).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(()=>{
         if(row.id) {
          return delCamera(row.id);
        }else{
          return delCameraBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },

  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.cameraTable.changeTableHeight();
  },
};
</script>
