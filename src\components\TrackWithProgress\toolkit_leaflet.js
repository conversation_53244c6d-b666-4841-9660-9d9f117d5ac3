/**
 * 20221027 - 来自wum
 */

import axios from 'axios';

const IS_4326 = false;

const TOKEN_TDT = "9801a6af2d3ca13c21b3a8cf18cdfe3a";
const MAX_ZOOM = 22;
const DEFAULT_DURATION = 1.5;
const VIEW_HEBEI = { center: [39.39697265625,116.08732090239953], zoom: 6.6, };
const VIEW_CHINA = { center: [36.03496872129995, 103.11767578125], zoom: 4, };
const SERVER_SM = "http://219.148.61.135:8090/iserver/services/";
const SERVER_SM_BASE = "http://219.148.61.135:8090/iserver/services/";
// const SERVER_SM_CHARACTER = "http://192.168.10.103:8090/iserver/services/";
const SERVER_SM_CHARACTER = "http://219.148.61.135:8090/iserver/services/";

let K = {
  viewer: null,
  wmts: L.TileLayer.extend({
    /** Default params for WMTS */
    defaultWmtsParams: {
      service: 'WMTS',
      request: 'GetTile',
      version: '1.0.0',
      layer: '',
      style: '',
      tilematrixset: '',
      format: 'image/png',
    },


    /**
     * Initialize plugin
     * @param {String} url Url to WMTS server
     * @param {Object} options List options
     */
    initialize(url, options) {
      this._url = url;

      const lOptions = {};
      const cOptions = Object.keys(options);
      cOptions.forEach(element => (
        lOptions[element.toLowerCase()] = options[element]
      ));

      const wmtsParams = L.extend({}, this.defaultWmtsParams);
      const tileSize = lOptions.tileSize || this.options.tileSize;

      if (lOptions.detectRetina && L.Browser.retina) {
        wmtsParams.width = tileSize * 2;
        wmtsParams.height = tileSize * 2;
      } else {
        wmtsParams.width = tileSize;
        wmtsParams.height = tileSize;
      }

      for (const i in lOptions) {
        // all keys that are in defaultWmtsParams options go to WMTS params
        // eslint-disable-next-line no-prototype-builtins
        if (wmtsParams.hasOwnProperty(i) && i !== 'matrixIds') {
          wmtsParams[i] = lOptions[i];
        }
      }

      this.wmtsParams = wmtsParams;
      this.matrixIds = options.matrixIds || this.getDefaultMatrix();

      L.setOptions(this, options);
    },


    /**
     * Set tile to map
     * @param {Leaflet.Map} map Leaflet map
     */
    onAdd(map) {
      this._crs = this.options.crs || map.options.crs;
      L.TileLayer.prototype.onAdd.call(this, map);
    },

    /*8888 20220906 试验超图瓦片的封装*/
    // createTile(coords, done) {
    //   const url = this.getTileUrl(coords);
    //   const img = document.createElement("img");
    //   img.setAttribute("role", "presentation");
    //   img.setAttribute("data-url", url);

    //   fetch(url, {
    //       method: "GET",
    //       // headers: {"whoareyou": "ourix"},
    //       mode: "cors",
    //       signal: new AbortController().signal
    //     }).then(async f => {
    //       const blob = await f.blob();
    //       const reader = new FileReader();
    //       reader.readAsDataURL(blob);
    //       reader.onload = () => {
    //         img.src = reader.result;
    //         img.className = "leaflet-tile leaflet-tile-loaded";
    //       };
    //     });
    //   return img;
    // },

    /**
     * Generate URL for tile pieces
     * @param {Leaflet.Point} coords Position tile
     * @param {Number} coords.x Position X
     * @param {Number} coords.y Position Y
     * @param {Number} coords.z Position Z
     * @return {String} URL
     */
    getTileUrl(coords) {
      const tileSize = this.options.tileSize;

      const nwPoint = coords.multiplyBy(tileSize);
      nwPoint.x += 1;
      nwPoint.y -= 1;

      const sePoint = nwPoint.add(new L.Point(tileSize, tileSize));
      const zoom = this._tileZoom;
      const se = this._crs.project(this._map.unproject(sePoint, zoom));
      const nw = this._crs.project(this._map.unproject(nwPoint, zoom));
      const tilewidth = se.x - nw.x;

      const ident = this.matrixIds[zoom].identifier;
      // const tilematrix = `${this.wmtsParams.tilematrixset}:${ident}`;
      const tilematrix = `${ident}`;
      const X0 = this.matrixIds[zoom].topLeftCorner.lng;
      const Y0 = this.matrixIds[zoom].topLeftCorner.lat;
      const tilecol = Math.floor((nw.x - X0) / tilewidth);
      const tilerow = -Math.floor((nw.y - Y0) / tilewidth);

      const url = L.Util.template(this._url, {
        s: this._getSubdomain(coords),
      });

      return `${url}${L.Util.getParamString(this.wmtsParams, url)}` +
        `&tilematrix=${tilematrix}&tilerow=${tilerow}&tilecol=${tilecol}`;
    },


    /**
     * Set params
     * @param {Object} params Params
     * @param {Boolean} noRedraw needed redraw map
     */
    setParams(params, noRedraw) {
      L.extend(this.wmtsParams, params);

      if (!noRedraw) {
        this.redraw();
      }

      return this;
    },


    /**
     * Generate default matrix
     * @description The matrix3857 represents the projection
     * for in the IGN WMTS for the google coordinates.
     */
    getDefaultMatrix() {
      const matrixIds3857 = new Array(22);

      for (let i = 0; i < 22; i++) {
        matrixIds3857[i] = {
          identifier: String(i),
          topLeftCorner: new L.LatLng(20037508.3428, -20037508.3428),
        };
      }

      return matrixIds3857;
    },
  }),
  rest: L.TileLayer.extend({
    /** Default params for REST */
    defaultWmtsParams: {
      service: 'REST',
      request: 'GetTile',
      version: '1.0.0',
      layer: '',
      style: '',
      tilematrixset: '',
      format: 'image/png',
    },
    initialize(url, options) {
      this._url = url;

      const lOptions = {};
      const cOptions = Object.keys(options);
      cOptions.forEach(element => (
        lOptions[element.toLowerCase()] = options[element]
      ));

      const wmtsParams = L.extend({}, this.defaultWmtsParams);
      const tileSize = lOptions.tileSize || this.options.tileSize;

      if (lOptions.detectRetina && L.Browser.retina) {
        wmtsParams.width = tileSize * 2;
        wmtsParams.height = tileSize * 2;
      } else {
        wmtsParams.width = tileSize;
        wmtsParams.height = tileSize;
      }

      for (const i in lOptions) {
        // all keys that are in defaultWmtsParams options go to WMTS params
        // eslint-disable-next-line no-prototype-builtins
        if (wmtsParams.hasOwnProperty(i) && i !== 'matrixIds') {
          wmtsParams[i] = lOptions[i];
        }
      }

      this.wmtsParams = wmtsParams;
      this.matrixIds = options.matrixIds || this.getDefaultMatrix();

      L.setOptions(this, options);
    },
    onAdd(map) {
      this._crs = this.options.crs || map.options.crs;
      L.TileLayer.prototype.onAdd.call(this, map);
    },
    getTileUrl(coords) {
      // 计算origin
      let origin = this.getOrigin();
      const url = L.Util.template(this._url, {
        s: this._getSubdomain(coords),
      });
      return `${url}`
        + `&origin=` + this.getOrigin()
        + `&scale=` + this.getScale(coords)
        + `&x=` + coords.x + `&y=` + coords.y;
    },
    /**
     * Set params
     * @param {Object} params Params
     * @param {Boolean} noRedraw needed redraw map
     */
    setParams(params, noRedraw) {
      L.extend(this.wmtsParams, params);

      if (!noRedraw) {
        this.redraw();
      }

      return this;
    },
    /**
     * Generate default matrix
     * @description The matrix3857 represents the projection
     * for in the IGN WMTS for the google coordinates.
     */
    getDefaultMatrix() {
      const matrixIds3857 = new Array(22);
      for (let i = 0; i < 22; i++) {
        matrixIds3857[i] = {
          identifier: String(i),
          topLeftCorner: new L.LatLng(20037508.3428, -20037508.3428),
        };
      }
      return matrixIds3857;
    },
    getOrigin(coords) {
      var crs = this._crs;
      if (crs.options && crs.options.origin) {
          return JSON.stringify({
              x: crs.options.origin[0],
              y: crs.options.origin[1]
          });
      } else if (crs.projection && crs.projection.bounds) {
          var bounds = crs.projection.bounds;
          var tileOrigin = L.point(bounds.min.x, bounds.max.y);
          return JSON.stringify({
              x: tileOrigin.x,
              y: tileOrigin.y
          });
      }
    },
    getScale(coords) {
      var scale;
      if (this.scales && this.scales[coords.z]) {
          return this.scales[coords.z];
      }
      this.scales = this.scales || {};

      var crs = this._crs;
      var tileBounds = this._tileCoordsToBounds(coords);
      var ne = crs.project(tileBounds.getNorthEast());
      var sw = crs.project(tileBounds.getSouthWest());
      var tileSize = this.options.tileSize;
      var resolution = Math.max(
          Math.abs(ne.x - sw.x) / tileSize,
          Math.abs(ne.y - sw.y) / tileSize
      );
      var inchPerMeter = 1 / 0.0254;
      scale = resolution * 96 * inchPerMeter;
      scale = 1 / scale;

      this.scales[coords.z] = scale;
      return scale;
    }
  }),
};
K.initMap = function (idDiv) {
	K.viewer = L.map(idDiv, {

    center: VIEW_HEBEI.center,
    zoom: VIEW_HEBEI.zoom,

		maxZoom: MAX_ZOOM,
    minZoom: 4,
		zoomSnap: 0.1,
		zoomDelta: 0.1,
		trackResize: true,
    maxBounds: L.latLngBounds(L.latLng(6.5417496775025175, 69.58659667193923), L.latLng(55.95172994170497, 130.49169247335865)),
		boxZoom: true,
		crs: IS_4326 ? L.CRS.EPSG4326 : L.CRS.EPSG3857,
		zoomControl: false, // 按钮地图缩放
		logoControl: false, // logo
		attributionControl: false // 版权
	});
  K.initWatermark();
	// L.control.scale({maxWidth:200, metric:true, imperial:false}).addTo(K.viewer);
}
K.initWatermark = function () {
	// 创建网格
	var layer = L.gridLayer({
		zIndex: 9999,
		maxZoom: MAX_ZOOM,
		opacity: 0.33,
	});
	layer.createTile = function(coords) {
		var tile = L.DomUtil.create("canvas", "leaflet-tile");
		var context = tile.getContext("2d");
		var size = this.getTileSize();
		tile.width = size.x;
		tile.height = size.y;


		context.fillStyle = "#ffffff";
		context.font = "12px 微软雅黑";
		// context.fillText(process.env.VUE_APP_TITLE, 106, 220);
		// context.fillText("@刘庭集团", 180, 250);
		context.strokeStyle = "#ffffff";
		context.beginPath();
		context.moveTo(0, 0);
    let stroke = 0.1;
		context.lineTo(size.x - stroke, 0);
		context.lineTo(size.x - stroke, size.y - stroke);
		// context.lineTo(0, size.y - stroke);
		// context.closePath();
		context.stroke();
		return tile;
	}
	layer.addTo(K.viewer);
}
K.createLayerGroup = function () {
  return new L.LayerGroup();
}
K.createLayerRestSm = function(nameService, nameMap, zIndex, token) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same CRS. ")
  }
  let url = SERVER_SM_CHARACTER + nameService + "/rest/maps/" + nameMap + "/tileImage.png?";
  url += "width=256&height=256&redirect=false";
  url += "&token=" + token;
  url += "&transparent=true&cacheEnabled=true&overlapDisplayed=false";
  let layer = new K.rest(url, {
    maxZoom: MAX_ZOOM,
    maxNativeZoom: 18,
    tileSize: 256,
    zoomOffset: 1,
    zIndex: zIndex,
    minZoom: 1,
  });
  return layer;
}
/**
 * 以单幅图像的方式调用超图rest瓦片服务，须在map.on("move")里进行重复调用
 */
K.createLayerRestSm_singleImaged = function(nameService, nameMap, zIndex) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same lane. ")
  }
  let size = K.viewer.getSize();
  let pixelBounds = K.viewer.getPixelBounds();
  let sw = K.viewer.unproject(pixelBounds.getBottomLeft());
  let ne = K.viewer.unproject(pixelBounds.getTopRight());
  let top = K.viewer.latLngToLayerPoint(ne).y;
  let bottom = K.viewer.latLngToLayerPoint(sw).y;
  if (top > 0 || bottom < size.y) {
      size.y = bottom - top;
  }

  var neProjected = K.viewer.options.crs.project(ne);
  var swProjected = K.viewer.options.crs.project(sw);
  let boundsProjected = L.bounds(neProjected, swProjected);

  var projBounds = {
      leftBottom: {
          x: boundsProjected.getBottomLeft().x,
          y: boundsProjected.getTopRight().y
      },
      rightTop: {
          x: boundsProjected.getTopRight().x,
          y: boundsProjected.getBottomLeft().y
      }
  };
  let viewBounds = JSON.stringify(projBounds);
  let url = SERVER_SM + nameService + "/rest/maps/" + nameMap + "/image.png?transparent=true";
  url += "&width=" + size.x;
  url += "&height=" + size.y;
  url += "&viewBounds=" + viewBounds;

  let layer = new L.ImageOverlay(url, K.viewer.getBounds(), {
    // opacity: 0,
    zIndex: zIndex,
    interactive: false,
  });
  return layer;
}
K.createLayerWmtsSm = function(nameService, nameMap, zIndex) {
  if (IS_4326) {
    console.log("Warning: We are using EPSG4326 now, make sure iServer is working in the same CRS. ")
  }
  let layer = new K.wmts(SERVER_SM + nameService + "/wmts100", {
    layer: nameMap,
    style: "default",
    // tilematrixSet: "Custom_MAP-HL",
    tilematrixSet: IS_4326 ? ("" + nameMap) : ("GoogleMapsCompatible_" + nameMap),
    format: "image/png",

    maxZoom: MAX_ZOOM,
    maxNativeZoom: 18,
    tileSize: 256,
    zoomOffset: 1,
    zIndex: zIndex,
    minZoom: 1,
  });
  return layer;
}
K.createLayerWmtsTdtByTunnel = function (type, zIndex) {
  let url = "http://120.46.147.156:4106/wmsTile/getTileFromTianditu?x={x}&y={y}&z={z}&type=" + type;
  const layer = L.tileLayer(url, {
  	maxZoom: MAX_ZOOM,
    maxNativeZoom: 18,
  	tileSize: 256,
  	zoomOffset: 1,
  	zIndex: zIndex,
  	minZoom: 1,
  	updateWhenIdle: false,
  	updateWhenZooming: false,
  });
  return layer;
}
K.createLayerWmtsTdt = function (type, zIndex) {
  let base = "http://t0.tianditu.gov.cn/" + type + "_c/wmts?tk=" + TOKEN_TDT;
  if (IS_4326) {
    let url = base + "&tilematrixset=c&Service=WMTS&Request=GetTile&Version=1.0.0&Style=default";
    url += "&Format=tiles&TileMatrix={z}&TileCol={x}&TileRow={y}&Layer=" + type;
    let layer = L.tileLayer(url, {
      maxZoom: MAX_ZOOM,
      maxNativeZoom: 18,
      tileSize: 256,
      zoomOffset: 1,
      zIndex: zIndex,
      minZoom: 1,
    });
    return layer;
  } else {
    let layer = new K.wmts("http://t0.tianditu.gov.cn/" + type + "_w/wmts?tk=" + TOKEN_TDT, {
      layer: type,
      style: "default",
      tilematrixset: "w",
      format: "tiles",

      maxZoom: MAX_ZOOM,
      maxNativeZoom: 18,
      tileSize: 256,
      zoomOffset: 1,
      zIndex: zIndex,
      minZoom: 1,
    });
    return layer;
  }
}

K.pickFeatureRestSm = function(nameService, listNameSourceSet, ll, cb) {
  let instance = axios.create({
    baseURL: SERVER_SM_CHARACTER + nameService + '/rest/data/',
    timeout: 1000 * 10,
  });
  let url = "featureResults.json?returnContent=true&fromIndex=0&toIndex=19";
  instance.post(url, {
    // datasetNames: ["HHSP:SLG_RV"],
    datasetNames: ["PTGC:L_PIPE"],
    getFeatureMode: "BUFFER",
    bufferDistance: 14.0625 * Math.pow(0.5, this.viewer.getZoom()),
    geometry: {
      id: 0,
      style: null,
      parts: [1],
      type: "POINT",
      points: [{
        x: ll.lng,
        y: ll.lat,
      }],
    },
  }).then(res => {
    let data = res.data;
    if (cb) {
      cb(data);
    }
  }).catch(r => {
    console.error(r);
  });
}

K.switchLayer = function (layer, on) {
  if (on) {
    layer.addTo(K.viewer);
  } else {
    layer.removeFrom(K.viewer);
  }
}
K.setViewToBounds = function (bounds, duration) {
	if (bounds) {
		// 重新定位地图视角
		if (duration) {
      K.viewer.dragging.disable();
			K.viewer.flyToBounds(bounds, {
				animate: true,
				duration: duration,
			});
      setTimeout(function(){
        K.viewer.dragging.enable();
      }, duration * 1000);
		} else {
			K.viewer.flyToBounds(bounds, {
				animate: false,
			});
		}
	} else {
		K.setViewToCurrentLocation();
	}
}
K.setViewToCurrentLocation = function () {
	//
	// resetMapViewToLatLng([latInit, lngInit]);
}
K.setViewToLatLng = function (pos, duration) {
	K.viewer.panTo(pos, {
		animate: true,
		duration: duration ? duration : DEFAULT_DURATION,
	});
}
K.setViewToProject = function (pos) {
  K.viewer.setView(pos, 12.5, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.setViewToHebei = function() {
  K.viewer.setView(VIEW_HEBEI.center, VIEW_HEBEI.zoom, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.setViewToYDH = function() {
  K.viewer.setView(VIEW_YDH.center, VIEW_YDH.zoom, {
    animate: true,
    duration: DEFAULT_DURATION,
  });
}
K.addBoundaryHebei = function(url) {
  L.geoJSON(url, {
  	style: function(feature) {
  		return {
  			color: "#008686",
  			opacity: 1,
  			weight: 4,
  			fill: true,
  			fillColor: "#008686",
  			fillOpacity: 0.2,
  			interactive: false,
  		};
  	},
  }).addTo(K.viewer);
}


K.bd09ToWgs84 = function(lng, lat) {
  let lamda = Math.PI * 3000.0 / 180.0;
  let z = Math.sqrt(Math.pow(lng, 2) + Math.pow(lat, 2)) + 0.00002 * Math.sin(lat * lamda);
  let theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * lamda);
  let lngNew = z * Math.cos(theta) + 0.0065;
  let latNew = z * Math.sin(theta) + 0.006;

  lngNew = 2 * lng - lngNew;
  latNew = 2 * lat - latNew;

  return K.gcj02ToWgs84(lngNew, latNew);
}

K.gcj02ToWgs84 = function(lng, lat) {
  let epsilon = 0.006693421622965823;
  let radLat = lat / 180 * Math.PI;
  let radius = 6378245;
  let magic = Math.sin(radLat);
  magic = 1 - epsilon * magic * magic;
  magic = Math.sqrt(magic);

  let x = lng - 105;
  let y = lat - 35;
  let a = (20 * Math.sin(6 * x * Math.PI) + 20 * Math.sin(2 * x * Math.PI)) * 2 / 3;
  let dLng = 300 + x + 2 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
  dLng += a;
  dLng += (20 * Math.sin(x * Math.PI) + 40 * Math.sin(x / 3 * Math.PI)) * 2 / 3;
  dLng += (150 * Math.sin(x / 12 * Math.PI) + 300 * Math.sin(x / 30 * Math.PI)) * 2 / 3;
  let dLat = -100 + 2 * x + 3 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
  dLat += a;
  dLat += (20 * Math.sin(y * Math.PI) + 40 * Math.sin(y / 3 * Math.PI)) * 2 / 3;
  dLat += (160 * Math.sin(y / 12 * Math.PI) + 320 * Math.sin(y / 30 * Math.PI)) * 2 / 3;

  dLng = (dLng * 180) / (radius / magic * Math.cos(radLat) * Math.PI);
  dLat = (dLat * 180) / ((radius * (1 - epsilon)) / (magic * magic) * Math.PI);
  return [lng - dLng, lat - dLat];
}

export default K;
