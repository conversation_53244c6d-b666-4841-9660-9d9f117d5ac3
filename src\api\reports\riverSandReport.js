import request from '@/utils/request'

// 查询砂石量统计-采砂报表详细
export function getRiverSandReport(id) {
  return request({
    url: '/reports/riverSandReport/info/' + id,
    method: 'post'
  })
}

// 新增砂石量统计-采砂报表
export function addRiverSandReport(data) {
  return request({
    url: '/reports/riverSandReport/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改砂石量统计-采砂报表
export function updateRiverSandReport(data) {
  return request({
    url: '/reports/riverSandReport/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除砂石量统计-采砂报表
export function delRiverSandReport(id) {
  return request({
    url: '/reports/riverSandReport/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除砂石量统计-采砂报表
export function delRiverSandReportBatch(ids) {
  return request({
    url: '/reports/riverSandReport/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


