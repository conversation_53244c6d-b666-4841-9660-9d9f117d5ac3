import request from '@/utils/request'

// 查询项目/沙场信息表详细
export function getProject(deptId) {
  return request({
    url: '/project/project/info/' + deptId,
    method: 'post'
  })
}

// 新增项目/沙场信息表
export function addProject(data) {
  return request({
    url: '/project/project/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改项目/沙场信息表
export function updateProject(data) {
  return request({
    url: '/project/project/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除项目/沙场信息表
export function delProject(deptId) {
  return request({
    url: '/project/project/delete/' + deptId,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除项目/沙场信息表
export function delProjectBatch(deptIds) {
  return request({
    url: '/project/project/delete',
    data: deptIds,
    method: 'post',
    showLoading: true,
  });
}

// 单项删除项目附件
export function delProjectFile(id) {
  return request({
    url: '/project/project/delProjectFile/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 获取项目配置详情
export function getProjectConfig(deptId) {
  return request({
    url: '/project/projectConfig/info/' + deptId,
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

// 新增项目配置信息
export function addProjectConfig(data) {
  return request({
    url: '/project/projectConfig/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改项目配置信息
export function updateProjectConfig(data) {
  return request({
    url: '/project/projectConfig/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 获取项目信息加密串
export function getProjectEncryptedString(deptId) {
  return request({
    url: '/project/project/encryptedString/' + deptId,
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}
//追加申请
export function setAppeal(data) {
  return request({
    url: '/project/project/saveGrit',
    method: 'post',
    data: data
  })
}
export function upStatus(data) {
  return request({
    url: 'project/project/auditGrit',
    method: 'post',
    data: data
  })
}
//chaxun
export function getProjectList(query) {
  return request({
    url: "/project/project/getGritAudit",
    method: "get",
    params: query,
  });
}
// 获取小程序码
export function getProjectWxCode(deptId) {
  return request({
    url: '/project/project/getWxCode/'+deptId,
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

