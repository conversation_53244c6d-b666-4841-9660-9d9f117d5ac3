import request from '@/utils/request'

//获取字典树
export function getDictTreeData(type) {
  return request({
    url: "/sys/dict/select?type=" + type,
    method: 'post',
  })
}

//删除所选字典
export function deleteDictData(dictId,parentValue) {
  return request({
    url: "/sys/dict/delete/" + dictId,
    method: 'post',
    params:{parentValue}
  })
}

//提交字典信息
export function submitDictData(dict) {
  var url = !dict.id ? '/sys/dict/save' : '/sys/dict/update'
  return request({
    url: url,
    method: 'post',
    data: dict
  })
}

//获取子项字典列表
export function subList(pValues) {
  return request({
    url: "/sys/dict/cList",
    method: 'post',
    headers: {
      allowRepeatSubmit: true,
    },
    params: {pValues: pValues, t: new Date().getTime()}

  });
}
