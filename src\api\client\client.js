import request from '@/utils/request'

// 获取所有项目列表
export function listProjectNames() {
  return request({
    url: '/project/project/listProjectNames',
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

// 获取某个项目下的磅站列表
export function listStationByProjectId(projectId) {
  return request({
    url: '/project/weighingStation/list/' + projectId,
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}

// 获取客户端信息列表 不分页
export function listClient() {
  return request({
    url: '/client/client/listClient',
    method: 'post',
    headers: {allowRepeatSubmit: true}
  })
}
export function goStart(query) {
  return request({
    url: "/client/tempDataPool/remotePoleLift",
    method: "get",
    params: query,
  });
}
// 查询客户端信息表详细
export function getClient(id) {
  return request({
    url: '/client/client/info/' + id,
    method: 'post'
  })
}

// 新增客户端信息表
export function addClient(data) {
  return request({
    url: '/client/client/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}
export function clientAppeal(data) {
  return request({
    url: '/client/tempDataPool/documentAppeal',
    method: 'post',
    data: data,
    showLoading: true,
  })
}
//chaxun
export function getCar(query) {
  return request({
    url: "/client/tempDataPool/appealPoleLiftPage",
    method: "get",
    params: query,
  });
}
// 修改客户端信息表
export function updateClient(data) {
  return request({
    url: '/client/client/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除客户端信息表
export function delClient(id) {
  return request({
    url: '/client/client/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除客户端信息表
export function delClientBatch(ids) {
  return request({
    url: '/client/client/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}


