<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="姓名" prop="name">
        <el-input
          clearable
          v-model.trim="queryParams.name"
          placeholder="请输入姓名"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          clearable
          v-model.trim="queryParams.mobile"
          placeholder="请输入手机号"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!projectDisable" label="项目名称" prop="projectId">
        <my-select
          v-model.trim="queryParams.projectId"
          :options="projectList"
          placeholder="请选择项目"
        >
          <el-option v-for="item in projectList"
                     :key="item.value"
                     :value="item.value"
                     :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
          ></el-option>
        </my-select>
      </el-form-item>
      <el-form-item label="是否已注册" prop="registered">
        <my-select
          v-model="queryParams.registered"
          pvalue="registered"
          placeholder="请选择是否已注册">
        </my-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:driver:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:driver:delete']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['project:driver:import']"
        >导入</el-button>
      </el-col>
<!--      <el-link type="primary" @click="handleDownload">
        <i aria-hidden="true" class="fa fa-file-code-o"></i> 下载模板
      </el-link>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/project/driver/page" ref="driverTable" row-key="id" @my-selection-change="handleSelectionChange">
      <el-table-column header-align="center"  align="left"  label="所属项目" min-width="150" prop="projectName" sortable="custom" column-key="project.NAME">
        <template #default="scope">
          <el-tooltip :content="scope.row.projectName" placement="top" effect="light">
            <div v-if="scope.row.sectionName !=='' && scope.row.sectionName !== null">{{ scope.row.projectName+'('+scope.row.sectionName+')' }}</div>
            <div v-else>{{ scope.row.projectName }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column header-align="center"  align="center"  label="姓名" min-width="80" prop="name" sortable="custom" column-key="NAME"></el-table-column>
      <el-table-column header-align="center" align="center"    label="手机号" min-width="80" prop="mobile" sortable="custom" column-key="MOBILE"
      ></el-table-column>
      <el-table-column header-align="center" align="center"  label="是否已注册" min-width="80" prop="registered" sortable="custom" column-key="REGISTERED" >
        <template #default="scope">
          {{ scope.row.registered == true ? '是':'否' }}
        </template>
      </el-table-column>
      <el-table-column header-align="center"  align="center"  label="创建人" min-width="80" prop="createUserName" sortable="custom" column-key="user.SHOW_NAME"></el-table-column>
      <el-table-column header-align="center"  align="center"  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="driver.CREATE_TIME"></el-table-column>

      <el-table-column header-align="center"  label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            class="btn-table-operate"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:driver:delete']"
          ></el-button>
        </template>
      </el-table-column>
    </my-table>

    <!-- 添加或修改运砂人白名单表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="driver"  label-width="120px">
        <el-row>
          <el-col :span="12">
            <my-form-item
              label="项目名称"
              ref="projectIds"
              prop="projectIds"
              :rules="[{notNull:true,message:'请选择项目'}]"
            >
              <my-select
                multiple
                :disabled = "projectDisable"
                v-model="driver.projectIds"
                :options="projectList"
                placeholder="请选择项目"
              >
                <el-option v-for="item in projectList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <my-form-item label="姓名" ref="name" prop="name" :rules="[{notNull:true,message:'请输入姓名'}]">
              <my-input v-model.trim="driver.name" maxlength="128" placeholder="请输入姓名" :maxlength="128"/>
            </my-form-item>
          </el-col>
          <el-col :span="12">
            <my-form-item label="手机号" ref="mobile" prop="mobile" :rules="[{isMobile:true,message:'手机号格式不正确'}]">
              <my-input v-model.trim="driver.mobile" placeholder="请输入手机号" :maxlength="20"/>
            </my-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
    <!--导入-->
    <my-dialog :title="title" :visible.sync="openImport" width="500px" append-to-body>
      <el-form ref="form2" :model="importData"  label-width="100px">
        <el-row>
            <my-form-item
              label="项目名称"
              ref="projectIds"
              prop="projectIds"
              :rules="[{notNull:true,message:'请选择项目'}]"
            >
              <my-select
                multiple
                :disabled = "projectDisable"
                v-model="importData.projectIds"
                :options="projectList"
                placeholder="请选择项目"
              >
                <el-option v-for="item in projectList"
                           :key="item.value"
                           :value="item.value"
                           :label="item.sectionName?item.name+'('+item.sectionName+')':item.name"
                ></el-option>
              </my-select>
            </my-form-item>
        </el-row>
        <el-row>
            <my-form-item
              label="导入方式"
              ref="isAdd"
              prop="isAdd"
              :rules="[{notNull:true,message:'导入方式不能为空'}]"
            >
              <template #label>
                <el-tooltip placement="right" effect="light">
                  <template #content> 删除旧数据是指：删除原有的运砂人，重新添加新的运砂人<br />
                    追加导入是指：在原有的运砂人基础上新增运砂人</template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
                导入方式
              </template>
              <el-switch
                v-model="importData.isAdd"
                size="large"
                active-text="追加导入"
                inactive-text="删除旧数据"
              />
            </my-form-item>
        </el-row>
        <el-row >
            <my-form-item
              label="上传附件"
              prop="fileName"
              :rules="[{notNull:true,message:'上传附件不能为空'}]"
            >
              <el-button v-if="importData.fileName === ''" @click="clickUpload" type="primary">上传</el-button>
              <span v-else>{{importData.fileName}}<el-button type="text" @click="clickClose"><i class="el-icon-close"></i></el-button></span>

              <input
                ref="excel"
                type="file"
                @change="changeUpload"
                style="display: none"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
              >
            </my-form-item>
        </el-row>
        <el-row >
          <my-form-item>
            <el-link type="primary" @click="handleDownload">
              <i aria-hidden="true" class="fa fa-file-code-o"></i> 下载模板
            </el-link>
          </my-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormImport">确 定</el-button>
        <el-button @click="cancelImport">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import {
  getDriver,
  delDriver,
  delDriverBatch,
  addDriver,
  updateDriver,
  downloadTemplate,
  driverImport,
  listProjectByDeptId
} from '@/api/project/driver'
import request from '@/utils/request'
import Template from '@/views/sms/template/index.vue'

export default {
  name: "Driver",
  components: { Template },
  data() {
    return {
      openImport:false,
      //获取项目数组
      projectList:[],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name:'',
        mobile: '',
        registered: '',
        projectId:'',
      },
      // 表单参数
      driver: {
        projectIds: []
      },
      projectDisable:false,
      importData:{
        isAdd:true,
        projectIds: [],
        fileName:'',
        uploadFile:[],
      },
    };
  },
  mounted() {
    this.getProjectName();
  },
  methods: {
    //获取项目列表
    getProjectName(){
      listProjectByDeptId().then(res => {
        if (res.list && res.list.length > 0) {
          this.projectList = res.list.map(item => {
            if(item.sectionName){
              return { 'name': item.name, 'value': item.deptId, 'sectionName':item.sectionName}
            }else {
              return { 'name': item.name, 'value': item.deptId }
            }
          });
          if (this.projectList.length === 1){
            this.driver.projectIds = this.projectList.map(e=>e.value)
            this.importData.projectIds = this.projectList.map(e=>e.value)
            /*this.queryParams.projectId = this.projectList[0].value*/
            this.projectDisable = true;
          }else {
            this.projectDisable = false;
          }
        }
      })
    },
    // selectable(row){
    //   return !row.registered
    // },
    handleDownload(){
      this.download("/project/driver/downloadTemplate", {},"运砂人信息导入模板.xlsx")
    },
    request,
    /** 查询运砂人白名单表列表 */
    reload(restart) {
      this.$refs.driverTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    //导入取消
    cancelImport(){
      this.openImport = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.driver = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        projectId: '',
        projectIds: [],
        name: '',
        mobile: '',
        registered: null
      };
      this.importData = {
        isAdd:true,
        projectIds : [],
        fileName:'',
        uploadFile:[],
      }
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.driverTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getProjectName();
      this.open = true;
      this.title = "添加运砂人";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getProjectName();
      const id = row.id || this.ids[0];
      getDriver(id).then(r => {
        this.driver = r.driver;
        this.open = true;
        this.title = "修改运砂人白名单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.driver.id) {
            updateDriver(this.driver).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addDriver(this.driver).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
        if(row.id) {
          return delDriver(row.id);
        }else{
          return delDriverBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch((err) => {
        console.log('err', err)
      });
    },
    //点击上传按钮
    clickUpload(){
      this.$refs.excel.click();
    },
    //删除已上传的附件
    clickClose(){
      this.importData.uploadFile = [];
      this.importData.fileName = '';
      this.$refs.excel.value = '';
    },
    //已上传附件发生变化时 赋值
    changeUpload(){
      this.importData.uploadFile = this.$refs.excel.files
      this.importData.fileName = this.importData.uploadFile[0].name
    },
    handleImport(){
      this.reset();
      this.getProjectName();
      this.title = "导入"
      this.openImport = true;
    },
    submitFormImport(){
      this.$refs["form2"].validate((valid, errorObj) => {
        if (valid){
          if (!this.importData.uploadFile.length) {
            this.$modal.msgError("请上传填写完成的导入模板");
            return;
          }
          var formData = new FormData();
          formData.append("file", this.importData.uploadFile[0]);
          formData.append("projectIds",this.importData.projectIds)
          formData.append("isAdd",this.importData.isAdd)
          driverImport(formData).then(res=>{
            if (res.code === 0){
              this.$modal.msgSuccess("导入成功");
              this.openImport =false;
              this.reload();
            }
          }).catch((err) => {
            console.log('err', err)
          });
        }else {
          this.$scrollView(errorObj);
        }
      });
    }
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.driverTable.changeTableHeight();
  },
};
</script>
