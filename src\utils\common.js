const common = {}

common.loadJsResource = (arr=[], baseUrl='')=>{
  return new Promise((resolve) => {
    let index = 0;
    arr.map(path=>{
      let $script = document.createElement('script');
      $script.src = baseUrl + path;
      document.body.appendChild($script);
      $script.onload = ()=>{
        if(index == (arr.length-1)) {
          resolve();
        }
        index++;
      }
    })
  })
}
/**
 * 格式化日期
 * @param date
 * @param format
 * @returns {string|*|string}
 */
common.formatDate = function (date, format) {
  var time = {
    "M+": date.getMonth() + 1
    , "d+": date.getDate()
    , "h+": date.getHours()
    , "m+": date.getMinutes()
    , "s+": date.getSeconds()
    , "q+": Math.floor((date.getMonth() + 3) / 3)
    , "S+": date.getMilliseconds()
  };
  if (/(y+)/i.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '')).substr(4 - RegExp.$1.length);
  }
  for (var k in time) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? time[k] : ('00' + time[k]).substr(('' + time[k]).length));
    }
  }
  return format;

}

/**
 * 金额千分符格式
 * @param num 金额 string或者number
 * @param decimals 保留几位小数
 * @param thousandsSep 千分位符号
 * @returns {string} 金额格式的字符串
 */
common.toThousands = (num, decimals, thousandsSep) => {
  // 处理null、undefined或空值
  if (num === null || num === undefined || num === '') {
    return '0.00'
  }
  
  if (isNaN(num)) {
    return '0.00' // 直接返回，避免后续处理null值 - 作者：仕伟
  }
  let prec = !isFinite(+decimals) ? 0 : Math.abs(decimals) // 保留的位数一定是有限位数的正整数
  let sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep
  let s = String(num).replace(/,/g, '') // 安全转换为字符串，避免null错误 - 作者：仕伟
  let p = parseFloat(s) // 解析一个字符串，并返回一个浮点数
  let n = isNaN(p) ? 0 : p // 将默认值改为0而不是1
  let formatNum = n.toFixed(prec).toString().replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
    return $1 + sep
  })
  return formatNum
}
export default common
