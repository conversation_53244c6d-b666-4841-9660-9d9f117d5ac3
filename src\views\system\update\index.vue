<template>
  <div class="app-container">
    <el-form :model="formData" ref="queryForm" size="small" :inline="true" v-show="showUpdate" label-width="110px">
      <el-form-item>
        <el-upload
          class="upload-demo"
          action="#"
          :limit="1"
          :http-request="UploadAction"
          :on-success="updateSuccess"
          :on-error="updateError"
          :file-list="fileList">
          <el-button class="btn btn-primary" type="primary" icon="el-icon-upload"><i class="fa fa-upload"></i>&nbsp;更新</el-button>
        </el-upload>
      </el-form-item>
    </el-form>


    <h5>历史备份文件列表</h5>
    <my-table url="/sys/update/list" ref="updateTable" row-key="id" :show-pager="false" >
      <el-table-column label="文件名" min-width="80" prop="name" sortable="custom"></el-table-column>
      <el-table-column label="创建日期" min-width="80" prop="date" sortable="custom"></el-table-column>
      <el-table-column label="操作" column-key="caozuo" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="success"
            class="btn-table-operate"
            icon="el-icon-edit"
            @click="restore(scope.row)"
          >还原</el-button>
        </template>
      </el-table-column>
    </my-table>


  </div>
</template>
<script>
import {reduction, uploadFile} from "@/api/system/update";
export default {
  name: "Update",
  data() {
    return{
      // 显示更新
      showUpdate: true,
      formData: {},
      fileList: [],
    }


  },
  methods:{
    UploadAction(params) {
      var formData = new FormData();
      formData.append("file", params.file);
      uploadFile(formData).then(r=>{
        console.log(r)
      })
    },
    updateSuccess:function (r,file,fileList) {
      console.log(r)
      if (r.code != 0) {
        alert(r.msg);
      }
    },
    updateError: function (err, file, fileList) {
      console.log(err);
      console.log(file);
      console.log(fileList);
    },
    restore:function (row,r){
      let fileName= row.fileName
      if (r.code != 0) {
        alert(r.msg)
      }else {
        reduction(fileName)
      }
    },
  }
}
</script>
