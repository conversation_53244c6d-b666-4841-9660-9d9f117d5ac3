import request from '@/utils/request'
import { download } from '@/utils/request'

//查询系统日志列表
export function getListData(queryParams) {
  return request({
    url: '/sys/log/sysLog',
    method: 'post',
    params: queryParams
  })
}

//通过文件名删除日志文件
export function viewSysLog(fileName) {
  return request({
    url: '/sys/log/detail',
    method: 'post',
    params: {
      fileName: fileName
    }
  })
}

//批量删除系统日志
export function batchDelSysLog(fileNames) {
  return request({
    url: '/sys/log/delSysLogBatch',
    method: 'post',
    data: fileNames
  })
}

//通过文件名删除日志文件
export function delOneSysLog(fileName) {
  return request({
    url: '/sys/log/delSysLog',
    method: 'post',
    params: {
      fileName: fileName
    }
  })
}

//通过文件名下载日志文件
export function downLoadSysLog(fileName) {
  return download('/sys/log/download?fileName=' + fileName, {}, fileName)

}
