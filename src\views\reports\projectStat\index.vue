<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reports:projectStat:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reports:projectStat:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reports:projectStat:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/reports/projectStat/page" ref="projectStatTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
  <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
  <el-table-column  label="项目ID" min-width="80" prop="deptId" sortable="custom" column-key="DEPT_ID"></el-table-column>
  <el-table-column  label="项目编码" min-width="80" prop="deptCode" sortable="custom" column-key="DEPT_CODE"></el-table-column>
  <el-table-column  label="区域编码" min-width="80" prop="areaCode" sortable="custom" column-key="AREA_CODE"></el-table-column>
  <el-table-column  label="项目类型;同项目表字典" min-width="80" prop="projectType" sortable="custom" column-key="PROJECT_TYPE"></el-table-column>
  <el-table-column  label="年月;格式：yyyy-MM" min-width="80" prop="yearMonth" sortable="custom" column-key="YEAR_MONTH"></el-table-column>
  <el-table-column  label="年;格式：yyyy" min-width="80" prop="year" sortable="custom" column-key="YEAR"></el-table-column>
  <el-table-column  label="月;格式：MM" min-width="80" prop="month" sortable="custom" column-key="MONTH"></el-table-column>
  <el-table-column  label="单据数量" min-width="80" prop="billCount" sortable="custom" column-key="BILL_COUNT"></el-table-column>
  <el-table-column  label="累计载运量" min-width="80" prop="totalVehicleLoad" sortable="custom" column-key="TOTAL_VEHICLE_LOAD"></el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['reports:projectStat:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['reports:projectStat:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改项目开单统计表对话框 -->
    <my-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="projectStat"  label-width="80px">
                              <my-form-item label="创建时间" ref="createTime" prop="createTime" :rules="[{notNull:true,message:'请输入创建时间'}]">
        <my-input v-model.trim="projectStat.createTime" placeholder="请输入创建时间" :maxlength="20"/>
      </my-form-item>
                              <my-form-item label="项目ID" ref="deptId" prop="deptId" :rules="[{notNull:true,message:'请输入项目ID'}]">
        <my-input v-model.trim="projectStat.deptId" placeholder="请输入项目ID" :maxlength="32"/>
      </my-form-item>
                              <my-form-item label="项目编码" ref="deptCode" prop="deptCode" :rules="[{notNull:true,message:'请输入项目编码'}]">
        <my-input v-model.trim="projectStat.deptCode" placeholder="请输入项目编码" :maxlength="50"/>
      </my-form-item>
                              <my-form-item label="区域编码" ref="areaCode" prop="areaCode" :rules="[{notNull:true,message:'请输入区域编码'}]">
        <my-input v-model.trim="projectStat.areaCode" placeholder="请输入区域编码" :maxlength="50"/>
      </my-form-item>
                              <my-form-item label="项目类型;同项目表字典" ref="projectType" prop="projectType" :rules="[{notNull:true,message:'请输入项目类型;同项目表字典'}]">
        <my-input v-model.trim="projectStat.projectType" placeholder="请输入项目类型;同项目表字典" :maxlength="64"/>
      </my-form-item>
                              <my-form-item label="年月;格式：yyyy-MM" ref="yearMonth" prop="yearMonth" :rules="[{notNull:true,message:'请输入年月;格式：yyyy-MM'}]">
        <my-input v-model.trim="projectStat.yearMonth" placeholder="请输入年月;格式：yyyy-MM" :maxlength="7"/>
      </my-form-item>
                              <my-form-item label="年;格式：yyyy" ref="year" prop="year" :rules="[{notNull:true,message:'请输入年;格式：yyyy'}]">
        <my-input v-model.trim="projectStat.year" placeholder="请输入年;格式：yyyy" :maxlength="4"/>
      </my-form-item>
                              <my-form-item label="月;格式：MM" ref="month" prop="month" :rules="[{notNull:true,message:'请输入月;格式：MM'}]">
        <my-input v-model.trim="projectStat.month" placeholder="请输入月;格式：MM" :maxlength="2"/>
      </my-form-item>
                              <my-form-item label="单据数量" ref="billCount" prop="billCount" :rules="[{notNull:true,message:'请输入单据数量'}]">
        <my-input v-model.trim="projectStat.billCount" placeholder="请输入单据数量" :maxlength="11"/>
      </my-form-item>
                      <my-form-item label="累计载运量" ref="totalVehicleLoad" prop="totalVehicleLoad" :rules="[{notNull:true,message:'请输入累计载运量'}]">
        <my-input v-model.trim="projectStat.totalVehicleLoad" placeholder="请输入累计载运量" />
      </my-form-item>
          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>

<script>
import { getProjectStat, delProjectStat, delProjectStatBatch,addProjectStat, updateProjectStat } from "@/api/reports/projectStat";

export default {
  name: "ProjectStat",
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      projectStat: {},
    };
  },
  mounted() {
  },
  methods: {
    /** 查询项目开单统计表列表 */
    reload(restart) {
      this.$refs.projectStatTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.projectStat = {
        id: '',
        createTime: '',
        deptId: '',
        deptCode: '',
        areaCode: '',
        projectType: '',
        yearMonth: '',
        year: '',
        month: '',
        billCount: null ,
        totalVehicleLoad: null 
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.projectStatTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目开单统计表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getProjectStat(id).then(r => {
        this.projectStat = r.projectStat;
        this.open = true;
        this.title = "修改项目开单统计表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.projectStat.id) {
            updateProjectStat(this.projectStat).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addProjectStat(this.projectStat).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var that=this;
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delProjectStat(row.id);
        }else{
          return delProjectStatBatch(that.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.projectStatTable.changeTableHeight();
  },
};
</script>
