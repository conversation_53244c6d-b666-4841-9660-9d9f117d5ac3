<template>
  <div class="app-container">
    <el-row style="margin-top: 10px">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="项目位置" prop="areaCode" style="margin-bottom: 0px"  v-hasPermi="['mobile:puichasingOrderManage:areaSelecter']">
            <my-area-select
              v-model="queryParams.areaCode"
              @change="getAreaName"
              :external-parent-value="parentAreaCode"
              :external-children-value="childrenAreaCode"
              :read-only-parent="isReadOnlyParent"
              :read-only-children="isReadOnlyChildren"
              :is-clear="isClear"
              style="font-size: medium;"/>
          </el-form-item>
          <el-form-item label="项目创建时间" label-width="100px" prop="timeRange" style="margin-bottom: 0px;" v-hasPermi="['mobile:puichasingOrderManage:dateRange']">
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              @change="handleDateChange"
              value-format="yyyy-MM-dd"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              :clearable="false">
            </el-date-picker>
          </el-form-item>
          <el-form-item  prop="selectedTime" style="margin-bottom: 0px" v-hasPermi="['mobile:puichasingOrderManage:timeTags']">
            <el-radio-group v-model="selectedTime" @change="handleTimeChange">
              <el-radio-button  label="本年"></el-radio-button>
              <el-radio-button  label="本月"></el-radio-button>
              <el-radio-button  label="本日"></el-radio-button>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          </el-form-item> -->
      </el-form>
    </el-row>

    <el-row :gutter="10" style="margin-top: 15px"  class="row-container">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['statistics:monthlyReport:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5" style="margin-left: 20px">
        <el-radio v-model="exportChoice" label="1">导出全部记录</el-radio>
        <el-radio v-model="exportChoice" label="2">导出筛选记录</el-radio>
      </el-col>
      <el-col :span="2" style="margin-left: auto;">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="reload(null, 'project')"></right-toolbar>
      </el-col>
    </el-row>

    <el-row style="margin-top: 10px">
      <el-col :span="24" style="padding: 0 5px" v-hasPermi="['statistics:monthlyReport:table']">
          <my-table
            url="/project/statistics/list"
            ref="projectTable"
            row-key="deptId"
            :fixed="true"
            cell-class-name="el-table-max-cell2"
            @my-selection-change="handleSelectionChange"
            @my-sort-change ="handleSortChange"
            class="el-table-auto-height"
            :show-pager = "false"
          >
            <el-table-column
              label="河道名称"
              fixed="left"
              header-align="center"
              align="left"
              min-width="250"
              prop="projectName"
              column-key="project.PROJECT_NAME">
              <template #default="scope">
                <el-tooltip :content="scope.row.projectName" placement="top" effect="light">
                  <div>{{ scope.row.projectName }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              label="河道治理采砂单位"
              align="left"
              header-align="center"
              min-width="160"
              prop="leaderName"
              column-key="project.LEADER_NAME">
            </el-table-column>
            <el-table-column
              label="许可证编号"
              align="left"
              header-align="center"
              min-width="160"
              prop="licenseNo"
              column-key="project.LICENSE_NO">
            </el-table-column>
            <el-table-column
              align="center"
              header-align="center"
              min-width="120"
              prop="totalYield"
              column-key="project.TOTAL_YIELD">
              <template #header>
                <div>
                  年度许可采砂量<br>
                  （万m³）
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="right"
              header-align="center"
              min-width="150"
              prop="governCoords"
              column-key="project.GOVERN_COORDS">
              <template #header>
                <div>
                  治理长度<br>
                  (起止点及坐标)
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="right"
              header-align="center"
              min-width="150"
              prop="leftshoreCoords"
              column-key="project.LEFTSHORE_COORDS">
              <template #header>
                <div>
                  左岸长度<br>
                  (起止点及坐标)
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="right"
              header-align="center"
              min-width="150"
              prop="rightshoreCoords"
              column-key="project.RIGHTSHORE_COORDS">
              <template #header>
                <div>
                  右岸长度<br>
                  (起止点及坐标)
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="具体治理内容"
              align="right"
              header-align="center"
              min-width="150"
              prop="governanceContent"
              column-key="project.GOVERNANCE_CONTENT">
            </el-table-column>
            <el-table-column
              align="right"
              header-align="center"
              min-width="120"
              prop="monthSandMined"
              column-key="project.MONTH_SAND_MINED">
              <template #header>
                <div>
                  当月采砂量<br>
                  （万m³）
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="right"
              header-align="center"
              min-width="120"
              prop="totalSandMined"
              column-key="project.TOTAL_SAND_MINED">
              <template #header>
                <div>
                  累计采砂量<br>
                  （万m³）
                </div>
              </template>
            </el-table-column>
          </my-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import MyAreaSelect from "@/components/YB/MyAreaSelect_back.vue";
import {getUserInfo} from '@/api'
export default {
  name: 'ProjectStatistics',
  components: {MyAreaSelect},
  data(){
    return{
      // 显示搜索条件
      showSearch: true,
      //查询参数
      queryParams:{
        areaCode: '',
        startTime: '',
        endTime: '',
      },
      defaultQueryParams:{
        areaCode: '',
        startTime: '',
        endTime: '',
      },

      //时间选择
      selectedTime: '',//radio-button 时间选择
      timeRange: ['', ''], // 时间范围，默认选中最近一年,
      //时间选择选项
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }],
      },

      //my-area-select
      parentAreaCode: '', // 父级地区编码
      childrenAreaCode: '', //子级地区编码
      isReadOnlyParent: false, // 控制父下拉框是否可编辑
      isReadOnlyChildren: false, // 控制子下拉框是否可编辑
      isClear:true, // 父选择框是否可清空

      //导出内容选择
      exportChoice: '1',
    }
  },

  created() {
  },
  mounted() {
    this.getUserInfo();
  },

  methods: {
    //获取当前登录用户信息
    getUserInfo() {
      console.log("********getUserInfo:")
      getUserInfo().then(res => {
        const { deptCode, areaCode, areaName, parentCode, parentName } = res.userInfo;
        let deptCodeLength = deptCode.length;

        // 定义公共属性
        this.defaultCityCode = areaCode;
        this.citySandMapName = areaName;

        switch (deptCodeLength) {
            case 4: // 省级用户
                // this.parentAreaCode = ''; // 默认显示河北省
                break;
            case 9: // 市级用户
                this.parentAreaCode = areaCode;
                this.isReadOnlyParent = true;
                break;
            case 14: // 区县级用户
                this.parentAreaCode = parentCode;
                this.childrenAreaCode = areaCode;
                this.isReadOnlyParent = true;
                this.isReadOnlyChildren = true;
                break;
            case 19: // 项目级用户
                break;
            default:
                return;
        }
      });
    },
    /** 查询项目列表 */
    reload(restart) {
      this.$refs.projectTable.search(this.queryParams, restart);
      this.single = true;
      this.multiple = true;
    },
    /** 项目列表-多选框选中数据 */
    handleSelectionChange() {
      this.deptIds = this.$refs.projectTable.getSelectRowKeys();
      this.single = this.deptIds.length!==1;
      this.multiple = !this.deptIds.length;
    },
    handleSortChange(opt,obj){
      this.queryParams.sidx = obj.sidx
      this.queryParams.order = obj.order
    },
    //项目地址change后更新城市采砂信息图表
    getAreaName(areaName) {
      if(this.queryParams.areaCode == '' && this.queryParams.areaName != areaName){
        console.log("项目地址更新执行方法++++++++++++++:地址更新")
        this.queryParams.areaName = areaName
        return
      }
      this.reload(true)
    },
    //时间选择器change后更新信息
    handleDateChange(value){
      console.log("时间选择器执行方法:" + value)
      this.selectedTime ='';
      this.queryParams.startTime = value[0];
      this.queryParams.endTime = value[1];
      this.reload(true)
    },
    //时间快捷切换
    handleTimeChange(value) {
      this.timeRange = [this.getStartOfTime(value),this.getEndOfTime(value)]
      this.queryParams.startTime = this.getStartOfTime(value);
      this.queryParams.endTime = this.getEndOfTime(value);
      this.reload(true)
    },
    //时间格式化
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // 格式化为 yyyy-MM-dd
    },
    //时间选择器默认选择最近一个月
    getLastYearStart() {
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
      return start;
    },
    //获取检索开始日期
    getStartOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth()+1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if(value == '本年'){
        return `${currentYear}-01-01`; // 返回格式为 yyyy-MM-dd
      }else if(value == '本月'){
        return `${currentYear}-${currentMonth}-01`;
      }else{
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    //获取检索结束日期
    getEndOfTime(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = (new Date().getMonth()+1).toString().padStart(2, '0');
      const currentDay = new Date().getDate().toString().padStart(2, '0');
      if(value == '本年'){
        return `${currentYear}-12-31`; // 返回格式为 yyyy-MM-dd
      }else if(value == '本月'){
        return `${currentYear}-${currentMonth}-31`;
      }else{
        return `${currentYear}-${currentMonth}-${currentDay}`;
      }
    },
    /** 导出按钮操作 */
    handleExport() {

      if(this.exportChoice == '1'){
        console.log("导出按钮执行方法:" + this.exportChoice)
        this.download('/project/statistics/export',
        {...this.defaultQueryParams},
        `采砂月报表_${new Date().getTime()}.xlsx`, 'application/json');
      }else{
        console.log("导出查询参数:" + this.queryParams.areaCode +  " || "  + this.queryParams.startTime + " || " + this.queryParams.endTime)
        this.download('/project/statistics/export',
          {...this.queryParams},
          `采砂月报表_${new Date().getTime()}.xlsx`, 'application/json');
      }

    },
  },
}

</script>

<style scoped>
.custom-header {
  white-space: pre-wrap;
  text-align: center;
}
.row-container {
  display: flex;          /* 使用 flexbox 布局 */
  align-items: center;    /* 垂直居中对齐 */
}
</style>
