import request from '@/utils/request'

// 查询南水北调交叉河道监管责任人表详细
export function getDiversionResponsiblePerson(id) {
  return request({
    url: '/reports/diversionResponsiblePerson/info/' + id,
    method: 'post'
  })
}

// 新增南水北调交叉河道监管责任人表
export function addDiversionResponsiblePerson(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改南水北调交叉河道监管责任人表
export function updateDiversionResponsiblePerson(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除南水北调交叉河道监管责任人表
export function delDiversionResponsiblePerson(id) {
  return request({
    url: '/reports/diversionResponsiblePerson/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除南水北调交叉河道监管责任人表
export function delDiversionResponsiblePersonBatch(ids) {
  return request({
    url: '/reports/diversionResponsiblePerson/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//县审核
export function diversionCheckPass(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/xianExamine',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//县批量审核
export function diversionCheckPassBatch(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/xianExamineBatch',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//市审核
export function diversionCheckPassCity(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/shiExamine',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//市批量审核
export function diversionCheckPassBatchCity(data) {
  return request({
    url: '/reports/diversionResponsiblePerson/shiExamineBatch',
    data: data,
    method: 'post',
    showLoading: true,
  });
}

//导入
export function diversionImport(files) {
  return request({
    url: '/reports/diversionResponsiblePerson/diversionImport',
    method: 'post',
    data: files,
    showLoading: true
  });
}


