<!-- 图片或视频上传组件 -->
<template>
  <div style="height: auto">
    <el-upload
      action="#"
      ref="uploadRef"
      :multiple="multiple"
      :accept="fileType"
      :limit="multiple?99:1"
      list-type="picture-card"
      :file-list="fileList"
      :disabled="disabled"
      v-if="(fileArr&&fileArr.length>0)||!disabled"
      :class="multiple?(disabled?'hidden-upload':''):(((fileArr&&fileArr.length===1)||disabled)?'hidden-upload':'')"
      :before-upload="handleDetect"
      :http-request="handleUpload"
      :on-success="handleSuccess"
    >
      <el-tooltip
        class="box-item"
        effect="dark"
        :content="label + '格式（' + fileType + '）'" placement="right"
      >
        <i class="el-icon-plus"></i>
      </el-tooltip>
      <template #file="{ file }">
        <img
          alt=""
          v-if="uploadMode==='image'"
          class="el-upload-list__item-thumbnail"
          :src="file.url"
        />
        <img
          alt=""
          v-if="uploadMode==='video'"
          class="el-upload-list__item-thumbnail"
          :src="file.uploadFile?file.uploadFile.thumbnailPath:videoImg"
        />
        <span class="el-upload-list__item-actions">
          <span
            title="预览"
            v-if="uploadMode==='image'"
            class="el-upload-list__item-preview"
            @click="handleImagePreview(file)"
          >
           <i class="el-icon-zoom-in"></i>
          </span>
          <span
            v-if="uploadMode==='video'"
            title="播放"
            class="el-upload-list__item-preview"
            @click="handleVideoPlayer(file)"
          >
            <i class="el-icon-video-play"></i>
          </span>
          <span
            v-if="!disabled"
            title="删除"
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </template>
    </el-upload>

    <div v-else>
      {{ '暂无' + label }}
    </div>

    <!-- 预览图片 -->
    <el-image-viewer
      v-if="imageVisible"
      :url-list="[imageUrl]"
      :on-close="() => {imageVisible = false}"
    />

    <!-- 播放视频 -->
    <my-dialog
      title="播放视频"
      width="640px"
      v-model="videoVisible"
      :before-close="closeVideoDialog"
      append-to-body
      height="70vh"
    >
      <video
        ref="myVideo"
        controls="controls"
        :autoplay="false"
        style="width: 100%;height: 95%"
        :src="videoUrl">
      </video>
    </my-dialog>
  </div>
</template>

<script>
import {uploadFileList} from "@/api/system/common";
import videoImg from "@/assets/images/video.jpg";

export default {
  name: "MyPvUpload",
  components: {
    'el-image-viewer': () => import("element-ui/packages/image/src/image-viewer"),
  },
  props: {
    label: {
      type: String,
      default: '现场图片'
    },
    fileList: {
      type: Array,
      default: () => []
    },
    pathFieldName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    fileType: {
      type: String,
      default: '.bmp,.gif,.jpg,.jpeg,.png'
    },
    uploadMode: {
      type: String,
      default: 'image'
    }
  },
  data() {
    return {
      fileArr: [],
      imageVisible: false,
      imageUrl: '',
      videoVisible: false,
      videoUrl: '',
      videoImg: videoImg
    }
  },
  methods: {
    /** 文件上传前 验证格式 */
    handleDetect(file) {
      const detectType = file.name.substring(file.name.lastIndexOf(".") + 1, file.name.length).toLowerCase();
      if (this.fileType.indexOf(detectType) !== -1) {
        return true;
      } else {
        this.$modal.msgError("文件格式错误，请上传正确格式的文件！");
        return false;
      }
    },

    /** 文件上传中 */
    handleUpload(params) {
      const file = params.file;
      const formData = new FormData();
      formData.append('file', file);
      this.$modal.loading("正在上传文件，请稍候...");
      uploadFileList(formData, this.pathFieldName).then(res => {
        params.onSuccess(res);
        this.$modal.closeLoading();
      }).catch(error => {
        params.onError(error);
        this.$modal.closeLoading();
      });
    },

    /** 上传成功回调函数 */
    handleSuccess(res, file) {
      file.uploadFile = res.file;
      this.fileArr.push(file.uploadFile);
      this.$emit("getFileList", this.fileArr);
    },

    /** 删除文件操作 */
    handleRemove(file) {
      for (let index in this.fileArr) {
        if (file.uploadFile.id === this.fileArr[index].id) {
          this.fileArr.splice(index, 1);
          break;
        }
      }
      this.$refs.uploadRef.handleRemove(file);
      this.$emit("getFileList", this.fileArr);
    },

    /** 预览图片操作 */
    handleImagePreview(file) {
      this.imageVisible = true;
      this.imageUrl = file.url;
    },

    /** 播放视频操作 */
    handleVideoPlayer(file){
      this.videoVisible = true;
      this.videoUrl = file.uploadFile.uploadFilePath;
    },

    /** 关闭视频播放弹窗 */
    closeVideoDialog(done) {
      this.$refs.myVideo.pause();
      done();
    }
  },
  mounted() {
    //初始化回显fileList
    if (this.fileList && this.fileList.length > 0) {
      this.fileArr = this.fileList.map(item=> {
        return item.uploadFile;
      });
    } else {
      this.fileArr = [];
    }
  },
  watch: {
    fileList(nval, oval) {
      if (nval && nval.length > 0) {
        this.fileArr = this.fileList.map(item=> {
          return item.uploadFile;
        });
      } else {
        this.fileArr = [];
      }
    }
  }
}
</script>

<style scoped>
::v-deep .hidden-upload {
  height: 90px;
}
.el-image-viewer__wrapper {
  z-index: 999999 !important; /* 或者更高的值 */
}
::v-deep .hidden-upload .el-upload--picture-card {
  display: none;
}

::v-deep .el-upload--picture-card {
  width: 90px;
  height: 90px;
  line-height: 100px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 90px;
  height: 90px;
  line-height: 90px;
  margin: 0 6px -7px 0;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-thumbnail {
  width: 90px;
  height: 90px;
  line-height: 90px;
}
</style>
