<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['sys:signature:save']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sys:signature:update']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sys:signature:delete']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="reload"></right-toolbar>
    </el-row>

    <my-table url="/sys/signature/page" ref="signatureTable" row-key="id" @my-selection-change="handleSelectionChange">
  <el-table-column  label="id 主键" min-width="80" prop="id" sortable="custom" column-key="ID"></el-table-column>
  <el-table-column  label="创建人id" min-width="80" prop="createUserId" sortable="custom" column-key="CREATE_USER_ID"></el-table-column>
  <el-table-column  label="修改人id" min-width="80" prop="updateUserId" sortable="custom" column-key="UPDATE_USER_ID"></el-table-column>
  <el-table-column  label="创建时间" min-width="80" prop="createTime" sortable="custom" column-key="CREATE_TIME"></el-table-column>
  <el-table-column  label="修改时间" min-width="80" prop="updateTime" sortable="custom" column-key="UPDATE_TIME"></el-table-column>
  <el-table-column  label="业务表ID" min-width="80" prop="ownerId" sortable="custom" column-key="OWNER_ID"></el-table-column>
  <el-table-column  label="业务类型" min-width="80" prop="businessType" sortable="custom" column-key="BUSINESS_TYPE"></el-table-column>
  <el-table-column  label="签名图片base64码" min-width="80" prop="imgCode" sortable="custom" column-key="IMG_CODE"></el-table-column>
  <el-table-column  label="操作" column-key="caozuo" fixed="right" align="center">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="success"
        class="btn-table-operate"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['sys:signature:update']"
      ></el-button>
      <el-button
        size="mini"
        type="danger"
        class="btn-table-operate"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['sys:signature:delete']"
      ></el-button>
    </template>
  </el-table-column>
    </my-table>

    <!-- 添加或修改签名表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="signature"  label-width="80px">
              <my-form-item label="创建人id" ref="createUserId" prop="createUserId" :rules="[{notNull:true,message:'请输入创建人id'}]">
        <my-input v-model="signature.createUserId" placeholder="请输入创建人id" />
      </my-form-item>
              <my-form-item label="修改人id" ref="updateUserId" prop="updateUserId" :rules="[{notNull:true,message:'请输入修改人id'}]">
        <my-input v-model="signature.updateUserId" placeholder="请输入修改人id" />
      </my-form-item>
              <my-form-item label="创建时间" ref="createTime" prop="createTime" :rules="[{notNull:true,message:'请输入创建时间'}]">
        <my-input v-model="signature.createTime" placeholder="请输入创建时间" />
      </my-form-item>
              <my-form-item label="修改时间" ref="updateTime" prop="updateTime" :rules="[{notNull:true,message:'请输入修改时间'}]">
        <my-input v-model="signature.updateTime" placeholder="请输入修改时间" />
      </my-form-item>
              <my-form-item label="业务表ID" ref="ownerId" prop="ownerId" :rules="[{notNull:true,message:'请输入业务表ID'}]">
        <my-input v-model="signature.ownerId" placeholder="请输入业务表ID" />
      </my-form-item>
              <my-form-item label="业务类型" ref="businessType" prop="businessType" :rules="[{notNull:true,message:'请输入业务类型'}]">
        <my-input v-model="signature.businessType" placeholder="请输入业务类型" />
      </my-form-item>
              <my-form-item label="签名图片base64码" ref="imgCode" prop="imgCode" :rules="[{notNull:true,message:'请输入签名图片base64码'}]">
        <my-input v-model="signature.imgCode" placeholder="请输入签名图片base64码" />
      </my-form-item>
          </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSignature, delSignature, delSignatureBatch,addSignature, updateSignature } from "@/api/sys/signature";

export default {
  name: "Signature",
  data() {
    return {
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {},
      // 表单参数
      signature: {},
    };
  },
  mounted() {
  },
  methods: {
    /** 查询签名表列表 */
    reload(restart) {
      this.$refs.signatureTable.search(this.queryParams, restart);
      this.single=true;
      this.multiple = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.signature = {
        id: '',
        createUserId: '',
        updateUserId: '',
        createTime: '',
        updateTime: '',
        ownerId: '',
        businessType: '',
        imgCode: ''
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.reload(true);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = this.$refs.signatureTable.getSelectRowKeys()
      this.single = this.ids.length!=1
      this.multiple = !this.ids.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加签名表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getSignature(id).then(r => {
        this.signature = r.signature;
        this.open = true;
        this.title = "修改签名表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, errorObj) => {
        if (valid) {
          if (this.signature.id) {
            updateSignature(this.signature).then(r => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.reload();
            });
          } else {
            addSignature(this.signature).then(r => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.reload();
            });
          }
        }else{
          this.$scrollView(errorObj);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除选中的数据吗？').then(function() {
         if(row.id) {
          return delSignature(row.id);
        }else{
          return delSignatureBatch(this.ids);
        }
      }).then(() => {
        this.reload();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  },
  activated() {
    //组件被激活时重绘表格
    this.$refs.signatureTable.changeTableHeight();
  },
};
</script>
