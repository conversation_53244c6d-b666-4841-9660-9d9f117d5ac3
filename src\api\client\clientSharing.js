import request from '@/utils/request'

// 查询客户端共享信息表详细
export function getClientSharing(id) {
  return request({
    url: '/client/clientSharing/info/' + id,
    method: 'post'
  })
}

// 新增客户端共享信息表
export function addClientSharing(data) {
  return request({
    url: '/client/clientSharing/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改客户端共享信息表
export function updateClientSharing(data) {
  return request({
    url: '/client/clientSharing/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除客户端共享信息表
export function delClientSharing(id) {
  return request({
    url: '/client/clientSharing/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除客户端共享信息表
export function delClientSharingBatch(ids) {
  return request({
    url: '/client/clientSharing/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

//获取项目列表
export function getProjectName(clientId,projectId) {
  return request({
    url: '/client/clientSharing/selectProjectList/'+clientId+'/'+projectId,
    method: 'post',
  });
}


