import request from '@/utils/request'

// 查询采砂监管责任人填报表详细
export function getResponsiblePersionReport(id) {
  return request({
    url: '/reports/responsiblePersionReport/info/' + id,
    method: 'post'
  })
}

// 新增采砂监管责任人填报表
export function addResponsiblePersionReport(data) {
  return request({
    url: '/reports/responsiblePersionReport/save',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 修改采砂监管责任人填报表
export function updateResponsiblePersionReport(data) {
  return request({
    url: '/reports/responsiblePersionReport/update',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

// 删除采砂监管责任人填报表
export function delResponsiblePersionReport(id) {
  return request({
    url: '/reports/responsiblePersionReport/delete/' + id,
    method: 'post',
    showLoading: true,
  });
}

// 批量删除采砂监管责任人填报表
export function delResponsiblePersionReportBatch(ids) {
  return request({
    url: '/reports/responsiblePersionReport/delete',
    data: ids,
    method: 'post',
    showLoading: true,
  });
}

// 导入采砂监管责任人
export function importResponsiblePersionReport(data,reportId,version) {
  return request({
    url: '/reports/responsiblePersion/csResponsibleImport/'+reportId+'/'+version,
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//县级审核
export function checkPassXian(data) {
  return request({
    url: '/reports/responsiblePersionReport/xianExamine',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//市级审核
export function checkPassShi(data) {
  return request({
    url: '/reports/responsiblePersionReport/shiExamine',
    method: 'post',
    data: data,
    showLoading: true,
  })
}

//消息提醒
export function getMessage() {
  return request({
    url: '/reports/responsiblePersionReport/messageReminder',
    method: 'post',
  })
}

//打回
export function responsibleBack(data) {
  return request({
    url: '/reports/responsiblePersionReport/shengBack',
    method: 'post',
    data: data,
    showLoading: true,
  })
}



