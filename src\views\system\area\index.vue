<template>
  <div v-cloak>
    <el-row :gutter="10" style="margin: 10px">
      <el-col :sm="6">
        <el-card>
          <div slot="header">区域导航</div>
          <div style="overflow: auto;height: 85%">
            <el-tree
              ref="leftTree"
              :data="areaList"
              :props="treeProps"
              node-key="id"
              :default-expanded-keys="expandedKeys"
              @node-click="handleLefTreeClick"
            ></el-tree>
          </div>
        </el-card>
      </el-col>
      <el-col :sm="18">
        <el-card>
          <div slot="header">{{ title }}</div>
          <div style="overflow: auto;height: 85%;width: 550px;">
            <el-form ref="form" :model="area" label-width="95px">
              <el-form-item v-if="area.areaCode !== '000'" label="上级区域">
                <el-input
                  readonly="readonly"
                  @click.native="editAble ? areaTree():''"
                  v-model="area.parentName"
                  placeholder="中国"
                ></el-input>
              </el-form-item>
              <el-form-item label="区域编码" prop="areaCode" v-show="!editAble">
                <el-input :readonly="!editAble" v-model="area.areaCode" placeholder="区域编码"></el-input>
              </el-form-item>
              <my-form-item label="区域编码" prop="selfCode" v-show="editAble" :rules="
               [{ required: true, message: '区域编码不能为空', trigger: 'blur' },
                { min: 3, max: 3, message: '子编码长度需为3位', trigger: 'blur' },
                { isNumber: true, message:'只能是数字'},
                { fn: this.checkCode, message: '区域编码重复', trigger: 'blur' }]">
                <el-input :readonly="!editAble" v-model="area.selfCode" placeholder="区域编码" @change="calcAreaCode">
                  <template v-if="editAble?prepend='prepend':prepend=''" :slot="prepend">{{ area.parentCode }}_</template>
                </el-input>
              </my-form-item>
              <my-form-item label="区域名称" prop="areaName" :rules="[{ required: true, message: '区域名称不能为空', trigger: 'blur' }]">
                <el-input
                  maxlength="100"
                  :readonly="!editAble"
                  v-model="area.areaName"
                  placeholder="区域名称"
                ></el-input>
              </my-form-item>
              <el-form-item label="区域全称" v-show="!editAble">
                <el-input
                  readonly
                  v-model="area.areaFullName"
                  placeholder="区域全称"
                ></el-input>
              </el-form-item>
              <el-form-item label="经纬度(百度)">
                <el-col :sm="spacing" style="padding-left: 0;padding-right: 0;" :style="editAble?'margin-right: 10px':''">
                  <my-form-item prop="jingdu" :rules="[{ isNumber: true, message: '经度只能是数字', trigger: 'blur' }]">
                    <el-input
                      v-model="area.jingdu"
                      placeholder="经度"
                      maxlength="20"
                      :readonly="!editAble"
                    ></el-input>
                  </my-form-item>
                </el-col>
                <el-col :sm="spacing" style="padding-left: 0;padding-right: 0;" :style="editAble?'margin-right: 10px':''">
                  <my-form-item prop="weidu" :rules="[{ isNumber: true, message: '纬度只能是数字', trigger: 'blur' }]">
                    <el-input
                      v-model="area.weidu"
                      placeholder="纬度"
                      maxlength="20"
                      :readonly="!editAble"
                    ></el-input>
                  </my-form-item>
                </el-col>
                <el-col :sm="2" v-if="editAble" style="padding-left: 0;padding-right: 0">
                  <el-button
                    type="primary"
                    icon="el-icon-map-location"
                    @click="showMap"
                    title="选择坐标"
                  ></el-button>
                </el-col>
              </el-form-item>
              <el-form-item v-if="!editAble">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="add"
                    v-hasPermi="['sys:area:save']"
                  >新增
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    @click="update"
                    v-show="area.areaCode !== '000'"
                    v-hasPermi="['sys:area:update']"
                  >修改
                  </el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="delOne"
                    v-show="area.areaCode !== '000'"
                    v-hasPermi="['sys:area:delete']"
                  >删除
                  </el-button>
                </el-col>
              </el-form-item>
              <el-form-item v-else>
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-success"
                    size="mini"
                    @click="saveOrUpdate"
                  >保存</el-button>
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="default"
                    icon="el-icon-refresh-left"
                    size="mini"
                    @click="reload"
                  >返回</el-button>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 选择上级区域 -->
    <my-dialog title="选择上级区域" v-el-drag-dialog :visible.sync="openSelectArea" width="300px" append-to-body>
      <el-tree
        ref="selectAreaTree"
        :data="areaList2"
        :props="treeProps"
        node-key="id"
        :default-expanded-keys="expandedKeys"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaSelect">确 定</el-button>
        <el-button @click="cancelAreaSelect">取 消</el-button>
      </div>
    </my-dialog>
    <!-- 获取经纬度 -->
    <my-dialog title="选择坐标" v-el-drag-dialog :visible.sync="openAreaMap" width="1200px" append-to-body>
      <BMap ref="BMap" v-if="openAreaMap" :baiduMapAk="baiduMapAk" :default-center="{lng:area.jingdu,lat:area.weidu}"></BMap>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitLocation">确 定</el-button>
        <el-button @click="cancelLocation">取 消</el-button>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import { getAreaTreeData, addArea, updateArea, delArea, checkAreaCode } from '@/api/system/area'
import { handleTree } from '@/utils/ruoyi'
import elDragDialog from '@/directive/dialog/drag'
import configUtils from '@/utils/configUtils'
import BMap from './map'
export default {
  name: 'Area',
  directives: {
    elDragDialog  //拖拽弹窗
  },
  components: {
    BMap
  },
  data() {
    return {
      baiduMapAk: '',
      title: '区域信息',
      editAble: false,
      openSelectArea: false,
      openAreaMap: false,
      prepend: '',
      area: {
        id: '000',
        parentCode: '',
        parentName: '中国',
        areaCode: '000',
        areaName: '中国',
        areaFullName: '中国',
        jingdu: '',
        weidu: ''
      },
      selectArea: {},
      oldAreaCode: '',
      areaList: [],
      areaList2: [],
      expandedKeys: [],
      treeProps: {
        children: 'children',
        label: 'areaName'
      },
      spacing: 12,
    }
  },
  methods: {
    //区域导航-区域树点击事件
    handleLefTreeClick(data, treeNode, nodeObj) {
      this.spacing = 12
      this.editAble = false
      this.title = '区域信息'
      this.$refs['form'].clearValidate()
      this.area = JSON.parse(JSON.stringify(data))
      this.oldAreaCode = data.areaCode
      var parentNode = data.parentNode
      if (parentNode != null && typeof (parentNode) != 'undefined') {
        this.area.parentName = parentNode.areaName
      }
      this.area.areaFullName = this.getFullName(data);
      //打开后默认展开树节点
      this.expandedKeys = [this.area.id]
    },
    //递归获取区域全名
    getFullName(node) {
      if (node) {
        return this.getFullName(node.parentNode) + node.areaName;
      }else{
        return "";
      }
    },
    //加载区域树
    getAreaTreeLeft() {
      getAreaTreeData().then(r => {
        this.areaList = handleTree(r.areaList, 'areaCode', 'parentCode')
        this.areaList2 = JSON.parse(JSON.stringify(this.areaList))
        var that = this
        this.$nextTick(() => {
          if (that.area.id) {
            that.$refs.leftTree.setCurrentKey(that.area.id)
            let node = that.$refs.leftTree.getNode(that.area.id)
            if (node) {
              that.handleLefTreeClick(node.data, node)
            }
          }
        })
      })
    },
    //新增操作
    add() {
      this.spacing = 10
      this.editAble = true
      this.title = '区域信息-新增'
      this.selectArea = JSON.parse(JSON.stringify(this.area))
      this.area = {
        parentName: this.area.areaName,
        parentId: this.area.id,
        parentCode: this.area.areaCode,
        selfCode: '',
        areaLevel: this.area.areaLevel + 1
      }
    },
    //修改操作
    update() {
      this.spacing = 10
      this.editAble = true
      this.title = '区域信息-修改'
      this.selectArea = JSON.parse(JSON.stringify(this.area))
    },
    //删除操作
    delOne() {
      let node = this.$refs.leftTree.getNode(this.area.id)
      console.log('node', node)
      if (node.data.parentId === '-1') {
        return this.$modal.msgWarning('不允许删除最上级区域')
      }
      if (node.data) {
        this.selectArea = this.$refs.leftTree.getNode(node.data.parentNode.id).data
        let tip = this.area.children ? '确定要删除选中的记录及其所有子区域吗？' : '确定要删除选中的记录吗？'
        this.$modal.confirm(tip).then(() => {
          this.$modal.loading('加载中')
          delArea(this.area.id).then(() => {
            this.$modal.msgSuccess('操作成功')
            this.area.areaCode = this.selectArea.parentCode
            this.reload()
            this.$modal.closeLoading()
          }).catch(this.$modal.closeLoading)
        }).catch(() => {})  //捕获异常错误
      }
    },
    //保存操作
    saveOrUpdate() {
      let that = this
      this.$refs['form'].validate(valid => {
        if (valid) {
          that.area.areaCode = that.area.parentCode + '_' + that.area.selfCode
          if (!that.area.id) {
            that.$modal.loading('加载中')
            addArea(that.area).then(() => {
              that.$modal.msgSuccess('操作成功')
              that.area.areaCode = that.selectArea.parentCode
              that.reload()
              that.$modal.closeLoading()
            }).catch(that.$modal.closeLoading)
          } else {
            that.$modal.loading('加载中')
            updateArea(that.area).then(() => {
              that.$modal.msgSuccess('操作成功')
              that.reload()
              that.$modal.closeLoading()
            }).catch(that.$modal.closeLoading)
          }
        }
      })
    },
    //返回操作
    reload() {
      this.area = JSON.parse(JSON.stringify(this.selectArea))
      this.$refs['form'].clearValidate()
      this.getAreaTreeLeft()
      this.spacing = 12
      this.editAble = false
      this.title = '区域信息'
    },
    //上级区域弹窗
    areaTree() {
      this.openSelectArea = true
      this.$nextTick(function() {
        this.$refs.selectAreaTree.setCurrentKey(this.area.id)
      })
    },
    //选择上级区域弹窗-确定操作
    submitAreaSelect() {
      let currentNode = this.$refs.selectAreaTree.getCurrentNode()
      if (currentNode.areaCode === this.oldAreaCode) {
        this.$modal.msgWarning('不能选择自己为上级')
      } else if (currentNode.areaCode.startsWith(this.oldAreaCode)) {
        this.$modal.msgWarning('不能选择下级为上级')
      } else {
        this.area.parentCode = currentNode.areaCode
        this.area.parentName = currentNode.areaName
        this.area.areaLevel = currentNode.areaLevel + 1
        this.calcAreaCode()
        this.openSelectArea = false
      }
    },
    //选择上级区域弹窗-取消操作
    cancelAreaSelect() {
      this.openSelectArea = false
    },
    //拼接区域编码
    calcAreaCode: function() {
      this.area.areaCode = this.area.parentCode + '_' + this.area.selfCode
    },
    //区域编码是否重复
    checkCode() {
      var that = this
      return new Promise(function(resolve, reject) {
        checkAreaCode(that.area).then(r => {
          if (r.valid) {
            resolve()
          } else {
            reject()
          }
        }).catch(reject)
      })
    },
    //地图弹窗
    showMap() {
      this.openAreaMap = true
    },
    //选取经纬度
    submitLocation() {
      this.area.jingdu = this.$refs.BMap.markerPoint.lng
      this.area.weidu = this.$refs.BMap.markerPoint.lat
      this.openAreaMap = false
    },
    //关闭地图弹窗
    cancelLocation() {
      this.openAreaMap = false
    }
  },
  mounted() {
    this.getAreaTreeLeft()
    //调用系统配置类
    configUtils.getConfigValue('bmap.ak').then(r => {
      this.baiduMapAk = r
    })
  }
}
</script>
