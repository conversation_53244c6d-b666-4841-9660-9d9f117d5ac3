<template>
  <div class="content">
    <el-cascader
      clearable
      filterable
      v-model="innerValue"
      style="width: 100%"
      :disabled="disabled"
      :show-all-levels="false"
      :options="deptOptions"
      :placeholder="placeholder"
      :props="{
        value: 'deptId',
        label: 'name',
        children: 'children',
        checkStrictly: true
      }"
      @change="dispatchEvent"
    />
  </div>
</template>

<script>
import {getDeptTreeData} from "@/api/system/dept";
import {handleTree} from "@/utils/ruoyi";

export default {
  name: "MyCascader",
  data() {
    return {
      // 部门树结构
      deptOptions: [],
      // 部门列表
      deptList: [],
      innerValue: [],
      strValue: ''
    }
  },
  props: {
    value: {
      default: '',
      type: String
    },
    disabled: {
      default: false,
      type: Boolean
    },
    placeholder: {
      default: '请选择',
      type: String
    },
    checkStrictly: {
      default: false,
      type: Boolean
    }
  },
  created() {
    this.getDeptTreeselect();
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTreeselect() {
      getDeptTreeData().then(res => {
        this.deptList = res.deptList;
        this.deptOptions = handleTree(res.deptList, 'deptId', 'parentId');
        //由于异步接口 方便回显数据
        if (this.value) {
          this.buildStructure(this.value);
        } else {
          this.innerValue = [];
        }
      });
    },
    /** 抛出值 */
    dispatchEvent() {
      this.strValue = this.innerValue.length > 0 ? this.innerValue[this.innerValue.length - 1] : '';
      this.$emit("input", this.strValue);
    },
    /** 递归查询父级 */
    findParentId(obj, arr) {
      if (!obj || !obj.parentId || obj.parentId === '-1') {
        return arr;
      }
      let result=null;
      this.deptList.forEach(item => {
        if (item.deptId === obj.parentId) {
          result = item;
          arr.unshift(item.deptId);
        }
      });
      return this.findParentId(result, arr);
    },
    /** 构造级联选择器绑定的数据结构用于回显数据 */
    buildStructure(value) {
      const obj = this.deptList.find(item => item.deptId === value);
      const result = this.findParentId(obj, []);
      result.push(value);
      this.innerValue = result;
    }
  },
  watch: {
    value(nval, oval) {
      if (nval) {
        this.buildStructure(nval);
      } else {
        this.innerValue = [];
      }
    }
  }
}
</script>

<style scoped>
.content {
  width: 100%;
}
</style>
