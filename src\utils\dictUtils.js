import cache from "@/plugins/cache"
import {projectCode} from "@/settings"
import {subList} from "@/api/system/dict";

/**
 * 字典工具类，走浏览器缓存
 */

const prefix = "dict";

/**
 * 请求锁防止同一个字典值重复请求
 */
let dictLock = false;

/**
 * <li>功能描述：通过pvalue 获取子项字典的map，优先从本地缓存获取</li>
 * @author: <EMAIL>
 * @date: 2021/1/25 9:56
 */
function cList(pValues) {
  //通过这个锁，防止列表中使用同一个字典时重复请求的情况
  if (dictLock) {
    return new Promise((resolve, reject)=>{
      setTimeout(function () {
        cList(pValues).then(resolve).catch(reject);
      },10);
    })
  }
  dictLock = true;
  let localVersion = cache.local.get("webSite.innerVersion");
  let keyPrefix = projectCode + "." + prefix + "_" + localVersion + ".";
   return new Promise((resolve, reject)=>{
     let result = {};
     let pValueArr = new Array();
     if (typeof (pValues) == 'array') {
       pValueArr = pValues;
     } else if (pValues.indexOf(",") != -1) {
       pValueArr = pValues.split(",");
     } else {
       pValueArr.push(pValues);
     }
     var notExistsPvalue = "";

     pValueArr.map(function (pValue) {
       var subList = cache.session.get(keyPrefix + pValue);
       if (subList) {
         result[pValue] = JSON.parse(subList);
       } else {
         notExistsPvalue += pValue + ",";
       }
     });
     if (notExistsPvalue.length > 0) {
       notExistsPvalue = notExistsPvalue.substring(0, notExistsPvalue.length - 1)
       subList(notExistsPvalue).then(r => {
         for (var key in r) {
           if (key == 'code' || key == 'msg') {
             continue;
           }
           cache.session.set(keyPrefix + key, JSON.stringify(r[key]));
           result[key] = r[key];
         }
         resolve(result)
         dictLock = false;
       }).catch(function () {
         reject();
         dictLock = false;
       });
     }else{
       resolve(result);
       dictLock=false;
     }
   });
}

/**
 *<li>功能描述：清空某个字典的值</li>
 * @author: <EMAIL>
 * @date:  2022/10/27 13:45
 */
function clearCache(pValue) {
  let localVersion = cache.local.get("webSite.innerVersion");
  cache.session.remove(projectCode + "." + prefix + "_" + localVersion + "." + pValue);
}

export default {
  cList,clearCache
}
